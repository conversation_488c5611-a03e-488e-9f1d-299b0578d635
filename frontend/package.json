{"name": "biography-generator-frontend", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.0.35", "@types/react-dom": "^18.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "axios": "^1.4.0", "react-query": "^3.39.3", "@mui/material": "^5.12.3", "@mui/icons-material": "^5.12.3", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.3.1", "react-dropzone": "^14.2.3", "notistack": "^3.0.1"}, "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}