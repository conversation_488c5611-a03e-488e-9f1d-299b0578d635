# Biography Generator Frontend

## Real-time Status Updates

This frontend now uses **WebSockets** for real-time job status updates instead of polling or SSE.

### Key Features

- ✅ **Zero Polling**: No constant HTTP requests
- 📡 **Real-time Updates**: Instant status changes via WebSocket
- 🔄 **Auto-reconnect**: <PERSON><PERSON><PERSON> handles connection issues
- 🛠 **Simple Implementation**: Easy to use and maintain
- 🚀 **Better Performance**: More efficient than SSE, no server-side timeouts

### Usage

#### Basic API Usage

```javascript
import { biographyA<PERSON> } from './services/api';

// Subscribe to job status updates via WebSocket
const webSocket = biographyApi.subscribeToJobStatus(
    jobId,
    (updatedJob) => {
        console.log('Job updated:', updatedJob);
        // Update UI with new status
    },
    (error) => {
        console.error('Connection error:', error);
    },
    () => {
        console.log('Job completed');
    }
);

// Manual cleanup (optional - auto-closes when job completes)
webSocket.close();

// Close specific job connection
biographyApi.closeJobConnection(jobId);

// Close all connections (useful for app cleanup)
biographyApi.closeAllConnections();
```

#### React Hook Usage

```javascript
import { useJobStatusWebSocket } from './utils/useJobStatusSSE';

const MyComponent = () => {
    const [job, setJob] = useState(null);
    
    const { closeConnection } = useJobStatusWebSocket({
        jobId: job?.id,
        onUpdate: (updatedJob) => {
            setJob(updatedJob);
        },
        onComplete: (completedJob) => {
            console.log('Job finished:', completedJob);
        },
        enabled: job?.status === 'processing'
    });

    return (
        <div>
            <p>Status: {job?.status}</p>
            <p>Progress: {job?.progress_percentage}%</p>
        </div>
    );
};
```

### New Features

#### Reset Failed Jobs

Failed jobs can now be reset to try again:

```javascript
await biographyApi.resetJob(jobId);
```

#### Enhanced Job Actions

- **Start**: Begin processing for pending jobs
- **Reset**: Reset failed jobs to pending
- **Download**: Download completed biographies
- **View**: View job details and content
- **Delete**: Remove jobs and files

### Components Updated

- **JobList**: Real-time status updates via WebSocket
- **UploadForm**: File validation and upload with progress
- **App**: Central WebSocket connection management

### WebSocket vs SSE Improvements

**WebSocket Benefits:**
- ✅ Bidirectional communication
- ✅ Lower latency
- ✅ Better browser support
- ✅ No server-side timeouts
- ✅ More efficient protocol
- ✅ Connection multiplexing

**Previous SSE Issues Resolved:**
- ❌ Server-side timeout every few minutes
- ❌ High request volume (1 per second per job)
- ❌ Browser connection limits
- ❌ Unidirectional communication only

## Development

```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build
```

## Environment Variables

```env
REACT_APP_API_URL=http://localhost:8000
``` 