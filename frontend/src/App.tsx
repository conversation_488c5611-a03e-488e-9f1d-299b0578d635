import React, { useState, useEffect } from 'react';
import {
    Con<PERSON><PERSON>,
    AppB<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>pography,
    <PERSON>,
    Tabs,
    Tab,
    ThemeProvider,
    createTheme,
    CssBaseline,
    Alert,
    Snackbar,
} from '@mui/material';
import { QueryClient, QueryClientProvider } from 'react-query';
import FileUpload from './components/FileUpload';
import JobList from './components/JobList';
import PromptManager from './components/PromptManager';
import SettingsContainer from './components/SettingsContainer';
import { BiographyJob, JobStatus } from './types';
import { biographyApi, promptsApi } from './services/api';

const queryClient = new QueryClient();

const theme = createTheme({
    palette: {
        primary: {
            main: '#1976d2',
        },
        secondary: {
            main: '#dc004e',
        },
    },
});

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function TabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
}

function App() {
    const [tabValue, setTabValue] = useState(0);
    const [jobs, setJobs] = useState<BiographyJob[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');

    // JobProgressBar components now handle their own WebSocket connections
    // No need for complex connection management here

    // Load jobs on component mount
    useEffect(() => {
        const initializeDefaultPrompts = async () => {
            try {
                await promptsApi.initializeDefaults();
                showMessage('Default prompts initialized!');
            } catch (err: any) {
                console.error('Failed to initialize prompts:', err);
            }
        };

        loadJobs();
        initializeDefaultPrompts();
    }, []);

    // Auto-refresh jobs every 30 seconds (reduced from previous polling)
    useEffect(() => {
        if (!jobs.length) return; // Don't set interval if no jobs

        const interval = setInterval(async () => {
            try {
                console.log('🔄 App: Refreshing jobs list to remove stale entries...');
                const updatedJobs = await biographyApi.getJobs();

                // Only update if there are actual changes to prevent unnecessary re-renders
                const currentJobIds = new Set(jobs.map(j => j.id));
                const newJobIds = new Set(updatedJobs.map(j => j.id));

                const hasChanges =
                    currentJobIds.size !== newJobIds.size ||
                    Array.from(currentJobIds).some(id => !newJobIds.has(id)) ||
                    Array.from(newJobIds).some(id => !currentJobIds.has(id));

                if (hasChanges) {
                    console.log('📝 App: Jobs list changed, updating UI');
                    console.log(`   Removed: ${Array.from(currentJobIds).filter(id => !newJobIds.has(id)).length} jobs`);
                    console.log(`   Added: ${Array.from(newJobIds).filter(id => !currentJobIds.has(id)).length} jobs`);
                    setJobs(updatedJobs);
                } else {
                    console.log('✅ App: No changes in jobs list');
                }
            } catch (error) {
                console.error('❌ App: Error refreshing jobs:', error);
            }
        }, 30000); // 30 seconds

        return () => {
            console.log('🧹 App: Clearing jobs refresh interval');
            clearInterval(interval);
        };
    }, [jobs]); // Depend on jobs to recreate interval when jobs change

    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    const showMessage = (message: string) => {
        setSnackbarMessage(message);
        setSnackbarOpen(true);
    };

    const loadJobs = async () => {
        setError(null);

        try {
            const jobsData = await biographyApi.getJobs();
            console.log('API response for getJobs:', jobsData);
            console.log('Jobs array length:', jobsData?.length);

            // Validate each job has required fields
            if (Array.isArray(jobsData)) {
                jobsData.forEach((job, index) => {
                    console.log(`Job ${index}:`, {
                        id: job.id,
                        idType: typeof job.id,
                        user_name: job.user_name,
                        status: job.status
                    });
                });

                // Set jobs - JobProgressBar components will handle their own WebSocket subscriptions
                setJobs(jobsData);

                console.log(`📊 Loaded ${jobsData.length} jobs - JobProgressBar components will handle their own updates`);

            } else {
                console.error('Jobs data is not an array:', jobsData);
                setJobs([]); // Set empty array as fallback
            }
        } catch (err: any) {
            console.error('Error loading jobs:', err);
            setError(err.response?.data?.detail || 'Failed to load jobs');
            setJobs([]); // Ensure jobs is always an array even on error
        }
    };

    const handleUploadSuccess = (job: BiographyJob) => {
        setJobs(prev => [job, ...prev]);
        showMessage('PDF uploaded successfully!');
    };

    const handleJobStart = async (jobId: string) => {
        try {
            console.log(`🚀 Starting job ${jobId}`);
            await biographyApi.startProcessing(jobId);
            showMessage('Biography generation started!');

            // Immediately update job status in UI to show it's processing
            console.log(`📊 Job ${jobId} started - updating UI status`);
            setJobs(prevJobs =>
                prevJobs.map(job =>
                    job.id === jobId
                        ? { ...job, status: JobStatus.PROCESSING, progress_percentage: 0 }
                        : job
                )
            );

            // Also refresh the specific job from server to get accurate status
            try {
                const updatedJob = await biographyApi.getJob(jobId);
                console.log(`🔄 Refreshed job ${jobId} from server:`, updatedJob.status);
                setJobs(prevJobs =>
                    prevJobs.map(job =>
                        job.id === jobId ? updatedJob : job
                    )
                );
            } catch (refreshErr) {
                console.warn(`⚠️ Could not refresh job ${jobId} from server:`, refreshErr);
                // Keep the optimistic update if server refresh fails
            }

            console.log(`✅ Job ${jobId} UI updated - WebSocket will handle further updates`);

        } catch (err: any) {
            console.error(`❌ Error starting job ${jobId}:`, err);
            setError(err.response?.data?.detail || 'Failed to start processing');
        }
    };

    const handleJobStartEnhancedIterative = async (jobId: string) => {
        try {
            console.log(`🚀 Starting Enhanced Iterative job ${jobId} (SQS)`);
            const result = await biographyApi.startEnhancedIterativeProcessing(jobId);
            showMessage(`Enhanced Iterative processing started! ${result.features.join(', ')}`);

            // Immediately update job status in UI to show it's processing
            console.log(`📊 Enhanced Iterative job ${jobId} started - updating UI status`);
            setJobs(prevJobs =>
                prevJobs.map(job =>
                    job.id === jobId
                        ? { ...job, status: JobStatus.PROCESSING, progress_percentage: 0 }
                        : job
                )
            );

            // Also refresh the specific job from server to get accurate status
            try {
                const updatedJob = await biographyApi.getJob(jobId);
                console.log(`🔄 Refreshed Enhanced Iterative job ${jobId} from server:`, updatedJob.status);
                setJobs(prevJobs =>
                    prevJobs.map(job =>
                        job.id === jobId ? updatedJob : job
                    )
                );
            } catch (refreshErr) {
                console.warn(`⚠️ Could not refresh Enhanced Iterative job ${jobId} from server:`, refreshErr);
                // Keep the optimistic update if server refresh fails
            }

            console.log(`✅ Enhanced Iterative job ${jobId} UI updated - WebSocket will handle further updates`);

        } catch (err: any) {
            console.error(`❌ Error starting Enhanced Iterative job ${jobId}:`, err);
            setError(err.response?.data?.detail || 'Failed to start Enhanced Iterative processing');
        }
    };

    const handleJobStartEnhancedIterativeDirect = async (jobId: string) => {
        try {
            console.log(`🚀 Starting Enhanced Iterative Direct job ${jobId}`);
            const result = await biographyApi.startEnhancedIterativeDirectProcessing(jobId);
            showMessage(`Enhanced Iterative Direct processing started! Processing type: ${result.processing_type}`);

            // Immediately update job status in UI to show it's processing
            console.log(`📊 Enhanced Iterative Direct job ${jobId} started - updating UI status`);
            setJobs(prevJobs =>
                prevJobs.map(job =>
                    job.id === jobId
                        ? { ...job, status: JobStatus.PROCESSING, progress_percentage: 0 }
                        : job
                )
            );

            // Also refresh the specific job from server to get accurate status
            try {
                const updatedJob = await biographyApi.getJob(jobId);
                console.log(`🔄 Refreshed Enhanced Iterative Direct job ${jobId} from server:`, updatedJob.status);
                setJobs(prevJobs =>
                    prevJobs.map(job =>
                        job.id === jobId ? updatedJob : job
                    )
                );
            } catch (refreshErr) {
                console.warn(`⚠️ Could not refresh Enhanced Iterative Direct job ${jobId} from server:`, refreshErr);
                // Keep the optimistic update if server refresh fails
            }

            console.log(`✅ Enhanced Iterative Direct job ${jobId} UI updated - WebSocket will handle further updates`);

        } catch (err: any) {
            console.error(`❌ Error starting Enhanced Iterative Direct job ${jobId}:`, err);
            setError(err.response?.data?.detail || 'Failed to start Enhanced Iterative Direct processing');
        }
    };

    const handleJobReset = async (jobId: string) => {
        try {
            await biographyApi.resetJob(jobId);
            showMessage('Job reset successfully!');
            // Refresh jobs to show updated status
            loadJobs();
        } catch (err: any) {
            setError(err.response?.data?.detail || 'Failed to reset job');
        }
    };

    const handleJobDelete = (jobId?: string) => {
        // Refresh jobs list
        loadJobs();
    };

    // Debug: track jobs changes
    useEffect(() => {
        console.log(`📈 Jobs state changed:`, jobs.map(j => ({ id: j.id, status: j.status, progress: j.progress_percentage })));
    }, [jobs]);

    // Set jobs - JobProgressBar components will handle their own WebSocket subscriptions
    const handleSetJobs = (newJobs: BiographyJob[]) => {
        console.log(`📝 App: Setting ${newJobs.length} jobs`);
        setJobs(newJobs);
    };

    return (
        <QueryClientProvider client={queryClient}>
            <ThemeProvider theme={theme}>
                <CssBaseline />
                <Box sx={{ flexGrow: 1 }}>
                    <AppBar position="static">
                        <Toolbar>
                            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                Personal Biography Generator
                            </Typography>
                        </Toolbar>
                    </AppBar>

                    <Container maxWidth="xl" sx={{ mt: 4 }}>
                        {error && (
                            <Alert severity="error" sx={{ mb: 3 }}>
                                {error}
                            </Alert>
                        )}

                        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                            <Tabs value={tabValue} onChange={handleTabChange}>
                                <Tab label="Upload & Generate" />
                                <Tab label="Jobs History" />
                                <Tab label="AI Prompts" />
                                <Tab label="Settings" />
                            </Tabs>
                        </Box>

                        <TabPanel value={tabValue} index={0}>
                            <FileUpload onUploadSuccess={handleUploadSuccess} />
                            <JobList
                                jobs={jobs}
                                onJobUpdate={handleJobDelete}
                                onJobStart={handleJobStart}
                                onJobStartEnhancedIterative={handleJobStartEnhancedIterative}
                                onJobStartEnhancedIterativeDirect={handleJobStartEnhancedIterativeDirect}
                                onJobReset={handleJobReset}
                            />
                        </TabPanel>

                        <TabPanel value={tabValue} index={1}>
                            <JobList
                                jobs={jobs}
                                onJobUpdate={handleJobDelete}
                                onJobStart={handleJobStart}
                                onJobStartEnhancedIterative={handleJobStartEnhancedIterative}
                                onJobStartEnhancedIterativeDirect={handleJobStartEnhancedIterativeDirect}
                                onJobReset={handleJobReset}
                            />
                        </TabPanel>

                        <TabPanel value={tabValue} index={2}>
                            <PromptManager />
                        </TabPanel>

                        <TabPanel value={tabValue} index={3}>
                            <SettingsContainer />
                        </TabPanel>
                    </Container>

                    <Snackbar
                        open={snackbarOpen}
                        autoHideDuration={6000}
                        onClose={() => setSnackbarOpen(false)}
                        message={snackbarMessage}
                    />
                </Box>
            </ThemeProvider>
        </QueryClientProvider>
    );
}

export default App; 