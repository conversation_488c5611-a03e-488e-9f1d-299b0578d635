import { useEffect, useRef, useState, useCallback } from 'react';
import { BiographyJob, JobStatus } from '../types';
import { biographyApi } from '../services/api';

interface UseJobWebSocketOptions {
    jobId: string;
    enabled?: boolean; // Control when to connect
    autoReconnect?: boolean; // Auto reconnect on connection failure
    onComplete?: () => void;
    onError?: (error: Event) => void;
}

interface UseJobWebSocketReturn {
    job: BiographyJob | null;
    isConnected: boolean;
    isConnecting: boolean;
    error: string | null;
    reconnect: () => void;
    disconnect: () => void;
}

/**
 * Custom hook for managing WebSocket connection to job status updates
 * 
 * @param initialJob - Initial job data
 * @param options - Configuration options
 * @returns Hook state and control functions
 */
export const useJobWebSocket = (
    initialJob: BiographyJob,
    options: UseJobWebSocketOptions
): UseJobWebSocketReturn => {
    const { jobId, enabled = true, autoReconnect = false, onComplete, onError } = options;

    // State
    const [job, setJob] = useState<BiographyJob | null>(initialJob);
    const [isConnected, setIsConnected] = useState(false);
    const [isConnecting, setIsConnecting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Refs
    const wsRef = useRef<WebSocket | null>(null);
    const mountedRef = useRef(true);
    const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Check if job needs WebSocket updates 
    const needsUpdates = useCallback((currentJob: BiographyJob): boolean => {
        return [
            JobStatus.PROCESSING,
            JobStatus.ITERATIVE_WRITING,
            JobStatus.QUEUED,
            JobStatus.PENDING
        ].includes(currentJob.status as JobStatus);
    }, []);

    // Initialize job state only once
    useEffect(() => {
        if (!job && initialJob && needsUpdates(initialJob)) {
            console.log(`🔄 useJobWebSocket: Initializing job state for processing job ${jobId.slice(-6)}`);
            setJob(initialJob);
        }
    }, [initialJob.id]); // Only initialize when job ID changes

    // WebSocket connection function
    const connect = useCallback(() => {
        if (!enabled || !mountedRef.current || !job || !needsUpdates(job)) {
            console.log(`⏸️ useJobWebSocket: Skipping connection for ${jobId.slice(-6)} - enabled: ${enabled}, mounted: ${mountedRef.current}, job: ${!!job}, needs updates: ${job ? needsUpdates(job) : false}`);
            return;
        }

        if (wsRef.current?.readyState === WebSocket.OPEN) {
            console.log(`✅ useJobWebSocket: Already connected for ${jobId.slice(-6)}`);
            return;
        }

        console.log(`🚀 useJobWebSocket: Starting connection for ${jobId.slice(-6)}`);
        setIsConnecting(true);
        setError(null);

        // Close existing connection
        if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
        }

        try {
            const updateCallback = (updatedJob: BiographyJob) => {
                if (!mountedRef.current) return;

                console.log(`📥 useJobWebSocket: Update received for ${jobId.slice(-6)}:`, {
                    status: updatedJob.status,
                    progress: updatedJob.progress_percentage
                });

                setJob(updatedJob);
                setError(null);
            };

            const errorCallback = (errorEvent: Event) => {
                if (!mountedRef.current) return;

                console.error(`💥 useJobWebSocket: Connection error for ${jobId.slice(-6)}:`, errorEvent);
                setError('WebSocket connection error');
                setIsConnected(false);
                setIsConnecting(false);

                if (onError) {
                    onError(errorEvent);
                }

                // Auto reconnect if enabled
                if (autoReconnect && job && needsUpdates(job)) {
                    console.log(`🔄 useJobWebSocket: Auto-reconnecting in 5 seconds for ${jobId.slice(-6)}`);
                    reconnectTimeoutRef.current = setTimeout(() => {
                        if (mountedRef.current && enabled) {
                            connect();
                        }
                    }, 5000);
                }
            };

            const completeCallback = () => {
                if (!mountedRef.current) return;

                console.log(`🏁 useJobWebSocket: Job completed for ${jobId.slice(-6)}`);
                setIsConnected(false);
                setIsConnecting(false);

                if (onComplete) {
                    onComplete();
                }
            };

            // Create WebSocket connection
            wsRef.current = biographyApi.subscribeToJobStatus(
                jobId,
                updateCallback,
                errorCallback,
                completeCallback
            );

            // Set up connection event listeners
            if (wsRef.current) {
                wsRef.current.addEventListener('open', () => {
                    if (!mountedRef.current) return;
                    console.log(`✅ useJobWebSocket: Connected for ${jobId.slice(-6)}`);
                    setIsConnected(true);
                    setIsConnecting(false);
                    setError(null);
                });

                wsRef.current.addEventListener('close', (event) => {
                    if (!mountedRef.current) return;
                    console.log(`🔒 useJobWebSocket: Disconnected for ${jobId.slice(-6)}, code: ${event.code}`);
                    setIsConnected(false);
                    setIsConnecting(false);
                });
            }

        } catch (err) {
            console.error(`💥 useJobWebSocket: Failed to create connection for ${jobId.slice(-6)}:`, err);
            setError(err instanceof Error ? err.message : 'Unknown error');
            setIsConnecting(false);
        }
    }, [enabled, job?.status, jobId, onComplete, onError, autoReconnect]); // Only essential dependencies

    // Disconnect function
    const disconnect = useCallback(() => {
        console.log(`🔒 useJobWebSocket: Manually disconnecting ${jobId.slice(-6)}`);

        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
        }

        if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
        }

        setIsConnected(false);
        setIsConnecting(false);
        setError(null);
    }, [jobId]);

    // Reconnect function
    const reconnect = useCallback(() => {
        console.log(`🔄 useJobWebSocket: Manual reconnect for ${jobId.slice(-6)}`);
        disconnect();
        setTimeout(() => {
            if (mountedRef.current) {
                connect();
            }
        }, 100); // Small delay to ensure cleanup
    }, [connect, disconnect, jobId]);

    // Main effect - manage connection lifecycle
    useEffect(() => {
        mountedRef.current = true;

        // Use initialJob for connection decisions to avoid state loops
        if (enabled && initialJob && needsUpdates(initialJob)) {
            console.log(`🎯 useJobWebSocket: Job ${jobId.slice(-6)} needs updates, connecting...`);
            connect();
        } else {
            console.log(`⏸️ useJobWebSocket: Job ${jobId.slice(-6)} doesn't need updates or disabled`);
            disconnect();
        }

        return () => {
            console.log(`🧹 useJobWebSocket: Cleaning up for ${jobId.slice(-6)}`);
            mountedRef.current = false;

            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
            }

            if (wsRef.current) {
                wsRef.current.close();
            }
        };
    }, [enabled, initialJob.status, jobId]); // Simplified dependencies to avoid callback recreation issues

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            mountedRef.current = false;
            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
            }
            if (wsRef.current) {
                wsRef.current.close();
            }
        };
    }, []);

    return {
        job,
        isConnected,
        isConnecting,
        error,
        reconnect,
        disconnect
    };
};

export default useJobWebSocket; 