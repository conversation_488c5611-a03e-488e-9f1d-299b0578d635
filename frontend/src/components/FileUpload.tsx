import React, { useState } from 'react';
import {
    Box,
    Typography,
    TextField,
    Paper,
    Alert,
    CircularProgress,
} from '@mui/material';
import { CloudUpload as CloudUploadIcon } from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { biographyApi } from '../services/api';
import { BiographyJob } from '../types';

interface FileUploadProps {
    onUploadSuccess: (job: BiographyJob) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess }) => {
    const [userName, setUserName] = useState('');
    const [email, setEmail] = useState('');
    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const onDrop = async (acceptedFiles: File[]) => {
        if (acceptedFiles.length === 0) return;

        const file = acceptedFiles[0];

        if (!file.type.includes('pdf')) {
            setError('Please upload a PDF file');
            return;
        }

        if (!userName.trim()) {
            setError('Please enter a user name');
            return;
        }

        setUploading(true);
        setError(null);

        try {
            const job = await biographyApi.uploadPDF(
                file,
                userName.trim(),
                email.trim() || undefined
            );
            onUploadSuccess(job);

            // Reset form
            setUserName('');
            setEmail('');
        } catch (err: any) {
            setError(err.response?.data?.detail || 'Upload failed');
        } finally {
            setUploading(false);
        }
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'application/pdf': ['.pdf']
        },
        maxFiles: 1,
        disabled: uploading || !userName.trim()
    });

    return (
        <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Typography variant="h5" gutterBottom>
                Upload Interview PDF
            </Typography>

            <Box sx={{ mb: 3 }}>
                <TextField
                    fullWidth
                    label="User Name"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    margin="normal"
                    required
                    disabled={uploading}
                />
                <TextField
                    fullWidth
                    label="Email (Optional)"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    margin="normal"
                    disabled={uploading}
                />
            </Box>

            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
            )}

            <Box
                {...getRootProps()}
                sx={{
                    border: '2px dashed',
                    borderColor: isDragActive ? 'primary.main' : 'grey.300',
                    borderRadius: 2,
                    p: 4,
                    textAlign: 'center',
                    cursor: userName.trim() && !uploading ? 'pointer' : 'not-allowed',
                    backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
                    opacity: !userName.trim() || uploading ? 0.6 : 1,
                    transition: 'all 0.2s ease-in-out',
                }}
            >
                <input {...getInputProps()} />

                {uploading ? (
                    <Box>
                        <CircularProgress sx={{ mb: 2 }} />
                        <Typography>Uploading...</Typography>
                    </Box>
                ) : (
                    <Box>
                        <CloudUploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                            {isDragActive
                                ? 'Drop the PDF file here'
                                : 'Drag & drop a PDF file here, or click to select'
                            }
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            {!userName.trim()
                                ? 'Please enter a user name first'
                                : 'PDF files only, max 50MB'
                            }
                        </Typography>
                    </Box>
                )}
            </Box>
        </Paper>
    );
};

export default FileUpload; 