import React, { useState, useEffect } from 'react';
import {
    Box,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Alert,
    CircularProgress,
    Typography,
    Chip,
    Card,
    CardContent,
    Grid,
    Tooltip,
    IconButton
} from '@mui/material';
import { Info as InfoIcon, CheckCircle as CheckIcon, Error as ErrorIcon } from '@mui/icons-material';
import { AIProviderSettings, AIProvider, ProviderInfo } from '../types';
import { settingsApi } from '../services/api';

interface AIProviderSelectorProps {
    onProviderChange?: () => void;
}

const AIProviderSelector: React.FC<AIProviderSelectorProps> = ({ onProviderChange }) => {
    const [settings, setSettings] = useState<AIProviderSettings | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [updating, setUpdating] = useState(false);

    useEffect(() => {
        loadSettings();
    }, []);

    const loadSettings = async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🔄 Loading AI provider settings...');
            const settingsData = await settingsApi.getAIProviderSettings();
            console.log('✅ Loaded AI provider settings:', settingsData);
            setSettings(settingsData);
        } catch (err: any) {
            console.error('❌ Error loading AI provider settings:', err);
            let errorMessage = 'Failed to load AI provider settings';

            if (err.response?.data?.detail) {
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleProviderChange = async (newProvider: string) => {
        setUpdating(true);
        setError(null);

        try {
            console.log('🔄 Updating AI provider to:', newProvider);
            await settingsApi.updateAIProvider(newProvider);
            console.log('✅ Updated AI provider successfully');

            // Update local state
            if (settings) {
                setSettings({
                    ...settings,
                    ai_provider: newProvider
                });
            }

            // Notify parent component
            if (onProviderChange) {
                onProviderChange();
            }
        } catch (err: any) {
            console.error('❌ Error updating AI provider:', err);
            let errorMessage = 'Failed to update AI provider';

            if (err.response?.data?.detail) {
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setUpdating(false);
        }
    };

    const getProviderDisplayName = (provider: string): string => {
        const names: Record<string, string> = {
            'openai': 'OpenAI GPT',
            'anthropic': 'Anthropic Claude',
            'gemini': 'Google Gemini',
            'grok': 'xAI Grok'
        };
        return names[provider] || provider;
    };

    const getProviderIcon = (provider: string): string => {
        const icons: Record<string, string> = {
            'openai': '🤖',
            'anthropic': '🧠',
            'gemini': '💎',
            'grok': '⚡'
        };
        return icons[provider] || '🔮';
    };

    const getProviderColor = (provider: string, status: string) => {
        if (status !== 'Available') return 'error';

        const colors: Record<string, any> = {
            'openai': 'primary',
            'anthropic': 'secondary',
            'gemini': 'success',
            'grok': 'warning'
        };
        return colors[provider] || 'default';
    };

    const getProviderFeatures = (provider: string): string[] => {
        const features: Record<string, string[]> = {
            'openai': ['General purpose', 'Reliable', 'Fast'],
            'anthropic': ['Safety-focused', 'Thoughtful', 'Long context'],
            'gemini': ['1M token context', 'Large files', 'Comprehensive'],
            'grok': ['Real-time info', 'Conversational', 'Up-to-date']
        };
        return features[provider] || [];
    };

    if (loading && !settings) {
        return (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <CircularProgress size={20} />
                <Typography>Loading AI provider settings...</Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
                AI Provider Configuration
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Select the AI provider to use for biography generation. Different providers offer unique capabilities and context windows.
            </Typography>

            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {settings && (
                <>
                    {/* Main Provider Selector */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap', mb: 3 }}>
                        <FormControl sx={{ minWidth: 250 }} size="small">
                            <InputLabel id="ai-provider-label">AI Provider</InputLabel>
                            <Select
                                labelId="ai-provider-label"
                                value={settings.ai_provider}
                                label="AI Provider"
                                onChange={(e) => handleProviderChange(e.target.value)}
                                disabled={updating}
                            >
                                {settings.available_providers.map((provider) => (
                                    <MenuItem
                                        key={provider}
                                        value={provider}
                                        disabled={settings.provider_status[provider] !== 'Available'}
                                    >
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                                            <Typography sx={{ fontSize: '1.2em' }}>
                                                {getProviderIcon(provider)}
                                            </Typography>
                                            <Typography>{getProviderDisplayName(provider)}</Typography>
                                            <Chip
                                                label={settings.provider_status[provider]}
                                                size="small"
                                                color={getProviderColor(provider, settings.provider_status[provider])}
                                                variant="outlined"
                                                icon={settings.provider_status[provider] === 'Available' ?
                                                    <CheckIcon fontSize="small" /> :
                                                    <ErrorIcon fontSize="small" />
                                                }
                                            />
                                        </Box>
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>

                        {updating && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <CircularProgress size={16} />
                                <Typography variant="body2" color="text.secondary">
                                    Updating...
                                </Typography>
                            </Box>
                        )}

                        <Chip
                            label={`Current: ${getProviderDisplayName(settings.ai_provider)}`}
                            color={getProviderColor(settings.ai_provider, settings.provider_status[settings.ai_provider])}
                            size="small"
                            icon={<span style={{ fontSize: '1em' }}>{getProviderIcon(settings.ai_provider)}</span>}
                        />
                    </Box>

                    {/* Provider Details Cards */}
                    <Grid container spacing={2}>
                        {Object.keys(settings.provider_descriptions).map((provider) => (
                            <Grid item xs={12} md={6} key={provider}>
                                <Card
                                    variant="outlined"
                                    sx={{
                                        height: '100%',
                                        border: settings.ai_provider === provider ? 2 : 1,
                                        borderColor: settings.ai_provider === provider ?
                                            getProviderColor(provider, settings.provider_status[provider]) + '.main' :
                                            'divider',
                                        opacity: settings.provider_status[provider] === 'Available' ? 1 : 0.6
                                    }}
                                >
                                    <CardContent>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                            <Typography sx={{ fontSize: '1.5em' }}>
                                                {getProviderIcon(provider)}
                                            </Typography>
                                            <Typography variant="h6">
                                                {getProviderDisplayName(provider)}
                                            </Typography>
                                            <Chip
                                                label={settings.provider_status[provider]}
                                                size="small"
                                                color={getProviderColor(provider, settings.provider_status[provider])}
                                                variant="filled"
                                            />
                                        </Box>

                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                            {settings.provider_descriptions[provider]}
                                        </Typography>

                                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                                            {getProviderFeatures(provider).map((feature, index) => (
                                                <Chip
                                                    key={index}
                                                    label={feature}
                                                    size="small"
                                                    variant="outlined"
                                                    sx={{ fontSize: '0.75rem' }}
                                                />
                                            ))}
                                        </Box>

                                        {settings.provider_models[provider] && (
                                            <Typography variant="caption" color="text.secondary">
                                                Models: {settings.provider_models[provider].length} available
                                            </Typography>
                                        )}
                                    </CardContent>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>

                    {/* Special Features Notice */}
                    {settings.ai_provider === 'gemini' && (
                        <Alert severity="info" sx={{ mt: 2 }}>
                            <Typography variant="body2">
                                <strong>🚀 Gemini 1.5 Pro Special Features:</strong>
                                <br />• 1 million token context window - can process entire large interviews in one pass
                                <br />• Automatically selected for files larger than 500k characters
                                <br />• Generates comprehensive biographies with better consistency
                            </Typography>
                        </Alert>
                    )}
                </>
            )}
        </Box>
    );
};

export default AIProviderSelector; 