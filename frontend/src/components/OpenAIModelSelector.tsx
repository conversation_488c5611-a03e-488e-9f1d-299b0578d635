import React, { useState, useEffect } from 'react';
import {
    Box,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Alert,
    CircularProgress,
    Typography,
    Chip
} from '@mui/material';
import { OpenAIModelSettings } from '../types';
import { settingsApi } from '../services/api';

const OpenAIModelSelector: React.FC = () => {
    const [settings, setSettings] = useState<OpenAIModelSettings | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [updating, setUpdating] = useState(false);

    useEffect(() => {
        loadSettings();
    }, []);

    const loadSettings = async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🔄 Loading OpenAI model settings...');
            const settingsData = await settingsApi.getOpenAIModelSettings();
            console.log('✅ Loaded OpenAI settings:', settingsData);
            setSettings(settingsData);
        } catch (err: any) {
            console.error('❌ Error loading OpenAI settings:', err);
            let errorMessage = 'Failed to load OpenAI model settings';

            if (err.response?.data?.detail) {
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleModelChange = async (newModel: string) => {
        setUpdating(true);
        setError(null);

        try {
            console.log('🔄 Updating OpenAI model to:', newModel);
            await settingsApi.updateOpenAIModel(newModel);
            console.log('✅ Updated OpenAI model successfully');

            // Update local state
            if (settings) {
                setSettings({
                    ...settings,
                    openai_model: newModel
                });
            }
        } catch (err: any) {
            console.error('❌ Error updating OpenAI model:', err);
            let errorMessage = 'Failed to update OpenAI model';

            if (err.response?.data?.detail) {
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setUpdating(false);
        }
    };

    const getModelTier = (model: string): string => {
        if (model.includes('gpt-4o')) return 'Latest';
        if (model.includes('gpt-4')) return 'Advanced';
        if (model.includes('gpt-3.5')) return 'Standard';
        return 'Unknown';
    };

    const getModelColor = (model: string) => {
        if (model.includes('gpt-4o')) return 'success';
        if (model.includes('gpt-4')) return 'primary';
        if (model.includes('gpt-3.5')) return 'secondary';
        return 'default';
    };

    if (loading && !settings) {
        return (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <CircularProgress size={20} />
                <Typography>Loading OpenAI model settings...</Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
                OpenAI Model Configuration
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Select the OpenAI model to use for biography generation. Higher tier models provide better quality but cost more.
            </Typography>

            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
            )}

            {settings && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                    <FormControl sx={{ minWidth: 200 }} size="small">
                        <InputLabel id="openai-model-label">OpenAI Model</InputLabel>
                        <Select
                            labelId="openai-model-label"
                            value={settings.openai_model}
                            label="OpenAI Model"
                            onChange={(e) => handleModelChange(e.target.value)}
                            disabled={updating}
                        >
                            {settings.available_models.map((model) => (
                                <MenuItem key={model} value={model}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                                        <Typography>{model}</Typography>
                                        <Chip
                                            label={getModelTier(model)}
                                            size="small"
                                            color={getModelColor(model) as any}
                                            variant="outlined"
                                        />
                                    </Box>
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    {updating && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CircularProgress size={16} />
                            <Typography variant="body2" color="text.secondary">
                                Updating...
                            </Typography>
                        </Box>
                    )}

                    <Chip
                        label={`Current: ${getModelTier(settings.openai_model)}`}
                        color={getModelColor(settings.openai_model) as any}
                        size="small"
                    />
                </Box>
            )}
        </Box>
    );
};

export default OpenAIModelSelector; 