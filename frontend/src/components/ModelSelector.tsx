import React, { useState, useEffect } from 'react';
import {
    Box,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Alert,
    CircularProgress,
    Typography,
    Chip,
    Paper
} from '@mui/material';
import { AIProviderSettings, OpenAIModelSettings } from '../types';
import { settingsApi } from '../services/api';

interface ModelSelectorProps {
    providerSettings?: AIProviderSettings;
    onProviderChange?: () => void;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({ providerSettings, onProviderChange }) => {
    const [modelSettings, setModelSettings] = useState<OpenAIModelSettings | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [updating, setUpdating] = useState(false);

    useEffect(() => {
        loadModelSettings();
    }, [providerSettings?.ai_provider]); // Reload when provider changes

    const loadModelSettings = async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🔄 Loading model settings for current provider...');
            const settingsData = await settingsApi.getOpenAIModelSettings();
            console.log('✅ Loaded model settings:', settingsData);
            setModelSettings(settingsData);
        } catch (err: any) {
            console.error('❌ Error loading model settings:', err);
            let errorMessage = 'Failed to load model settings';

            if (err.response?.data?.detail) {
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleModelChange = async (newModel: string) => {
        setUpdating(true);
        setError(null);

        try {
            console.log('🔄 Updating model to:', newModel);
            await settingsApi.updateOpenAIModel(newModel);
            console.log('✅ Updated model successfully');

            // Update local state
            if (modelSettings) {
                setModelSettings({
                    ...modelSettings,
                    openai_model: newModel
                });
            }

            // Notify parent component if needed
            if (onProviderChange) {
                onProviderChange();
            }
        } catch (err: any) {
            console.error('❌ Error updating model:', err);
            let errorMessage = 'Failed to update model';

            if (err.response?.data?.detail) {
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setUpdating(false);
        }
    };

    const getModelOutputTokens = (model: string, provider: string): number => {
        if (provider === 'openai') {
            if (model.includes('gpt-4o')) return 16384;
            if (model.includes('gpt-4')) return 8192;
            if (model.includes('gpt-3.5')) return 4096;
        } else if (provider === 'anthropic') {
            return 4096; // All Claude models
        } else if (provider === 'gemini') {
            // Gemini 2.5 models have significantly higher output limits
            if (model === 'gemini-2.5-pro') return 65535;     // 65K tokens
            if (model === 'gemini-2.5-flash') return 65535;   // 65K tokens
            if (model.includes('2.5-flash-lite')) return 8192; // 8K tokens for lite
            // All other Gemini models (2.0, 1.5) have 8K limit
            return 8192;
        } else if (provider === 'grok') {
            return 4096; // Grok models
        }
        return 2048; // Default fallback
    };

    const getModelTier = (model: string, provider: string): string => {
        const outputTokens = getModelOutputTokens(model, provider);
        const outputTokensK = (outputTokens / 1000).toFixed(0);

        if (provider === 'openai') {
            if (model.includes('gpt-4o')) return `Latest (${outputTokensK}K out)`;
            if (model.includes('gpt-4')) return `Advanced (${outputTokensK}K out)`;
            if (model.includes('gpt-3.5')) return `Standard (${outputTokensK}K out)`;
        } else if (provider === 'anthropic') {
            if (model.includes('opus')) return `Advanced (${outputTokensK}K out)`;
            if (model.includes('sonnet')) return `Balanced (${outputTokensK}K out)`;
            if (model.includes('haiku')) return `Fast (${outputTokensK}K out)`;
        } else if (provider === 'gemini') {
            if (model === 'gemini-2.5-pro') return `Thinking (2M in, ${outputTokensK}K out)`;
            if (model === 'gemini-2.5-flash') return `Best Value (1M in, ${outputTokensK}K out)`;
            if (model.includes('2.5-flash-lite')) return `Cost-Efficient (1M in, ${outputTokensK}K out)`;
            if (model === 'gemini-2.0-flash') return `Next Gen (1M in, ${outputTokensK}K out)`;
            if (model === 'gemini-2.0-flash-lite') return `Fast & Cheap (1M in, ${outputTokensK}K out)`;
            if (model === 'gemini-1.5-flash') return `Versatile (1M in, ${outputTokensK}K out)`;
            if (model === 'gemini-1.5-flash-8b') return `Efficient (1M in, ${outputTokensK}K out)`;
            if (model === 'gemini-1.5-pro') return `Reasoning (1M in, ${outputTokensK}K out)`;
        } else if (provider === 'grok') {
            if (model.includes('vision')) return `Vision (${outputTokensK}K out)`;
            if (model.includes('beta')) return `Latest (${outputTokensK}K out)`;
        }
        return `Unknown (${outputTokensK}K out)`;
    };

    const getModelColor = (model: string, provider: string) => {
        if (provider === 'openai') {
            if (model.includes('gpt-4o')) return 'success';
            if (model.includes('gpt-4')) return 'primary';
            if (model.includes('gpt-3.5')) return 'secondary';
        } else if (provider === 'anthropic') {
            if (model.includes('opus')) return 'primary';
            if (model.includes('sonnet')) return 'secondary';
            if (model.includes('haiku')) return 'success';
        } else if (provider === 'gemini') {
            if (model === 'gemini-2.5-pro') return 'success';     // Most powerful - green
            if (model === 'gemini-2.5-flash') return 'primary';   // Best value - blue  
            if (model.includes('2.5-flash-lite')) return 'info';  // Cost-efficient - light blue
            if (model === 'gemini-2.0-flash') return 'secondary'; // Next gen - gray
            if (model === 'gemini-2.0-flash-lite') return 'secondary'; // Fast & cheap - gray
            if (model === 'gemini-1.5-flash') return 'warning';   // Versatile - orange
            if (model === 'gemini-1.5-flash-8b') return 'warning'; // Efficient - orange
            if (model === 'gemini-1.5-pro') return 'warning';     // Reasoning - orange
        } else if (provider === 'grok') {
            return 'warning';
        }
        return 'default';
    };

    const getProviderDisplayName = (provider: string): string => {
        const names: Record<string, string> = {
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'gemini': 'Gemini',
            'grok': 'Grok'
        };
        return names[provider] || provider;
    };

    const getAvailableModels = (): string[] => {
        if (!providerSettings || !modelSettings) return [];

        const currentProvider = providerSettings.ai_provider;

        // If we have provider-specific models, use those
        if (providerSettings.provider_models[currentProvider]) {
            return providerSettings.provider_models[currentProvider];
        }

        // Otherwise use the models from modelSettings (fallback)
        return modelSettings.available_models;
    };

    const getCurrentProvider = (): string => {
        return providerSettings?.ai_provider || 'openai';
    };

    if (loading && !modelSettings) {
        return (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <CircularProgress size={20} />
                <Typography>Loading model settings...</Typography>
            </Box>
        );
    }

    if (!providerSettings) {
        return (
            <Paper sx={{ p: 2, mb: 3 }}>
                <Typography variant="body2" color="text.secondary">
                    Loading provider settings...
                </Typography>
            </Paper>
        );
    }

    const currentProvider = getCurrentProvider();
    const availableModels = getAvailableModels();

    return (
        <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
                Model Configuration
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Select the specific model to use for biography generation with{' '}
                <strong>{getProviderDisplayName(currentProvider)}</strong>.
                Each model will automatically use its maximum output token limit for the most detailed responses.
                {currentProvider === 'gemini' && (
                    <span style={{ color: '#1976d2', fontWeight: 'bold' }}>
                        {' '}Gemini 2.5 models support up to 65K output tokens for incredibly detailed biographies!
                    </span>
                )}
            </Typography>

            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
            )}

            {modelSettings && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                    <FormControl sx={{ minWidth: 280 }} size="small">
                        <InputLabel id="model-label">
                            {getProviderDisplayName(currentProvider)} Model
                        </InputLabel>
                        <Select
                            labelId="model-label"
                            value={modelSettings.openai_model}
                            label={`${getProviderDisplayName(currentProvider)} Model`}
                            onChange={(e) => handleModelChange(e.target.value)}
                            disabled={updating || availableModels.length === 0}
                        >
                            {availableModels.map((model) => (
                                <MenuItem key={model} value={model}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                                        <Typography>{model}</Typography>
                                        <Chip
                                            label={getModelTier(model, currentProvider)}
                                            size="small"
                                            color={getModelColor(model, currentProvider) as any}
                                            variant="outlined"
                                        />
                                    </Box>
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    {updating && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CircularProgress size={16} />
                            <Typography variant="body2" color="text.secondary">
                                Updating...
                            </Typography>
                        </Box>
                    )}

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Chip
                            label={`Current: ${getModelTier(modelSettings.openai_model, currentProvider)}`}
                            color={getModelColor(modelSettings.openai_model, currentProvider) as any}
                            size="small"
                        />
                        <Typography variant="caption" color="text.secondary">
                            🚀 Max output: {getModelOutputTokens(modelSettings.openai_model, currentProvider).toLocaleString()} tokens
                        </Typography>
                    </Box>
                </Box>
            )}

            {availableModels.length === 0 && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                    No models available for {getProviderDisplayName(currentProvider)} provider.
                    Please check your API configuration.
                </Alert>
            )}
        </Paper>
    );
};

export default ModelSelector; 