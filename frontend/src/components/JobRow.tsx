import React, { useMemo, useEffect, useCallback } from 'react';
import {
    Typography,
    Chip,
    LinearProgress,
    Box,
    IconButton
} from '@mui/material';
import {
    Download as DownloadIcon,
    Delete as DeleteIcon,
    Visibility as ViewIcon,
    PlayArrow as PlayIcon,
    Refresh as RefreshIcon,
    RocketLaunch as RocketIcon,
    Science as ScienceIcon,
} from '@mui/icons-material';
import { BiographyJob, JobStatus } from '../types';
import { useJobWebSocket } from '../utils/useJobWebSocket';

interface JobRowProps {
    job: BiographyJob;
    onView: (job: BiographyJob) => void;
    onStart: (jobId: string) => void;
    onStartEnhancedIterative?: (jobId: string) => void;
    onStartEnhancedIterativeDirect?: (jobId: string) => void;
    onReset?: (jobId: string) => void;
    onDelete: (jobId: string) => void;
    onDownload: (jobId: string) => void;
    onJobUpdate?: () => void; // Callback to refresh the job list
}

const JobRow: React.FC<JobRowProps> = ({
    job,
    onView,
    onStart,
    onStartEnhancedIterative,
    onStartEnhancedIterativeDirect,
    onReset,
    onDelete,
    onDownload,
    onJobUpdate
}) => {
    console.log(`🔄 JobRow: Rendering for job ${job.id.slice(-6)}, status: ${job.status}, progress: ${job.progress_percentage}%`);

    // Normalize status to one of three values
    const normalizeStatus = (status: string): 'pending' | 'processing' | 'completed' | 'failed' => {
        const statusLower = status.toLowerCase();

        if (statusLower === 'pending' || statusLower === 'queued') {
            return 'pending';
        }

        if (statusLower === 'completed') {
            return 'completed';
        }

        if (statusLower === 'failed' || statusLower === 'error') {
            return 'failed';
        }

        // All other statuses are considered processing
        return 'processing';
    };

    const currentStatus = normalizeStatus(job.status);

    // Determine if this job needs real-time updates
    const needsRealTime = [
        JobStatus.PROCESSING,
        JobStatus.ITERATIVE_WRITING,
        JobStatus.QUEUED,
        JobStatus.PENDING
    ].includes(job.status as JobStatus);

    console.log(`🎯 JobRow: Job ${job.id.slice(-6)} needs real-time updates: ${needsRealTime} (status: ${currentStatus})`);
    console.log(`🔍 JobRow: Job status details:`, {
        originalStatus: job.status,
        statusType: typeof job.status,
        needsRealTime,
        matchedStatuses: [JobStatus.PROCESSING, JobStatus.ITERATIVE_WRITING, JobStatus.QUEUED, JobStatus.PENDING],
        JobStatusEnum: JobStatus
    });

    // Callback when job completes
    const handleJobComplete = useCallback(() => {
        console.log(`🏁 JobRow: Job ${job.id.slice(-6)} completed via WebSocket, refreshing list`);
        // Refresh the job list from parent component
        if (onJobUpdate) {
            onJobUpdate();
        }
    }, [job.id, onJobUpdate]);

    // Use WebSocket hook for real-time updates ONLY for processing jobs
    const {
        job: wsJob,
        isConnected,
        isConnecting,
        error: wsError,
        reconnect
    } = useJobWebSocket(job, {
        jobId: job.id,
        enabled: needsRealTime,
        autoReconnect: true,
        onComplete: handleJobComplete,
        onError: (error) => {
            console.error(`💥 JobRow: WebSocket error for ${job.id.slice(-6)}:`, error);
        }
    });

    // Log WebSocket hook results
    console.log(`🔍 JobRow: WebSocket hook status for ${job.id.slice(-6)}:`, {
        enabled: needsRealTime,
        isConnected,
        isConnecting,
        hasWsJob: !!wsJob,
        wsError,
        wsJobStatus: wsJob?.status,
        wsJobProgress: wsJob?.progress_percentage
    });

    // Use WebSocket data ONLY for processing jobs that are connected
    const currentJob = useMemo(() => {
        // For completed/failed/pending jobs, always use prop data
        if (!needsRealTime) {
            console.log(`📊 JobRow: Using prop data for ${currentStatus} job ${job.id.slice(-6)}: ${job.status} (${job.progress_percentage}%)`);
            return job;
        }

        // For processing jobs, use WebSocket data if available and connected
        if (wsJob && isConnected && needsRealTime) {
            console.log(`🔄 JobRow: Using WebSocket data for processing job ${job.id.slice(-6)}: ${wsJob.status} (${wsJob.progress_percentage}%)`);
            return wsJob;
        }

        // Fallback to prop data for processing jobs without WebSocket connection
        console.log(`📊 JobRow: Using prop data fallback for processing job ${job.id.slice(-6)}: ${job.status} (${job.progress_percentage}%)`);
        return job;
    }, [job, wsJob, isConnected, needsRealTime, currentStatus]);

    console.log(`🎨 JobRow: Final render data for ${job.id.slice(-6)}:`, {
        originalStatus: job.status,
        currentStatus,
        progress: currentJob.progress_percentage,
        needsRealTime,
        wsConnected: isConnected,
        usingWSData: needsRealTime && wsJob && isConnected
    });

    const getStatusColor = (status: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
        switch (status) {
            case 'completed':
                return 'success';
            case 'failed':
                return 'error';
            case 'processing':
                return 'info';
            case 'pending':
                return 'default';
            default:
                return 'default';
        }
    };

    const getStatusText = (status: string): string => {
        switch (status) {
            case 'pending':
                return 'Pending';
            case 'processing':
                return 'Processing';
            case 'completed':
                return 'Completed';
            case 'failed':
                return 'Failed';
            default:
                return status;
        }
    };

    return (
        <>
            {/* User Column */}
            <td style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
                <Typography variant="body2" fontWeight="bold">
                    {currentJob.user_name}
                </Typography>
                {currentJob.email && (
                    <Typography variant="caption" color="text.secondary">
                        {currentJob.email}
                    </Typography>
                )}
            </td>

            {/* Status Column */}
            <td style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
                <Box display="flex" alignItems="center" gap={1}>
                    <Chip
                        label={getStatusText(currentStatus)}
                        color={getStatusColor(currentStatus)}
                        size="small"
                    />

                    {/* WebSocket connection indicators - only for processing jobs */}
                    {needsRealTime && (
                        <Box display="flex" alignItems="center" gap={0.5}>
                            {isConnecting && (
                                <Box
                                    sx={{
                                        width: 6,
                                        height: 6,
                                        borderRadius: '50%',
                                        backgroundColor: '#ff9800',
                                        animation: 'pulse 1s ease-in-out infinite'
                                    }}
                                    title="Connecting..."
                                />
                            )}
                            {isConnected && (
                                <Box
                                    sx={{
                                        width: 6,
                                        height: 6,
                                        borderRadius: '50%',
                                        backgroundColor: '#4caf50',
                                        animation: 'pulse 2s ease-in-out infinite'
                                    }}
                                    title="Connected to WebSocket"
                                />
                            )}
                            {wsError && (
                                <Box
                                    sx={{
                                        width: 6,
                                        height: 6,
                                        borderRadius: '50%',
                                        backgroundColor: '#f44336',
                                        cursor: 'pointer'
                                    }}
                                    title={`WebSocket error: ${wsError}`}
                                    onClick={reconnect}
                                />
                            )}
                        </Box>
                    )}
                </Box>
            </td>

            {/* Progress Column */}
            <td style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
                <Box display="flex" alignItems="center" gap={1} width="100%">
                    <LinearProgress
                        variant="determinate"
                        value={currentJob.progress_percentage || 0}
                        sx={{
                            flexGrow: 1,
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: 'rgba(0, 0, 0, 0.1)',
                            '& .MuiLinearProgress-bar': {
                                borderRadius: 4,
                                backgroundColor:
                                    currentStatus === 'completed' ? '#4caf50' :
                                        isConnected && needsRealTime ? '#2196f3' :
                                            '#90a4ae'
                            }
                        }}
                    />
                    <Typography variant="caption" color="textSecondary">
                        {Math.round(currentJob.progress_percentage || 0)}%
                    </Typography>
                </Box>
            </td>

            {/* Created Column */}
            <td style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
                <Typography variant="caption">
                    {new Date(currentJob.created_at).toLocaleString()}
                </Typography>
            </td>

            {/* Processing Time Column */}
            <td style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
                <Typography variant="caption">
                    {currentJob.processing_time_seconds ?
                        `${Math.floor(currentJob.processing_time_seconds / 60)}:${Math.floor(currentJob.processing_time_seconds % 60).toString().padStart(2, '0')}`
                        : '-'
                    }
                </Typography>
            </td>

            {/* Actions Column */}
            <td style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton size="small" onClick={() => onView(currentJob)} title="View details">
                        <ViewIcon fontSize="small" />
                    </IconButton>

                    {currentStatus === 'pending' && (
                        <IconButton
                            size="small"
                            onClick={() => onStart(currentJob.id)}
                            color="primary"
                            title="Start processing"
                        >
                            <PlayIcon fontSize="small" />
                        </IconButton>
                    )}

                    {currentStatus === 'pending' && onStartEnhancedIterative && (
                        <IconButton
                            size="small"
                            onClick={() => onStartEnhancedIterative(currentJob.id)}
                            color="secondary"
                            title="Enhanced Iterative (SQS) - Book-style with large context"
                        >
                            <RocketIcon fontSize="small" />
                        </IconButton>
                    )}

                    {currentStatus === 'pending' && onStartEnhancedIterativeDirect && (
                        <IconButton
                            size="small"
                            onClick={() => onStartEnhancedIterativeDirect(currentJob.id)}
                            color="info"
                            title="Enhanced Iterative (Direct) - Testing/debugging version"
                        >
                            <ScienceIcon fontSize="small" />
                        </IconButton>
                    )}

                    {currentStatus === 'failed' && onReset && (
                        <IconButton
                            size="small"
                            onClick={() => onReset(currentJob.id)}
                            color="warning"
                            title="Restart"
                        >
                            <RefreshIcon fontSize="small" />
                        </IconButton>
                    )}

                    {currentStatus === 'completed' && (currentJob.has_output || currentJob.output_pdf_path) && (
                        <IconButton
                            size="small"
                            onClick={() => onDownload(currentJob.id)}
                            color="primary"
                            title="Download biography"
                        >
                            <DownloadIcon fontSize="small" />
                        </IconButton>
                    )}

                    <IconButton
                        size="small"
                        onClick={() => onDelete(currentJob.id)}
                        color="error"
                        title="Delete job"
                    >
                        <DeleteIcon fontSize="small" />
                    </IconButton>
                </Box>
            </td>
        </>
    );
};

export default JobRow; 