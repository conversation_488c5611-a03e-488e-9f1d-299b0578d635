import React, { useState, useEffect } from 'react';
import { Box, Typography, Divider } from '@mui/material';
import AIProviderSelector from './AIProviderSelector';
import ModelSelector from './ModelSelector';
import { AIProviderSettings } from '../types';
import { settingsApi } from '../services/api';

const SettingsContainer: React.FC = () => {
    const [providerSettings, setProviderSettings] = useState<AIProviderSettings | undefined>(undefined);
    const [refreshKey, setRefreshKey] = useState(0);

    useEffect(() => {
        loadProviderSettings();
    }, [refreshKey]);

    const loadProviderSettings = async () => {
        try {
            console.log('🔄 Loading provider settings...');
            const settings = await settingsApi.getAIProviderSettings();
            console.log('✅ Loaded provider settings:', settings);
            setProviderSettings(settings);
        } catch (error) {
            console.error('❌ Error loading provider settings:', error);
        }
    };

    const handleProviderChange = () => {
        // Refresh both provider and model settings when provider changes
        console.log('🔄 Provider changed, refreshing settings...');
        setRefreshKey(prev => prev + 1);
    };

    const handleModelChange = () => {
        // Model change doesn't require provider refresh, just log
        console.log('✅ Model updated successfully');
    };

    return (
        <Box sx={{ maxWidth: 1200 }}>
            <Typography variant="h4" gutterBottom>
                Settings
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Configure AI providers and models for biography generation.
            </Typography>

            {/* AI Provider Selection */}
            <AIProviderSelector
                key={`provider-${refreshKey}`}
                onProviderChange={handleProviderChange}
            />

            <Divider sx={{ my: 3 }} />

            {/* Model Selection - depends on selected provider */}
            <ModelSelector
                key={`model-${refreshKey}`}
                providerSettings={providerSettings}
                onProviderChange={handleModelChange}
            />
        </Box>
    );
};

export default SettingsContainer; 