import React, { useState, useEffect } from 'react';
import {
    Box,
    Paper,
    Typography,
    Button,
    TextField,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Alert,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Chip,
    CircularProgress,
    Divider,
    Card,
    CardContent,
    Grid,
} from '@mui/material';
import {
    ExpandMore as ExpandMoreIcon,
    Edit as EditIcon,
    Add as AddIcon,
    BugReport as DebugIcon,
    Refresh as RefreshIcon,
    ClearAll as ClearIcon,
    CleaningServices as CleaningServicesIcon,
} from '@mui/icons-material';
import { AgentPrompt } from '../types';
import { promptsApi } from '../services/api';
import OpenAIModelSelector from './OpenAIModelSelector';

const PromptManager: React.FC = () => {
    const [prompts, setPrompts] = useState<AgentPrompt[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [editingPrompt, setEditingPrompt] = useState<AgentPrompt | null>(null);
    const [agentName, setAgentName] = useState('');
    const [promptContent, setPromptContent] = useState('');
    const [debugInfo, setDebugInfo] = useState<any>(null);
    const [showDebug, setShowDebug] = useState(false);
    const [outlineTestLoading, setOutlineTestLoading] = useState(false);
    const [outlineTestResult, setOutlineTestResult] = useState<any>(null);

    const agentTypes = ['outline', 'writer', 'evaluation', 'rewrite'];

    useEffect(() => {
        loadPrompts();
    }, []);

    const loadPrompts = async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🔄 PromptManager: Loading prompts...');
            const promptsData = await promptsApi.getAllPrompts();
            console.log('✅ PromptManager: Loaded prompts:', promptsData);
            setPrompts(promptsData);
        } catch (err: any) {
            console.error('❌ PromptManager: Error loading prompts:', err);
            let errorMessage = 'Failed to load prompts';

            if (err.response?.data?.detail) {
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                } else {
                    errorMessage = JSON.stringify(err.response.data.detail);
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = (prompt: AgentPrompt) => {
        setEditingPrompt(prompt);
        setAgentName(prompt.agent_name);
        setPromptContent(prompt.prompt_content);
        setDialogOpen(true);
    };

    const handleAdd = () => {
        setEditingPrompt(null);
        setAgentName('');
        setPromptContent('');
        setDialogOpen(true);
    };

    const handleSave = async () => {
        if (!agentName.trim() || !promptContent.trim()) {
            setError('Please fill in all fields');
            return;
        }

        setLoading(true);
        setError(null);

        try {
            if (editingPrompt) {
                // Update existing prompt
                await promptsApi.updatePrompt(editingPrompt.id, {
                    prompt_content: promptContent.trim()
                });
            } else {
                // Create new prompt
                await promptsApi.createPrompt({
                    agent_name: agentName.trim(),
                    prompt_content: promptContent.trim()
                });
            }

            await loadPrompts();
            setDialogOpen(false);
        } catch (err: any) {
            setError(err.response?.data?.detail || 'Failed to save prompt');
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setDialogOpen(false);
        setEditingPrompt(null);
        setAgentName('');
        setPromptContent('');
        setError(null);
    };

    const handleDebugPrompts = async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🔍 PromptManager: Loading debug information...');
            const debugData = await promptsApi.debugPrompts();
            console.log('✅ PromptManager: Debug info loaded:', debugData);
            setDebugInfo(debugData);
            setShowDebug(true);
        } catch (err: any) {
            console.error('❌ PromptManager: Error loading debug info:', err);
            setError(err.response?.data?.detail || 'Failed to load debug information');
        } finally {
            setLoading(false);
        }
    };

    const handleClearCache = async () => {
        setLoading(true);
        setError(null);

        try {
            console.log('🗑️ PromptManager: Clearing prompt cache...');
            const result = await promptsApi.clearCache();
            console.log('✅ PromptManager: Cache cleared:', result);

            // Refresh debug info if showing
            if (showDebug) {
                await handleDebugPrompts();
            }

            // Optionally refresh prompts list
            await loadPrompts();
        } catch (err: any) {
            console.error('❌ PromptManager: Error clearing cache:', err);
            setError(err.response?.data?.detail || 'Failed to clear cache');
        } finally {
            setLoading(false);
        }
    };

    const handleTestOutlineParsing = async () => {
        setOutlineTestLoading(true);
        setOutlineTestResult(null);

        try {
            const response = await fetch('/api/prompts/test-outline-parsing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to test outline parsing');
            }

            const result = await response.json();
            setOutlineTestResult(result);
        } catch (error) {
            console.error('Error testing outline parsing:', error);
            alert('Failed to test outline parsing');
        } finally {
            setOutlineTestLoading(false);
        }
    };

    const handleCleanNameTags = async () => {
        const content = prompt('Enter content with name tags to clean:',
            'she immersed {HIM_HERSELF} in creative projects with peers who shared similar passions');
        const personName = prompt('Enter person name:', 'Jennifer Martinez');

        if (!content || !personName) return;

        try {
            const response = await fetch('/api/prompts/clean-name-tags', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content,
                    person_name: personName
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to clean name tags');
            }

            const result = await response.json();

            // Show results in a modal or alert
            const analysisText = `
Original Tags Found: ${result.analysis.original_tags.join(', ') || 'None'}
Remaining Tags: ${result.analysis.remaining_tags.join(', ') || 'None'}
Tags Cleaned: ${result.analysis.tags_cleaned}
Placeholders Cleaned: ${result.analysis.placeholders_cleaned}

ORIGINAL:
${result.original_content}

CLEANED:
${result.cleaned_content}
            `;

            alert('Name Tags Cleanup Result:\n' + analysisText);

        } catch (error) {
            console.error('Error cleaning name tags:', error);
            alert('Failed to clean name tags');
        }
    };

    return (
        <Paper elevation={3} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5">
                    AI Agent Prompts
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                        variant="outlined"
                        size="small"
                        startIcon={<DebugIcon />}
                        onClick={handleDebugPrompts}
                        disabled={loading}
                    >
                        Debug Info
                    </Button>
                    <Button
                        variant="outlined"
                        size="small"
                        startIcon={<ClearIcon />}
                        onClick={handleClearCache}
                        disabled={loading}
                    >
                        Clear Cache
                    </Button>
                    <Button
                        variant="outlined"
                        size="small"
                        color="warning"
                        onClick={handleTestOutlineParsing}
                        disabled={loading}
                    >
                        Test Outline
                    </Button>
                    <Button
                        variant="outlined"
                        startIcon={<CleaningServicesIcon />}
                        onClick={handleCleanNameTags}
                        disabled={loading}
                    >
                        Clean Name Tags
                    </Button>
                    <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleAdd}
                        disabled={loading}
                    >
                        Add Prompt
                    </Button>
                </Box>
            </Box>

            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
            )}

            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Configure the prompts used by each AI agent in the biography generation pipeline.
            </Typography>

            <OpenAIModelSelector />

            {loading && prompts.length === 0 ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                    <CircularProgress />
                    <Typography sx={{ ml: 2 }}>Loading prompts...</Typography>
                </Box>
            ) : (
                <>
                    {prompts.map((prompt) => (
                        <Accordion key={prompt.id} sx={{ mb: 2 }}>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                                    <Typography variant="h6" sx={{ textTransform: 'capitalize' }}>
                                        {prompt.agent_name} Agent
                                    </Typography>
                                    <Chip
                                        label={`v${prompt.version}`}
                                        size="small"
                                        color={prompt.is_active ? 'success' : 'default'}
                                    />
                                    <Box sx={{ flexGrow: 1 }} />
                                    <Button
                                        size="small"
                                        startIcon={<EditIcon />}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleEdit(prompt);
                                        }}
                                    >
                                        Edit
                                    </Button>
                                </Box>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Typography
                                    variant="body2"
                                    component="pre"
                                    sx={{
                                        whiteSpace: 'pre-wrap',
                                        backgroundColor: 'grey.50',
                                        p: 2,
                                        borderRadius: 1,
                                        maxHeight: 300,
                                        overflow: 'auto',
                                        fontFamily: 'monospace',
                                        fontSize: '0.875rem'
                                    }}
                                >
                                    {prompt.prompt_content}
                                </Typography>
                            </AccordionDetails>
                        </Accordion>
                    ))}

                    {prompts.length === 0 && !loading && (
                        <Typography variant="body1" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                            No prompts found. Click "Add Prompt" to create your first prompt.
                        </Typography>
                    )}
                </>
            )}

            {/* Debug Information Section */}
            {showDebug && debugInfo && (
                <>
                    <Divider sx={{ my: 4 }} />
                    <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                            <DebugIcon sx={{ mr: 1 }} />
                            Prompt Debug Information
                            <Button
                                size="small"
                                onClick={() => setShowDebug(false)}
                                sx={{ ml: 'auto' }}
                            >
                                Hide
                            </Button>
                        </Typography>

                        <Grid container spacing={2} sx={{ mb: 3 }}>
                            <Grid item xs={12} sm={4}>
                                <Card>
                                    <CardContent>
                                        <Typography variant="subtitle2" color="text.secondary">
                                            Total Active Prompts
                                        </Typography>
                                        <Typography variant="h4">
                                            {debugInfo.total_active_prompts}
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid>
                            <Grid item xs={12} sm={8}>
                                <Card>
                                    <CardContent>
                                        <Typography variant="subtitle2" color="text.secondary">
                                            Cache Status
                                        </Typography>
                                        <Typography variant="h6">
                                            {debugInfo.cache_status}
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>

                        {/* Load Test Results */}
                        <Typography variant="h6" sx={{ mb: 2 }}>
                            Load Test Results
                        </Typography>
                        <Grid container spacing={2} sx={{ mb: 3 }}>
                            {Object.entries(debugInfo.load_test || {}).map(([agentName, testResult]: [string, any]) => (
                                <Grid item xs={12} sm={6} md={3} key={agentName}>
                                    <Card>
                                        <CardContent>
                                            <Typography variant="subtitle2" color="text.secondary">
                                                {agentName.toUpperCase()} Agent
                                            </Typography>
                                            <Chip
                                                label={testResult.status}
                                                color={testResult.status === 'success' ? 'success' : 'error'}
                                                size="small"
                                                sx={{ mb: 1 }}
                                            />
                                            {testResult.status === 'success' && (
                                                <Typography variant="body2">
                                                    Length: {testResult.length} chars
                                                </Typography>
                                            )}
                                            {testResult.error && (
                                                <Typography variant="body2" color="error">
                                                    Error: {testResult.error}
                                                </Typography>
                                            )}
                                        </CardContent>
                                    </Card>
                                </Grid>
                            ))}
                        </Grid>

                        {/* Outline Parsing Test Results */}
                        {debugInfo.outline_parsing_test && (
                            <>
                                <Typography variant="h6" sx={{ mb: 2 }}>
                                    Outline Parsing Test Results
                                </Typography>
                                <Card sx={{ mb: 3 }}>
                                    <CardContent>
                                        <Typography variant="subtitle2" color="text.secondary">
                                            Test Status
                                        </Typography>
                                        <Chip
                                            label={debugInfo.outline_parsing_test.success ? 'SUCCESS' : 'FAILED'}
                                            color={debugInfo.outline_parsing_test.success ? 'success' : 'error'}
                                            sx={{ mb: 2 }}
                                        />

                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Model:</strong> {debugInfo.outline_parsing_test.test_parameters?.model}
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Sample Text Length:</strong> {debugInfo.outline_parsing_test.test_parameters?.sample_text_length} chars
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 2 }}>
                                            <strong>Target Chapters:</strong> {debugInfo.outline_parsing_test.test_parameters?.target_chapters}
                                        </Typography>

                                        {debugInfo.outline_parsing_test.parsing_results && (
                                            <>
                                                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                                    Parsing Attempts:
                                                </Typography>
                                                {debugInfo.outline_parsing_test.parsing_results.parsing_attempts?.map((attempt: any, idx: number) => (
                                                    <Box key={idx} sx={{ ml: 2, mb: 1 }}>
                                                        <Typography variant="body2">
                                                            <Chip
                                                                size="small"
                                                                label={attempt.pattern}
                                                                color={attempt.success ? 'success' : 'default'}
                                                                sx={{ mr: 1 }}
                                                            />
                                                            {attempt.success ?
                                                                `✅ Success - ${attempt.chapters_found || 0} chapters found` :
                                                                `❌ Failed - ${attempt.matches_found || 0} matches`
                                                            }
                                                        </Typography>
                                                    </Box>
                                                ))}

                                                {debugInfo.outline_parsing_test.parsing_results.successful_parse && (
                                                    <Accordion sx={{ mt: 2 }}>
                                                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                                            <Typography variant="subtitle2">
                                                                Successful Parse Result
                                                            </Typography>
                                                        </AccordionSummary>
                                                        <AccordionDetails>
                                                            <Typography
                                                                variant="body2"
                                                                component="pre"
                                                                sx={{
                                                                    whiteSpace: 'pre-wrap',
                                                                    backgroundColor: 'grey.50',
                                                                    p: 2,
                                                                    borderRadius: 1,
                                                                    maxHeight: 300,
                                                                    overflow: 'auto',
                                                                    fontFamily: 'monospace',
                                                                    fontSize: '0.75rem'
                                                                }}
                                                            >
                                                                {JSON.stringify(debugInfo.outline_parsing_test.parsing_results.successful_parse, null, 2)}
                                                            </Typography>
                                                        </AccordionDetails>
                                                    </Accordion>
                                                )}

                                                <Accordion sx={{ mt: 1 }}>
                                                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                                        <Typography variant="subtitle2">
                                                            Raw AI Response
                                                        </Typography>
                                                    </AccordionSummary>
                                                    <AccordionDetails>
                                                        <Typography
                                                            variant="body2"
                                                            component="pre"
                                                            sx={{
                                                                whiteSpace: 'pre-wrap',
                                                                backgroundColor: 'grey.50',
                                                                p: 2,
                                                                borderRadius: 1,
                                                                maxHeight: 300,
                                                                overflow: 'auto',
                                                                fontFamily: 'monospace',
                                                                fontSize: '0.75rem'
                                                            }}
                                                        >
                                                            {debugInfo.outline_parsing_test.parsing_results.raw_response}
                                                        </Typography>
                                                    </AccordionDetails>
                                                </Accordion>
                                            </>
                                        )}
                                    </CardContent>
                                </Card>
                            </>
                        )}

                        {/* Detailed Prompt Information */}
                        <Typography variant="h6" sx={{ mb: 2 }}>
                            Detailed Prompt Information
                        </Typography>
                        {debugInfo.prompts?.map((prompt: any) => (
                            <Accordion key={prompt.id} sx={{ mb: 1 }}>
                                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                                        <Typography variant="subtitle1">
                                            {prompt.agent_name.toUpperCase()} v{prompt.version}
                                        </Typography>
                                        <Chip
                                            label={prompt.is_cached ? 'Cached' : 'Not Cached'}
                                            size="small"
                                            color={prompt.is_cached ? 'success' : 'default'}
                                        />
                                        <Typography variant="body2" color="text.secondary">
                                            {prompt.content_length} chars
                                        </Typography>
                                    </Box>
                                </AccordionSummary>
                                <AccordionDetails>
                                    <Typography
                                        variant="body2"
                                        component="pre"
                                        sx={{
                                            whiteSpace: 'pre-wrap',
                                            backgroundColor: 'grey.50',
                                            p: 2,
                                            borderRadius: 1,
                                            maxHeight: 200,
                                            overflow: 'auto',
                                            fontFamily: 'monospace',
                                            fontSize: '0.75rem'
                                        }}
                                    >
                                        {prompt.content_preview}
                                    </Typography>
                                </AccordionDetails>
                            </Accordion>
                        ))}
                    </Box>
                </>
            )}

            {/* Edit/Add Dialog */}
            <Dialog
                open={dialogOpen}
                onClose={handleClose}
                maxWidth="md"
                fullWidth
            >
                <DialogTitle>
                    {editingPrompt ? 'Edit Prompt' : 'Add New Prompt'}
                </DialogTitle>
                <DialogContent>
                    <TextField
                        fullWidth
                        label="Agent Name"
                        value={agentName}
                        onChange={(e) => setAgentName(e.target.value)}
                        margin="normal"
                        disabled={!!editingPrompt}
                        select={!editingPrompt}
                        SelectProps={!editingPrompt ? {
                            native: true,
                        } : undefined}
                    >
                        {!editingPrompt && (
                            <>
                                <option value="">Select Agent Type</option>
                                {agentTypes.map((type) => (
                                    <option key={type} value={type}>
                                        {type.charAt(0).toUpperCase() + type.slice(1)} Agent
                                    </option>
                                ))}
                            </>
                        )}
                    </TextField>

                    <TextField
                        fullWidth
                        label="Prompt Content"
                        value={promptContent}
                        onChange={(e) => setPromptContent(e.target.value)}
                        margin="normal"
                        multiline
                        rows={15}
                        placeholder="Enter the prompt content for this agent..."
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>Cancel</Button>
                    <Button
                        onClick={handleSave}
                        variant="contained"
                        disabled={loading || !agentName.trim() || !promptContent.trim()}
                    >
                        {editingPrompt ? 'Update' : 'Create'}
                    </Button>
                </DialogActions>
            </Dialog>
        </Paper>
    );
};

export default PromptManager; 