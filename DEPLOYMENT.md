# Deployment Guide

## Prerequisites

- Docker and Docker Compose
- Python 3.9+ (for local development)
- Node.js 18+ (for local development)
- OpenAI or Anthropic API key

## Environment Setup

1. Create a `.env` file in the root directory:

```bash
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
AI_PROVIDER=openai
```

## Local Development

### Backend Setup

```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

pip install -r requirements.txt
uvicorn main:app --reload
```

### Frontend Setup

```bash
cd frontend
npm install
npm start
```

### Redis (Required for job processing)

```bash
docker run -d -p 6379:6379 redis:7-alpine
```

## Docker Deployment

### Development Mode

```bash
# Start all services
docker-compose up --build

# Start in background
docker-compose up -d --build
```

### Production Mode (with PostgreSQL)

```bash
# Start with PostgreSQL
docker-compose --profile production up -d --build
```

## Services

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Redis**: localhost:6379
- **PostgreSQL** (production): localhost:5432

## Configuration

### AI Provider

Set the `AI_PROVIDER` environment variable:
- `openai` - Uses OpenAI GPT-4
- `anthropic` - Uses Claude 3

### Database

For production, update the `DATABASE_URL` environment variable:
```bash
DATABASE_URL=postgresql://user:password@localhost/biography_generator
```

### File Storage

- Local files are stored in `backend/uploads/` and `backend/output/`
- For cloud storage, configure AWS S3 credentials in environment variables

## Scaling for Production

### Horizontal Scaling

1. **Load Balancer**: Add nginx or AWS ALB in front of multiple backend instances
2. **Database**: Use external PostgreSQL with connection pooling
3. **File Storage**: Use AWS S3 or similar cloud storage
4. **Processing Queue**: Use Celery with Redis for async job processing

### Cloud Deployment

#### AWS ECS/Fargate

1. Build and push Docker images to ECR
2. Create ECS task definitions
3. Configure Application Load Balancer
4. Use RDS for database and ElastiCache for Redis

#### Kubernetes

1. Create Kubernetes manifests from Docker Compose
2. Use managed databases (RDS, Cloud SQL)
3. Configure ingress controllers
4. Set up horizontal pod autoscaling

## Monitoring

- Add health checks for all services
- Monitor API response times
- Track job processing success rates
- Set up log aggregation (ELK stack, CloudWatch)

## Security

1. **Environment Variables**: Never commit API keys
2. **HTTPS**: Use SSL certificates in production
3. **Authentication**: Add user authentication for multi-tenant usage
4. **File Validation**: Ensure PDF validation is robust
5. **Rate Limiting**: Implement API rate limiting

## Backup

1. **Database**: Regular backups of biography jobs and prompts
2. **Files**: Backup uploaded PDFs and generated biographies
3. **Configuration**: Version control all configuration files 