# Changelog

## [1.0.2] - 2025-05-29

### Added
- ✅ **Dev script**: Added `"dev": "react-scripts start"` script to `frontend/package.json`
  - Now supports both `npm start` and `npm run dev` commands
  - Convenient alias for developers familiar with `dev` command
  - Both commands do exactly the same thing - start the React development server

## [1.0.1] - 2025-05-29

### Fixed
- ✅ **Package.json script fix**: Removed incorrect `"dev": "react-scripts dev"` script from `frontend/package.json`
  - React (Create React App) doesn't have a `dev` command
  - Kept only the correct scripts: `start`, `build`, `test`, `eject`
  - This resolves potential confusion with npm commands

### Technical Details
- **File**: `frontend/package.json`
- **Change**: Removed line `"dev": "react-scripts dev",`
- **Impact**: Cleaner npm script configuration, no functional changes to application behavior
- **Status**: All services continue to work correctly

---

## [1.0.0] - 2025-05-29

### 🎉 Initial Release
- Complete Personal Biography Generator application
- 4-stage AI pipeline for biography generation
- React frontend with Material-UI
- FastAPI backend with SQLite/PostgreSQL support
- Docker containerization
- Development and deployment scripts 