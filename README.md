# Personal Biography Generator

**🚀 For quick development setup, see [QUICK_START.md](QUICK_START.md)**

## Overview
A scalable application for generating personalized biographies using AI agents. The system processes interview PDFs through a chain of specialized AI agents to create comprehensive biographical books.

## Architecture
- **Backend**: Fast<PERSON>I (Python) - Scalable REST API
- **Frontend**: React + TypeScript - Modern UI with Material-UI
- **Database**: SQLite (local) with PostgreSQL migration support
- **AI Processing**: Chain of 4 specialized agents
- **PDF Processing**: PyPDF2 for input, ReportLab for output
- **Storage**: Local filesystem with cloud storage support
- **Queue**: Redis for async processing

## AI Agents Pipeline
1. **Outline Agent** - Creates biography structure from interview transcript
2. **Writer Agent** - Generates full biography text in <PERSON> style
3. **Evaluation Agent** - Reviews and grades the output quality
4. **Rewrite Agent** - Improves biography based on evaluation feedback

## Features
- 📄 **PDF Upload**: Drag & drop interface for interview PDFs
- 🤖 **AI Processing**: 4-stage AI agent pipeline for quality output
- ⚙️ **Prompt Management**: Editable prompts for each AI agent via UI
- 📊 **Progress Tracking**: Real-time job status and progress monitoring
- 📚 **PDF Generation**: Professional book-style PDF output
- 🔄 **Job Management**: Complete CRUD operations for biography jobs
- 📈 **Scalable**: Ready for cloud deployment and horizontal scaling

## Quick Start

### Prerequisites
- Python 3.9+
- Node.js 18+
- Docker and Docker Compose
- OpenAI or Anthropic API key

### Environment Setup
Create a `.env` file in the root directory:
```bash
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
AI_PROVIDER=openai
```

### Docker Deployment (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd biography-generator

# Start all services
docker-compose up --build

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Local Development

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Setup
```bash
cd frontend
npm install
npm start
```

#### Redis (Required)
```bash
docker run -d -p 6379:6379 redis:7-alpine
```

### Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## Usage

1. **Upload Interview PDF**: Use the drag & drop interface to upload a PDF interview transcript
2. **Enter User Details**: Provide name and optional email
3. **Start Processing**: Click to begin the AI generation pipeline
4. **Monitor Progress**: Watch real-time progress through the 4 AI agents
5. **Download Biography**: Get the generated PDF book when complete
6. **Manage Prompts**: Customize AI agent prompts in the "AI Prompts" tab

## API Documentation

See [API_DOCUMENTATION.md](API_DOCUMENTATION.md) for complete API reference.

### Key Endpoints
- `POST /api/biography/upload` - Upload PDF and create job
- `POST /api/biography/process/{job_id}` - Start processing
- `GET /api/biography/jobs` - List all jobs
- `GET /api/biography/jobs/{job_id}/download` - Download PDF
- `GET /api/prompts/` - Manage AI prompts

## Deployment

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

### Production Deployment
```bash
# With PostgreSQL
docker-compose --profile production up -d --build
```

### Cloud Scaling
The application is designed for cloud deployment with:
- **Container Orchestration**: Docker/Kubernetes ready
- **Database**: PostgreSQL with connection pooling
- **Storage**: AWS S3 integration
- **Processing**: Celery with Redis for async jobs
- **Load Balancing**: Multiple backend instances

## Technology Stack

### Backend
- **Framework**: FastAPI
- **Database**: SQLAlchemy with SQLite/PostgreSQL
- **AI Integration**: OpenAI GPT-4, Anthropic Claude
- **PDF Processing**: PyPDF2, ReportLab
- **Queue**: Redis, Celery (ready)
- **Validation**: Pydantic

### Frontend
- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI (MUI)
- **State Management**: React Query
- **File Upload**: React Dropzone
- **HTTP Client**: Axios

### Infrastructure
- **Containerization**: Docker, Docker Compose
- **Database**: SQLite → PostgreSQL migration path
- **Caching**: Redis
- **Web Server**: Nginx (production)

## Configuration

### AI Providers
- **OpenAI**: GPT-4 Turbo for high-quality generation
- **Anthropic**: Claude 3 Sonnet as alternative
- **Local**: Extension point for local models

### File Limits
- Maximum PDF size: 50MB
- Supported formats: PDF only
- Storage: Local filesystem (S3 ready)

### Database
- Development: SQLite
- Production: PostgreSQL recommended
- Migrations: Alembic ready

## Security Features
- Input validation for all file uploads
- PDF content verification
- Error handling and logging
- Environment-based configuration
- CORS protection
- Ready for authentication integration

## Monitoring & Logging
- Health check endpoints
- Structured error responses
- Processing time tracking
- Job status monitoring
- Ready for external logging systems

### Contributing
1. Fork the repository
2. Create feature branch
3. Follow code style guidelines
4. Add tests for new features
5. Submit pull request

## License
[Add your license here]

## Support
- Check the API documentation at `/docs`
- Review deployment guide in `DEPLOYMENT.md`
- Submit issues for bugs or feature requests 