from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
import asyncio
import threading
import logging

from app.core.config import settings
from app.models.base import engine, Base
from app.routers import biography, prompts
from app.routers import settings as settings_router
from app.services.websocket_manager import manager

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="A scalable application for generating personalized biographies using AI agents",
    debug=settings.debug
)

# Background task for processing WebSocket notifications
notification_task = None

async def notification_processor():
    """Background task to process WebSocket notifications from database."""
    logger = logging.getLogger(__name__)
    logger.info("🔄 Starting WebSocket notification processor...")
    
    while True:
        try:
            await manager.process_pending_notifications()
            await asyncio.sleep(2)  # Check every 2 seconds
        except Exception as e:
            logger.error(f"❌ Error in notification processor: {e}")
            await asyncio.sleep(5)  # Wait longer on error

@app.on_event("startup")
async def startup_event():
    """Start background tasks on application startup."""
    global notification_task
    logger = logging.getLogger(__name__)
    logger.info("🚀 Starting FastAPI application with WebSocket notification processor")
    
    # Start notification processor
    notification_task = asyncio.create_task(notification_processor())

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up background tasks on application shutdown."""
    global notification_task
    logger = logging.getLogger(__name__)
    logger.info("🛑 Shutting down FastAPI application")
    
    if notification_task:
        notification_task.cancel()
        try:
            await notification_task
        except asyncio.CancelledError:
            logger.info("✅ Notification processor stopped")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(biography.router)
app.include_router(prompts.router)
app.include_router(settings_router.router)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": settings.version}

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Personal Biography Generator API",
        "version": settings.version,
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    ) 