#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create SQS queues for the biography generation service.
This script creates the main queue and Dead Letter Queue (DLQ).
"""

import boto3
import json
import sys
from botocore.exceptions import ClientError
from app.core.config import settings

def create_sqs_queues():
    """Create SQS queues for biography processing."""
    
    # Initialize SQS client
    sqs = boto3.client(
        'sqs',
        region_name=settings.aws_region,
        aws_access_key_id=settings.aws_access_key_id,
        aws_secret_access_key=settings.aws_secret_access_key
    )
    
    print(f"Creating SQS queues in region: {settings.aws_region}")
    
    # Queue names
    main_queue_name = "biography-generation-queue.fifo"
    dlq_name = "biography-generation-dlq.fifo"
    
    try:
        # Create Dead Letter Queue first
        print(f"Creating Dead Letter Queue: {dlq_name}")
        dlq_response = sqs.create_queue(
            QueueName=dlq_name,
            Attributes={
                'FifoQueue': 'true',
                'ContentBasedDeduplication': 'true',
                'MessageRetentionPeriod': str(settings.message_retention_period),
                'VisibilityTimeoutSeconds': str(settings.visibility_timeout_seconds),
            }
        )
        dlq_url = dlq_response['QueueUrl']
        print(f"✅ DLQ created: {dlq_url}")
        
        # Get DLQ ARN for main queue configuration
        dlq_attrs = sqs.get_queue_attributes(
            QueueUrl=dlq_url,
            AttributeNames=['QueueArn']
        )
        dlq_arn = dlq_attrs['Attributes']['QueueArn']
        
        # Create main queue with DLQ configuration
        print(f"Creating main queue: {main_queue_name}")
        
        # Dead letter queue redrive policy
        redrive_policy = {
            "deadLetterTargetArn": dlq_arn,
            "maxReceiveCount": settings.max_retries
        }
        
        main_queue_response = sqs.create_queue(
            QueueName=main_queue_name,
            Attributes={
                'FifoQueue': 'true',
                'ContentBasedDeduplication': 'true',
                'MessageRetentionPeriod': str(settings.message_retention_period),
                'VisibilityTimeoutSeconds': str(settings.visibility_timeout_seconds),
                'RedrivePolicy': json.dumps(redrive_policy),
                'DelaySeconds': '0',
                'MaxReceiveCount': str(settings.max_retries)
            }
        )
        main_queue_url = main_queue_response['QueueUrl']
        print(f"✅ Main queue created: {main_queue_url}")
        
        # Print environment variables to set
        print("\n" + "="*60)
        print("🎉 SQS Queues created successfully!")
        print("="*60)
        print("\nAdd these environment variables to your .env file:")
        print(f"SQS_QUEUE_URL={main_queue_url}")
        print(f"SQS_DLQ_QUEUE_URL={dlq_url}")
        print("\nOr set them as environment variables:")
        print(f"export SQS_QUEUE_URL={main_queue_url}")
        print(f"export SQS_DLQ_QUEUE_URL={dlq_url}")
        
        # Save to .env file
        try:
            with open('.env.production', 'a') as f:
                f.write(f"\n# SQS Configuration (auto-generated)\n")
                f.write(f"SQS_QUEUE_URL={main_queue_url}\n")
                f.write(f"SQS_DLQ_QUEUE_URL={dlq_url}\n")
            print(f"\n✅ Environment variables saved to .env.production")
        except Exception as e:
            print(f"⚠️  Could not save to .env.production: {e}")
        
        return main_queue_url, dlq_url
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'QueueAlreadyExists':
            print(f"⚠️  Queues already exist. Retrieving URLs...")
            
            # Get existing queue URLs
            try:
                main_queue_url = sqs.get_queue_url(QueueName=main_queue_name)['QueueUrl']
                dlq_url = sqs.get_queue_url(QueueName=dlq_name)['QueueUrl']
                
                print(f"Main queue URL: {main_queue_url}")
                print(f"DLQ URL: {dlq_url}")
                return main_queue_url, dlq_url
                
            except ClientError as e2:
                print(f"❌ Error retrieving existing queue URLs: {e2}")
                return None, None
        else:
            print(f"❌ Error creating queues: {e}")
            return None, None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None, None

def delete_sqs_queues():
    """Delete SQS queues (for cleanup)."""
    sqs = boto3.client(
        'sqs',
        region_name=settings.aws_region,
        aws_access_key_id=settings.aws_access_key_id,
        aws_secret_access_key=settings.aws_secret_access_key
    )
    
    queue_names = [
        "biography-generation-queue.fifo",
        "biography-generation-dlq.fifo"
    ]
    
    for queue_name in queue_names:
        try:
            queue_url = sqs.get_queue_url(QueueName=queue_name)['QueueUrl']
            sqs.delete_queue(QueueUrl=queue_url)
            print(f"✅ Deleted queue: {queue_name}")
        except ClientError as e:
            if e.response['Error']['Code'] == 'AWS.SimpleQueueService.NonExistentQueue':
                print(f"⚠️  Queue does not exist: {queue_name}")
            else:
                print(f"❌ Error deleting queue {queue_name}: {e}")

def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == 'delete':
        print("🗑️  Deleting SQS queues...")
        delete_sqs_queues()
    else:
        print("🚀 Creating SQS queues for biography generation...")
        
        # Check AWS credentials
        if not settings.aws_access_key_id or not settings.aws_secret_access_key:
            print("❌ AWS credentials not configured!")
            print("Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.")
            sys.exit(1)
        
        create_sqs_queues()

if __name__ == "__main__":
    main() 