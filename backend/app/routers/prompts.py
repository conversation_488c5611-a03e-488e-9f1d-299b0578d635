from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict

from app.models.base import get_db
from app.models.agent_prompt import AgentPrompt
from app.schemas.agent_prompt import AgentPromptCreate, AgentPromptResponse, AgentPromptUpdate
from app.services.prompt_service import PromptService

router = APIRouter(prefix="/api/prompts", tags=["prompts"])

@router.get("/", response_model=List[AgentPromptResponse])
async def get_all_prompts(db: Session = Depends(get_db)):
    """Get all agent prompts."""
    prompts = db.query(AgentPrompt).filter(AgentPrompt.is_active == True).all()
    return prompts

@router.get("/{agent_name}", response_model=AgentPromptResponse)
async def get_prompt_by_agent(agent_name: str, db: Session = Depends(get_db)):
    """Get prompt for a specific agent."""
    prompt = db.query(AgentPrompt).filter(
        AgentPrompt.agent_name == agent_name,
        AgentPrompt.is_active == True
    ).first()
    
    if not prompt:
        raise HTTPException(status_code=404, detail=f"Prompt for agent '{agent_name}' not found")
    
    return prompt

@router.post("/", response_model=AgentPromptResponse)
async def create_prompt(prompt_data: AgentPromptCreate, db: Session = Depends(get_db)):
    """Create a new agent prompt."""
    
    # Check if prompt for this agent already exists
    existing = db.query(AgentPrompt).filter(
        AgentPrompt.agent_name == prompt_data.agent_name
    ).first()
    
    if existing:
        # Deactivate old prompt and increment version
        existing.is_active = False
        new_version = existing.version + 1
    else:
        new_version = 1
    
    # Create new prompt
    prompt = AgentPrompt(
        agent_name=prompt_data.agent_name,
        prompt_content=prompt_data.prompt_content,
        version=new_version,
        is_active=True
    )
    
    db.add(prompt)
    db.commit()
    db.refresh(prompt)
    
    return prompt

@router.put("/{prompt_id}", response_model=AgentPromptResponse)
async def update_prompt(
    prompt_id: str,
    prompt_update: AgentPromptUpdate, 
    db: Session = Depends(get_db)
):
    """Update an existing agent prompt."""
    
    prompt = db.query(AgentPrompt).filter(AgentPrompt.id == prompt_id).first()
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    # Update fields
    if prompt_update.prompt_content is not None:
        prompt.prompt_content = prompt_update.prompt_content
        prompt.version += 1
    
    if prompt_update.is_active is not None:
        prompt.is_active = prompt_update.is_active
    
    db.commit()
    db.refresh(prompt)
    
    return prompt

@router.delete("/{prompt_id}")
async def delete_prompt(prompt_id: str, db: Session = Depends(get_db)):
    """Delete an agent prompt."""
    
    prompt = db.query(AgentPrompt).filter(AgentPrompt.id == prompt_id).first()
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    db.delete(prompt)
    db.commit()
    
    return {"message": "Prompt deleted successfully"}

@router.post("/initialize-defaults")
async def initialize_default_prompts(db: Session = Depends(get_db)):
    """Initialize default prompts for all agents."""
    
    default_prompts = {
        "outline": """You are the "Biography Outliner," a specialized AI whose task is to review a complete interview transcript between the user and our AI Historian. From this transcript, your job is to create a structured outline for a written biography of the user's life.

=========== BACKGROUND CONTEXT ===========
The AI Historian typically interviews users across 12 core chapters:
1) Early Life & Childhood
2) Adolescence & Teenage Years
3) Early Adulthood (College / Career Start)
4) Mid-Life Experiences (Career, Family, Relationships)
5) Major Life Events & Reflections
6) Values, Beliefs, and Worldview
7) Personal Passions, Hobbies, and Interests
8) Reflections on Relationships & Community
9) Life Lessons & Legacy
10) Future Outlook & Aspirations
11) Wrap-Up & Validation
12) Freestyle (Open-Ended)

However, the user may or may not have covered all these chapters in depth. Your task is to adapt to whatever content they actually provided.

=========== OBJECTIVE ===========
1. Read through the entire transcript, focusing on the user's experiences and the chapters/topics they actually covered.
2. Produce a high-level outline (chapters and chapter names) for a written biography that reflects the user's unique life story.
3. Personalize the chapter titles based on specific details from the user's transcript (e.g., "Chapter 1: Growing Up in Texas").
4. Omit or combine chapters that were not discussed or had minimal content.

=========== PROMPT REQUIREMENTS ===========
• Read the user's interview transcript in full.
• Consult the generic structure above as a reference, but only include a chapter if the user has enough content to justify it.
• For each segment the user meaningfully covered, propose a **custom chapter** that reflects the essence, location, or unique experiences discussed.
• If the user spent considerable time on a topic not in the standard interview flow, feel free to create a separate chapter for it.
• Provide each chapter as follows:
  – A **chapter number** (e.g., "Chapter 1," "Chapter 2," etc.).
  – A **descriptive, user-specific title** reflecting key themes or places from the user's story.
  – (Optionally) a **one- to two-sentence summary** or bullet points indicating what that chapter covers.

=========== STYLE AND FORMAT ===========
• Present a concise, user-friendly list of chapters.
• Chapter titles should not be generic if the user offered specific details to personalize them.
• Do not provide the full biography—just the **outline** of chapters.

=========== SAMPLE GUIDANCE ===========
– If the user spoke a lot about early family life in a small seaside town, consider: "Chapter 1: Childhood by the Coast" instead of something generic.
– If they skipped the teenage years or only mentioned them briefly, either omit or combine that content as you see fit.
– If there's a topic that doesn't neatly match a standard chapter but was significant (e.g., a mid-life entrepreneurial venture), create a separate chapter for it.

=========== FINAL INSTRUCTIONS ===========
• After reviewing the transcript, output only the **proposed biography outline** with personalized chapter titles.
• Keep your outline well organized and accurate to the content actually discussed.""",

        "writer": """You are the "Full Biography Writer," tasked with composing a detailed, in-depth biography of a user based on the following that you will be provided with:
1) The complete interview transcript between the user and our AI Historian.
2) The personalized biography outline created by the Biography Outliner.

=========== CONTEXT ON ETERNAL AI ===========
• Eternal AI is a technology company dedicated to capturing and preserving human life stories for future generations. Our "Capsule" concept ensures that these stories, experiences, and personalities can live on in various forms—print biographies, podcasts, digital avatars, and more.

=========== GOALS ===========
1. Write a comprehensive, captivating biography of the user's life, covering all the chapters from the provided outline in a logical, narrative flow.
2. Emulate the engaging, in-depth style of a Walter Isaacson biography—meaning a blend of personal detail, historical or contextual notes, thoughtful analysis, and a lively narrative voice.
3. Infuse the story with detail: don't skim. Retell specific stories and key anecdotes the user shared during the interview. Include direct quotes from the user where relevant, so it reads authentically and with personality.
4. Ensure the final biography remains interesting and never bland. Keep the user's unique experiences front-and-center. If the user has humorous or poignant moments, reflect those in the writing.
5. Don't make the biography too fluffy or lacking substance. Your writing should be filled with meaningful content, not filler.
6. Avoid repetition. Do not restate the same themes or anecdotes unnecessarily. Write it as a flowing narrative without looping back on previously covered material.

=========== PROMPT REQUIREMENTS ===========
• Use the biography outline as your structural guide. Each chapter or section in your final text should correspond to the outline's chapters, though you may rearrange slightly if it serves the narrative flow.
• Integrate details from the interview transcript, weaving in quotes and anecdotes that the user provided.
• Maintain a comprehensive but not repetitive style. You don't need to restate entire blocks of text from the transcript; aim for a narrative retelling that includes the essential facts, emotions, and revelations.
• The tone should be reminiscent of a Walter Isaacson biography: respectful, curious, rich with context, and told in an engaging storytelling voice.
• Do not simply bullet out the user's life; write a cohesive story, with paragraphs and smooth transitions between chapters.
• Feel free to add short transitional or contextual notes if needed, but stay true to the user's experiences.
• Ensure that the written content is TRUE and GROUNDED to the transcript. Do not make things up.

=========== FORMAT SUGGESTION ===========
• Title Page (optional)
• Introduction (optional prologue if you think it improves the flow)
• Chapters in sequential order, following the outline's structure:
   – Chapter # / Title
   – Narrative text with quotes, details, etc.
• Conclusion or final reflection (optional), summarizing the user's overall journey or key takeaways
• Refer to the user's full name in the introduction, but after that refer to them simply by their first name only or last name only
• Don't be repetitive with unique words, for example, "palette" or "tapestry". You can use rich words like that occasionally, but don't repeat them throughout the biography.

=========== ADDITIONAL TIPS ===========
• It's acceptable to incorporate brief background info if the user described historical events or cultural references—but focus on the user's perspective.
• When quoting, attribute it clearly to the user, like:  
  "I always found solace in painting," the user recalled.
• Keep the user's voice and experiences at the heart of the biography. The user is the protagonist, so the narrative revolves around their journey, influences, and transformations.

=========== FINAL INSTRUCTIONS ===========
• Produce the final biography text as a cohesive narrative. 
• Reference the user's direct words by inserting short quotes (and small paraphrases) where beneficial. 
• Write for clarity, depth, and reader engagement—this biography should be a treasured keepsake that readers can enjoy and learn from.""",

        "evaluation": """Ok, now we have the full bio written. I'm going to provide you with the original transcript, as well as the final output for the bio.
Can you please review the bio and grade it on the following:

• Writing style (voice, tone, readability)
• Coverage - The bio covered the most important points from the transcript
• Accuracy - Facts are correct, no obvious hallucinations
• Engagement - The biography is compelling and enjoyable
• Length Appropriateness - the length of the bio seemed appropriate given the length of the underlying transcript
• Redundancy - does the bio repeat itself or redundant in parts of the bio?
• OVERALL QUALITY OF THE BIOGRAPHY - Rate the overall quality of the biography.""",

        "rewrite": """You are the "Rewrite Agent." Your role is to:
1) Review the final biography text produced by the "Full Biography Writer."
2) Read and consider the Evaluation Agent's feedback, which includes comments on style, coverage, accuracy, engagement, length, redundancy, and overall quality.
3) Produce a revised biography that addresses any identified issues or suggestions from the evaluation.

============ INPUTS ============
• The original transcript of the user's interview with the AI Historian, containing their life story.
• The final biography text from Agent 2 (Full Biography Writer).
• The evaluation details from Agent 3 (Evaluation Agent), which highlight strengths, weaknesses, or areas for improvement.

============ OBJECTIVE ============
- Create a new, improved version of the biography, maintaining the same general structure and chapter order but making sure to:
  • (if instructed by the evaluation agent) Incorporate any missing content the evaluator noted.
  • Remove redundancies or repetitive language if mentioned.
  • Fix inaccuracies or factual errors if pointed out.
  • Adjust the length if it's deemed too short or too long.
  • Strengthen readability, engagement, and clarity if requested.
  • (if instructed by the evaluation agent's feedback) Enhance style or variety in word choice, especially if the evaluation called out repeated words.
  • (if instructed by the evaluation agent's feedback) Retain the user's overall story and voice, preserving the authenticity of direct quotes while ensuring the text is cohesive and compelling.

============ GUIDELINES ============
• Preserve the chapters and general organization from the current biography. You may combine or slightly reorder sections only if needed to improve flow, but keep the main structure the same.
• Do not invent new facts. Only use details from the transcript or from the current biography. If the evaluator mentioned missing details, you may weave them in from the original interview transcript if available.
• Maintain the respectful, engaging tone akin to a Walter Isaacson–style biography. However, fix any style critiques the evaluator gave (e.g., if too formal or too casual).
• (if instructed by the evaluation agent's feedback) If a section is flagged as too short, expand it with more detail from the transcript. If it's too long or repetitive, condense or remove extraneous text.
• Keep quotes attributed clearly to the user, but revise them if the evaluation indicates clarity issues or potential misquoting.

============ FORMAT ============
• Provide the final revised biography in paragraphs and chapters, just like a book, with smooth transitions and logical flow.
• Maintain or improve the narrative style. Do not revert to bullet-point summaries or outlines.

============ FINAL INSTRUCTIONS ============
• Output only the **new, fully revised biography**. 
• Ensure all evaluator concerns have been addressed. 
• Make sure the final text is accurate, engaging, and well-polished."""
    }
    
    created_prompts = []
    
    for agent_name, prompt_content in default_prompts.items():
        # Check if prompt already exists
        existing = db.query(AgentPrompt).filter(
            AgentPrompt.agent_name == agent_name
        ).first()
        
        if not existing:
            prompt = AgentPrompt(
                agent_name=agent_name,
                prompt_content=prompt_content,
                is_active=True,
                version=1
            )
            db.add(prompt)
            created_prompts.append(agent_name)
    
    db.commit()
    
    return {
        "message": f"Initialized default prompts for: {', '.join(created_prompts)}" if created_prompts else "Default prompts already exist"
    }

@router.get("/debug", response_model=Dict)
async def debug_prompts(db: Session = Depends(get_db)):
    """Debug endpoint - get detailed information about all active prompts."""
    
    print(f"\n🔍 [PROMPTS DEBUG] Debug endpoint called - fetching all prompt information")
    
    prompt_service = PromptService(db)
    
    # Get all active prompts with detailed info
    active_prompts = db.query(AgentPrompt).filter(AgentPrompt.is_active == True).all()
    
    debug_info = {
        "total_active_prompts": len(active_prompts),
        "cache_status": f"{len(prompt_service._prompt_cache)} prompts cached",
        "prompts": []
    }
    
    for prompt in active_prompts:
        prompt_info = {
            "id": prompt.id,
            "agent_name": prompt.agent_name,
            "version": prompt.version,
            "is_active": prompt.is_active,
            "content_length": len(prompt.prompt_content),
            "content_preview": prompt.prompt_content[:300] + "..." if len(prompt.prompt_content) > 300 else prompt.prompt_content,
            "created_at": getattr(prompt, 'created_at', 'N/A'),
            "is_cached": prompt.agent_name in prompt_service._prompt_cache
        }
        debug_info["prompts"].append(prompt_info)
        
        print(f"   📋 {prompt.agent_name}: v{prompt.version}, {len(prompt.prompt_content)} chars, cached: {prompt.agent_name in prompt_service._prompt_cache}")
    
    # Also test loading each prompt
    debug_info["load_test"] = {}
    for agent_name in ["outline", "writer", "evaluation", "rewrite"]:
        try:
            loaded_prompt = prompt_service.get_prompt(agent_name)
            debug_info["load_test"][agent_name] = {
                "status": "success",
                "length": len(loaded_prompt),
                "preview": loaded_prompt[:100] + "..." if len(loaded_prompt) > 100 else loaded_prompt
            }
        except Exception as e:
            debug_info["load_test"][agent_name] = {
                "status": "error",
                "error": str(e)
            }
    
    print(f"✅ [PROMPTS DEBUG] Debug info collected for {len(active_prompts)} prompts")
    
    return debug_info

@router.post("/clear-cache")
async def clear_prompt_cache(db: Session = Depends(get_db)):
    """Clear the prompt cache to force reload from database."""
    
    print(f"\n🗑️ [PROMPTS DEBUG] Clearing prompt cache...")
    
    prompt_service = PromptService(db)
    cache_size_before = len(prompt_service._prompt_cache)
    
    prompt_service.clear_cache()
    
    print(f"✅ [PROMPTS DEBUG] Cache cleared - {cache_size_before} prompts removed")
    
    return {
        "message": f"Prompt cache cleared - {cache_size_before} cached prompts removed",
        "cache_size_before": cache_size_before,
        "cache_size_after": len(prompt_service._prompt_cache)
    }

@router.post("/test-outline-parsing")
async def test_outline_parsing(
    request: dict,
    db: Session = Depends(get_db)
):
    """Test endpoint for debugging outline parsing with different models."""
    
    print(f"\n🧪 [OUTLINE TEST] Starting outline parsing test...")
    
    # Get test parameters
    model = request.get('model', 'gpt-3.5-turbo')
    sample_text = request.get('sample_text', 'Test interview about childhood, career, and family life.')
    target_chapters = request.get('target_chapters', 8)
    
    print(f"🔧 [OUTLINE TEST] Using model: {model}")
    print(f"📝 [OUTLINE TEST] Sample text length: {len(sample_text)} chars")
    print(f"📚 [OUTLINE TEST] Target chapters: {target_chapters}")
    
    try:
        # Get outline prompt from database
        prompt_service = PromptService(db)
        outline_prompt = prompt_service.get_prompt("outline")
        
        print(f"📋 [OUTLINE TEST] Using outline prompt ({len(outline_prompt)} chars)")
        
        # Create AI service instance
        from app.services.ai_service import AIService
        ai_service = AIService(db)
        
        # Show which model will be used
        current_model = ai_service.get_current_model()
        print(f"🤖 [OUTLINE TEST] AI Service will use model: {current_model}")
        
        # Generate test system message
        system_message = f"""You are an expert biography outliner creating a {target_chapters}-chapter structure.
        
        CRITICAL: Respond with ONLY valid JSON in this exact format:
        {{
            "outline": "brief description",
            "chapters": [
                {{
                    "number": 1,
                    "title": "Chapter Title",
                    "description": "What this chapter covers",
                    "key_themes": ["theme1", "theme2"],
                    "estimated_sections": 3,
                    "time_period": "childhood/early years/etc"
                }}
            ],
            "chapter_count": {target_chapters}
        }}"""
        
        # Create test prompt
        full_prompt = f"""
{outline_prompt[:1000]}

=========== INTERVIEW TRANSCRIPT (TEST) ===========
{sample_text}

Create a {target_chapters}-chapter biography outline. Respond with ONLY the JSON format specified.
"""
        
        print(f"🚀 [OUTLINE TEST] Generating outline with {model}...")
        
        # Generate outline
        response = await ai_service.generate_completion(
            full_prompt, system_message, max_tokens=2000
        )
        
        print(f"📥 [OUTLINE TEST] Received response ({len(response)} chars)")
        print(f"📄 [OUTLINE TEST] Response preview: {response[:300]}...")
        
        # Test parsing
        import re
        import json
        
        parsing_results = {
            "raw_response": response,
            "response_length": len(response),
            "parsing_attempts": []
        }
        
        # Try different parsing patterns
        json_patterns = [
            (r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', "Nested JSON pattern"),
            (r'```json\s*(\{.*?\})\s*```', "Markdown JSON block"),
            (r'```\s*(\{.*?\})\s*```', "Generic code block"),
            (r'(\{.*?\})', "Simple JSON match")
        ]
        
        for pattern, pattern_name in json_patterns:
            attempt = {"pattern": pattern_name, "success": False}
            
            matches = re.findall(pattern, response, re.DOTALL)
            if matches:
                attempt["matches_found"] = len(matches)
                json_content = matches[0] if isinstance(matches[0], str) else matches[0]
                attempt["json_preview"] = json_content[:200]
                
                try:
                    parsed_data = json.loads(json_content)
                    attempt["success"] = True
                    attempt["parsed_keys"] = list(parsed_data.keys())
                    if 'chapters' in parsed_data:
                        attempt["chapters_found"] = len(parsed_data['chapters'])
                    
                    # Store successful result
                    parsing_results["successful_parse"] = parsed_data
                    
                except json.JSONDecodeError as e:
                    attempt["json_error"] = str(e)
            else:
                attempt["matches_found"] = 0
            
            parsing_results["parsing_attempts"].append(attempt)
        
        print(f"✅ [OUTLINE TEST] Test completed")
        
        return {
            "test_parameters": {
                "model": model,
                "sample_text_length": len(sample_text),
                "target_chapters": target_chapters
            },
            "parsing_results": parsing_results,
            "success": any(attempt["success"] for attempt in parsing_results["parsing_attempts"])
        }
        
    except Exception as e:
        print(f"❌ [OUTLINE TEST] Test failed: {e}")
        return {
            "error": str(e),
            "test_parameters": {
                "model": model,
                "sample_text_length": len(sample_text),
                "target_chapters": target_chapters
            },
            "success": False
        }

@router.post("/clean-name-tags")
async def clean_name_tags_in_content(
    request: dict,
    db: Session = Depends(get_db)
):
    """Clean malformed name tags in existing content."""
    try:
        content = request.get("content", "")
        person_name = request.get("person_name", "Unknown Person")
        
        if not content:
            raise HTTPException(status_code=400, detail="Content is required")
        
        # Use enhanced service for cleanup
        from app.services.enhanced_progressive_service import EnhancedProgressiveBiographyService
        service = EnhancedProgressiveBiographyService(db)
        
        # Clean the content
        cleaned_content = service._replace_name_tags(content, person_name)
        final_content = service._clean_content_issues(cleaned_content, person_name)
        
        # Analyze the changes
        import re
        original_tags = re.findall(r'\{[A-Z_]+\}', content)
        remaining_tags = re.findall(r'\{[A-Z_]+\}', final_content)
        
        # Find placeholders that were cleaned
        original_placeholders = re.findall(r'\[[A-Za-z/]+\]', content)
        remaining_placeholders = re.findall(r'\[[A-Za-z/]+\]', final_content)
        
        return {
            "success": True,
            "original_content": content,
            "cleaned_content": final_content,
            "analysis": {
                "original_tags": original_tags,
                "remaining_tags": remaining_tags,
                "tags_cleaned": len(original_tags) - len(remaining_tags),
                "original_placeholders": original_placeholders,
                "remaining_placeholders": remaining_placeholders,
                "placeholders_cleaned": len(original_placeholders) - len(remaining_placeholders),
                "person_name": person_name,
                "content_length_change": len(final_content) - len(content)
            }
        }
        
    except Exception as e:
        logger.error(f"Error cleaning name tags: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clean name tags: {str(e)}") 