from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.models.base import get_db
from app.models.app_settings import AppSettings
from app.schemas.app_settings import (
    AppSettingCreate, 
    AppSettingResponse, 
    AppSettingUpdate,
    OpenAIModelSettings,
    AIProviderSettings
)

router = APIRouter(prefix="/api/settings", tags=["settings"])

# Constants for AI providers and models
# Based on https://ai.google.dev/gemini-api/docs/models
PROVIDER_MODELS = {
    "openai": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-turbo-preview", "gpt-4o", "gpt-4o-mini"],
    "anthropic": ["claude-3-haiku-20240307", "claude-3-sonnet-20240229", "claude-3-opus-20240229"],
    "gemini": [
        "gemini-2.5-pro",                        # Most powerful thinking model  
        "gemini-2.5-flash",                      # Best price-performance
        "gemini-2.5-flash-lite-preview-06-17",  # Cost-efficient (experimental)
        "gemini-2.0-flash",                      # Next generation features
        "gemini-2.0-flash-lite",                 # Cost efficiency and low latency
        "gemini-1.5-flash",                      # Fast and versatile
        "gemini-1.5-flash-8b",                   # High performance with efficiency  
        "gemini-1.5-pro"                        # Complex reasoning tasks
    ],
    "grok": ["grok-beta", "grok-vision-beta"]
}

PROVIDER_DEFAULTS = {
    "openai": "gpt-3.5-turbo",
    "anthropic": "claude-3-haiku-20240307", 
    "gemini": "gemini-2.5-flash",  # Use stable flash model as default
    "grok": "grok-beta"
}

PROVIDER_DESCRIPTIONS = {
    "openai": "OpenAI GPT models - General purpose, reliable",
    "anthropic": "Anthropic Claude models - Safety-focused, thoughtful responses", 
    "gemini": "Google Gemini models - Large context window (up to 2M tokens), excellent for complex tasks",
    "grok": "xAI Grok models - Real-time information, conversational"
}

@router.get("/openai-models", response_model=OpenAIModelSettings)
async def get_openai_model_settings(db: Session = Depends(get_db)):
    """Get current model settings for any AI provider."""
    # Get current model setting
    model_setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "openai_model",
        AppSettings.is_active == True
    ).first()
    
    # Get current provider from database
    provider_setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "ai_provider",
        AppSettings.is_active == True
    ).first()
    
    current_provider = provider_setting.setting_value if provider_setting else "openai"
    
    current_model = model_setting.setting_value if model_setting else PROVIDER_DEFAULTS.get(current_provider, "gpt-3.5-turbo")
    available_models = PROVIDER_MODELS.get(current_provider, PROVIDER_MODELS["openai"])
    
    return OpenAIModelSettings(
        openai_model=current_model,
        available_models=available_models
    )

@router.post("/openai-model")
async def update_openai_model(
    model_name: str,
    db: Session = Depends(get_db)
):
    """Update the model setting for any AI provider."""
    
    # Get current provider from database
    provider_setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "ai_provider",
        AppSettings.is_active == True
    ).first()
    
    current_provider = provider_setting.setting_value if provider_setting else "openai"
    
    # Get valid models for current provider
    valid_models = PROVIDER_MODELS.get(current_provider, [])
    
    # Also allow cross-provider OpenAI models for backward compatibility (mapping)
    if current_provider != "openai":
        valid_models.extend(["gpt-3.5-turbo", "gpt-4", "gpt-4o"])
    
    if model_name not in valid_models:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid model for {current_provider} provider. Must be one of: {', '.join(valid_models)}"
        )
    
    # Get or create setting
    setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "openai_model"
    ).first()
    
    if setting:
        setting.setting_value = model_name
        setting.is_active = True
    else:
        setting = AppSettings(
            setting_key="openai_model",
            setting_value=model_name,
            description="Currently selected OpenAI model for biography generation",
            is_active=True
        )
        db.add(setting)
    
    db.commit()
    db.refresh(setting)
    
    return {
        "message": f"OpenAI model updated to {model_name}",
        "model": model_name
    }

@router.get("/ai-providers", response_model=AIProviderSettings)
async def get_ai_provider_settings(db: Session = Depends(get_db)):
    """Get current AI provider settings."""
    # Get current provider setting
    provider_setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "ai_provider",
        AppSettings.is_active == True
    ).first()
    
    current_provider = provider_setting.setting_value if provider_setting else "openai"
    
    # Check which providers have valid API keys
    from app.core.config import settings as app_settings
    
    available_providers = []
    provider_status = {}
    
    if app_settings.openai_api_key:
        available_providers.append("openai")
        provider_status["openai"] = "Available"
    else:
        provider_status["openai"] = "API key missing"
    
    if app_settings.anthropic_api_key:
        available_providers.append("anthropic") 
        provider_status["anthropic"] = "Available"
    else:
        provider_status["anthropic"] = "API key missing"
    
    if app_settings.gemini_api_key:
        available_providers.append("gemini")
        provider_status["gemini"] = "Available"
    else:
        provider_status["gemini"] = "API key missing"
    
    if app_settings.grok_api_key:
        available_providers.append("grok")
        provider_status["grok"] = "Available"
    else:
        provider_status["grok"] = "API key missing"
    
    return AIProviderSettings(
        ai_provider=current_provider,
        available_providers=available_providers,
        provider_status=provider_status,
        provider_models=PROVIDER_MODELS,
        provider_descriptions=PROVIDER_DESCRIPTIONS
    )

@router.post("/ai-provider")
async def update_ai_provider(
    provider_name: str,
    db: Session = Depends(get_db)
):
    """Update the AI provider setting."""
    
    valid_providers = ["openai", "anthropic", "gemini", "grok"]
    
    if provider_name not in valid_providers:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid provider. Must be one of: {', '.join(valid_providers)}"
        )
    
    # Check if API key is available for this provider
    from app.core.config import settings as app_settings
    
    provider_keys = {
        "openai": app_settings.openai_api_key,
        "anthropic": app_settings.anthropic_api_key,
        "gemini": app_settings.gemini_api_key,
        "grok": app_settings.grok_api_key
    }
    
    if not provider_keys.get(provider_name):
        raise HTTPException(
            status_code=400,
            detail=f"API key not configured for {provider_name} provider"
        )
    
    # Get or create setting
    setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "ai_provider"
    ).first()
    
    if setting:
        setting.setting_value = provider_name
        setting.is_active = True
    else:
        setting = AppSettings(
            setting_key="ai_provider",
            setting_value=provider_name,
            description="Currently selected AI provider for biography generation",
            is_active=True
        )
        db.add(setting)
    
    db.commit()
    db.refresh(setting)
    
    return {
        "message": f"AI provider updated to {provider_name}",
        "provider": provider_name
    }

@router.get("/", response_model=List[AppSettingResponse])
async def get_all_settings(db: Session = Depends(get_db)):
    """Get all application settings."""
    settings = db.query(AppSettings).filter(AppSettings.is_active == True).all()
    return settings

@router.post("/", response_model=AppSettingResponse)
async def create_setting(
    setting_data: AppSettingCreate,
    db: Session = Depends(get_db)
):
    """Create a new application setting."""
    
    # Check if setting already exists
    existing = db.query(AppSettings).filter(
        AppSettings.setting_key == setting_data.setting_key
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=400,
            detail=f"Setting with key '{setting_data.setting_key}' already exists"
        )
    
    setting = AppSettings(
        setting_key=setting_data.setting_key,
        setting_value=setting_data.setting_value,
        description=setting_data.description,
        is_active=True
    )
    
    db.add(setting)
    db.commit()
    db.refresh(setting)
    
    return setting

@router.put("/{setting_id}", response_model=AppSettingResponse)
async def update_setting(
    setting_id: str,
    setting_update: AppSettingUpdate,
    db: Session = Depends(get_db)
):
    """Update an existing setting."""
    
    setting = db.query(AppSettings).filter(AppSettings.id == setting_id).first()
    if not setting:
        raise HTTPException(status_code=404, detail="Setting not found")
    
    setting.setting_value = setting_update.setting_value
    if setting_update.description is not None:
        setting.description = setting_update.description
    
    db.commit()
    db.refresh(setting)
    
    return setting 