from sqlalchemy import Column, String, DateTime, Boolean, Text, func
from sqlalchemy.dialects.postgresql import UUID
from app.models.base import Base
import uuid


class WebSocketNotification(Base):
    """Model for storing WebSocket notifications for inter-process communication."""
    __tablename__ = "websocket_notifications"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    job_id = Column(String(36), nullable=False, index=True)
    notification_type = Column(String(50), default="job_update", nullable=False)
    payload = Column(Text, nullable=True)  # JSON data for the notification
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed = Column(Boolean, default=False)
    processed_at = Column(DateTime(timezone=True), nullable=True) 