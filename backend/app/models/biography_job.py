from sqlalchemy import Column, Integer, String, DateTime, Text, Enum, Float, JSON
from sqlalchemy.sql import func
from .base import Base
import enum
import uuid

class JobStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    OUTLINE_COMPLETE = "outline_complete"
    ITERATIVE_WRITING = "iterative_writing"
    WRITING_COMPLETE = "writing_complete"
    EVALUATION_COMPLETE = "evaluation_complete"
    REWRITE_COMPLETE = "rewrite_complete"
    GENERATING_PDF = "generating_pdf"
    COMPLETED = "completed"
    FAILED = "failed"

class BiographyJob(Base):
    __tablename__ = "biography_jobs"
    
    id = Column(String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_name = Column(String(255), nullable=False)
    email = Column(String(255), nullable=True)
    
    # File paths
    uploaded_pdf_path = Column(String(500), nullable=False)
    output_pdf_path = Column(String(500), nullable=True)
    
    # Job status and progress
    status = Column(Enum(JobStatus), default=JobStatus.PENDING, nullable=False)
    progress_percentage = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)
    
    # Agent outputs
    outline_content = Column(Text, nullable=True)
    biography_content = Column(Text, nullable=True)
    evaluation_content = Column(Text, nullable=True)
    final_biography_content = Column(Text, nullable=True)
    
    # New fields for iterative generation
    chapters_content = Column(JSON, nullable=True)
    chapter_theses = Column(JSON, nullable=True)
    generation_iterations = Column(JSON, nullable=True)
    total_chapters = Column(Integer, default=0)
    completed_chapters = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Processing time tracking
    processing_time_seconds = Column(Float, nullable=True) 