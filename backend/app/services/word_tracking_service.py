"""
Word Tracking Service for Biography Generation

This service tracks word usage across the entire biography generation process
to prevent overuse of specific words and phrases.
"""

import re
import logging
from typing import Dict, List, Set, Optional
from collections import Counter, defaultdict
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

class WordTrackingService:
    """Service for tracking word usage across biography generation."""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.word_counts = Counter()
        self.phrase_counts = Counter()
        self.total_words = 0
        
        # Words to track for overuse
        self.tracked_words = {
            'tapestry', 'mosaic', 'journey', 'palette', 'woven', 'painted', 
            'canvas', 'thread', 'fabric', 'brushstroke', 'symphony', 
            'orchestra', 'melody', 'harmony', 'rhythm', 'dance', 'choreography',
            'sculpture', 'masterpiece', 'portrait', 'landscape', 'vista',
            'horizon', 'beacon', 'lighthouse', 'anchor', 'compass', 'map',
            'treasure', 'gem', 'jewel', 'diamond', 'pearl', 'gold',
            'silver', 'bronze', 'crystal', 'prism', 'rainbow', 'spectrum'
        }
        
        # Phrases to track for overuse
        self.tracked_phrases = {
            'rich tapestry', 'colorful mosaic', 'life\'s journey', 
            'vibrant palette', 'carefully woven', 'beautifully painted',
            'blank canvas', 'golden thread', 'fabric of life',
            'masterful brushstroke', 'symphony of life', 'orchestrated life',
            'melodic journey', 'harmonious blend', 'rhythmic dance',
            'choreographed life', 'sculpted experience', 'living masterpiece',
            'portrait of life', 'landscape of memories', 'vast vista',
            'distant horizon', 'guiding beacon', 'steady lighthouse',
            'firm anchor', 'moral compass', 'roadmap of life',
            'hidden treasure', 'precious gem', 'sparkling jewel',
            'brilliant diamond', 'lustrous pearl', 'golden opportunity',
            'silver lining', 'bronze medal', 'crystal clear',
            'prismatic view', 'rainbow of emotions', 'full spectrum'
        }
        
        # Thresholds for overuse warnings
        self.word_threshold_per_1000 = 2.0  # Max 2 occurrences per 1000 words
        self.phrase_threshold_total = 3  # Max 3 total occurrences of any phrase
        
    def update_word_counts(self, text: str) -> None:
        """Update word and phrase counts with new text."""
        
        if not text:
            return
            
        # Extract words
        words = re.findall(r'\b\w+\b', text.lower())
        self.total_words += len(words)
        
        # Count tracked words
        for word in words:
            if word in self.tracked_words:
                self.word_counts[word] += 1
        
        # Count tracked phrases
        text_lower = text.lower()
        for phrase in self.tracked_phrases:
            count = len(re.findall(re.escape(phrase), text_lower))
            if count > 0:
                self.phrase_counts[phrase] += count
                
        logger.debug(f"Updated word tracking for job {self.job_id[:8]}: {len(words)} words processed")
    
    def get_overused_words(self) -> Dict[str, Dict]:
        """Get words that are being overused."""
        
        overused = {}
        
        if self.total_words == 0:
            return overused
            
        for word, count in self.word_counts.items():
            frequency_per_1000 = (count / self.total_words) * 1000
            
            if frequency_per_1000 > self.word_threshold_per_1000:
                overused[word] = {
                    'count': count,
                    'frequency_per_1000': frequency_per_1000,
                    'threshold': self.word_threshold_per_1000,
                    'severity': 'high' if frequency_per_1000 > self.word_threshold_per_1000 * 2 else 'medium'
                }
        
        return overused
    
    def get_overused_phrases(self) -> Dict[str, Dict]:
        """Get phrases that are being overused."""
        
        overused = {}
        
        for phrase, count in self.phrase_counts.items():
            if count > self.phrase_threshold_total:
                overused[phrase] = {
                    'count': count,
                    'threshold': self.phrase_threshold_total,
                    'severity': 'high' if count > self.phrase_threshold_total * 2 else 'medium'
                }
        
        return overused
    
    def get_word_alternatives(self, word: str) -> List[str]:
        """Get alternative words to replace overused words."""
        
        alternatives = {
            'tapestry': ['narrative', 'story', 'composition', 'blend', 'weaving', 'pattern', 'fabric'],
            'mosaic': ['collection', 'array', 'combination', 'mixture', 'assembly', 'compilation', 'medley'],
            'journey': ['experience', 'progression', 'development', 'evolution', 'path', 'adventure', 'voyage'],
            'palette': ['range', 'spectrum', 'variety', 'selection', 'array', 'collection', 'assortment'],
            'woven': ['integrated', 'combined', 'blended', 'merged', 'connected', 'intertwined', 'unified'],
            'painted': ['created', 'formed', 'shaped', 'crafted', 'built', 'developed', 'constructed'],
            'canvas': ['backdrop', 'setting', 'foundation', 'background', 'stage', 'platform', 'framework'],
            'thread': ['element', 'aspect', 'component', 'strand', 'part', 'feature', 'detail'],
            'fabric': ['structure', 'foundation', 'essence', 'core', 'framework', 'basis', 'substance'],
            'brushstroke': ['touch', 'detail', 'element', 'aspect', 'feature', 'nuance', 'flourish'],
            'symphony': ['harmony', 'composition', 'arrangement', 'orchestration', 'blend', 'coordination'],
            'orchestra': ['ensemble', 'group', 'collection', 'assembly', 'gathering', 'coordination'],
            'melody': ['tune', 'rhythm', 'flow', 'cadence', 'pattern', 'sequence', 'progression'],
            'harmony': ['balance', 'unity', 'accord', 'agreement', 'coordination', 'synchronization'],
            'rhythm': ['pattern', 'flow', 'cadence', 'tempo', 'pace', 'beat', 'sequence'],
            'dance': ['movement', 'flow', 'progression', 'sequence', 'choreography', 'performance'],
            'choreography': ['arrangement', 'coordination', 'organization', 'orchestration', 'planning'],
            'sculpture': ['creation', 'formation', 'construction', 'development', 'crafting'],
            'masterpiece': ['achievement', 'accomplishment', 'creation', 'work', 'triumph', 'success'],
            'portrait': ['picture', 'image', 'representation', 'depiction', 'illustration', 'view'],
            'landscape': ['view', 'scene', 'panorama', 'vista', 'setting', 'environment', 'backdrop'],
            'vista': ['view', 'panorama', 'scene', 'perspective', 'outlook', 'prospect', 'horizon'],
            'horizon': ['future', 'prospect', 'outlook', 'vista', 'boundary', 'limit', 'edge'],
            'beacon': ['guide', 'light', 'signal', 'marker', 'indicator', 'sign', 'landmark'],
            'lighthouse': ['guide', 'beacon', 'landmark', 'marker', 'signal', 'reference point'],
            'anchor': ['foundation', 'base', 'support', 'stability', 'grounding', 'cornerstone'],
            'compass': ['guide', 'direction', 'guidance', 'orientation', 'navigation', 'reference'],
            'map': ['guide', 'plan', 'blueprint', 'outline', 'framework', 'structure', 'layout'],
            'treasure': ['gift', 'blessing', 'value', 'asset', 'prize', 'reward', 'benefit'],
            'gem': ['treasure', 'jewel', 'prize', 'asset', 'value', 'blessing', 'gift'],
            'jewel': ['treasure', 'gem', 'prize', 'asset', 'value', 'blessing', 'gift'],
            'diamond': ['treasure', 'gem', 'jewel', 'prize', 'asset', 'value', 'precious thing'],
            'pearl': ['treasure', 'gem', 'jewel', 'prize', 'asset', 'value', 'precious thing'],
            'gold': ['treasure', 'value', 'worth', 'richness', 'wealth', 'abundance'],
            'silver': ['value', 'worth', 'brightness', 'clarity', 'distinction', 'honor'],
            'bronze': ['achievement', 'accomplishment', 'recognition', 'honor', 'distinction'],
            'crystal': ['clarity', 'transparency', 'purity', 'brilliance', 'precision'],
            'prism': ['lens', 'perspective', 'viewpoint', 'angle', 'dimension', 'facet'],
            'rainbow': ['spectrum', 'range', 'variety', 'diversity', 'array', 'collection'],
            'spectrum': ['range', 'variety', 'array', 'collection', 'diversity', 'breadth']
        }
        
        return alternatives.get(word.lower(), [])
    
    def suggest_replacement_text(self, text: str, max_replacements: int = 3) -> str:
        """Suggest replacements for overused words in the given text."""
        
        overused_words = self.get_overused_words()
        if not overused_words:
            return text
        
        # Sort by severity and frequency
        sorted_overused = sorted(
            overused_words.items(),
            key=lambda x: (x[1]['severity'] == 'high', x[1]['frequency_per_1000']),
            reverse=True
        )
        
        replacement_text = text
        replacements_made = 0
        
        for word, info in sorted_overused[:max_replacements]:
            if replacements_made >= max_replacements:
                break
                
            alternatives = self.get_word_alternatives(word)
            if not alternatives:
                continue
            
            # Find occurrences in the text
            pattern = r'\b' + re.escape(word) + r'\b'
            matches = list(re.finditer(pattern, replacement_text, re.IGNORECASE))
            
            if matches:
                # Replace the first occurrence with an alternative
                match = matches[0]
                alternative = alternatives[0]  # Use first alternative
                
                # Preserve capitalization
                original = match.group()
                if original[0].isupper():
                    alternative = alternative.capitalize()
                
                start, end = match.span()
                replacement_text = replacement_text[:start] + alternative + replacement_text[end:]
                replacements_made += 1
                
                logger.info(f"Suggested replacement: '{original}' → '{alternative}'")
        
        return replacement_text
    
    def get_usage_report(self) -> Dict:
        """Get a comprehensive usage report."""
        
        return {
            'total_words': self.total_words,
            'tracked_word_counts': dict(self.word_counts),
            'tracked_phrase_counts': dict(self.phrase_counts),
            'overused_words': self.get_overused_words(),
            'overused_phrases': self.get_overused_phrases(),
            'word_frequency_per_1000': {
                word: (count / self.total_words) * 1000 if self.total_words > 0 else 0
                for word, count in self.word_counts.items()
            }
        }
    
    def reset_tracking(self) -> None:
        """Reset all tracking data."""
        
        self.word_counts.clear()
        self.phrase_counts.clear()
        self.total_words = 0
        logger.info(f"Reset word tracking for job {self.job_id[:8]}")


class GlobalWordTracker:
    """Global tracker for managing word tracking across multiple jobs."""
    
    _trackers: Dict[str, WordTrackingService] = {}
    
    @classmethod
    def get_tracker(cls, job_id: str) -> WordTrackingService:
        """Get or create a word tracker for a job."""
        
        if job_id not in cls._trackers:
            cls._trackers[job_id] = WordTrackingService(job_id)
            logger.info(f"Created new word tracker for job {job_id[:8]}")
        
        return cls._trackers[job_id]
    
    @classmethod
    def remove_tracker(cls, job_id: str) -> None:
        """Remove a word tracker for a completed job."""
        
        if job_id in cls._trackers:
            del cls._trackers[job_id]
            logger.info(f"Removed word tracker for job {job_id[:8]}")
    
    @classmethod
    def get_active_trackers(cls) -> List[str]:
        """Get list of active tracker job IDs."""
        
        return list(cls._trackers.keys())
