from typing import Optional
import asyncio
import openai
import anthropic
import google.generativeai as genai
from groq import Groq
import os
from app.core.config import settings
import time
from sqlalchemy.orm import Session
from app.services.prompt_service import PromptService
from app.models.app_settings import AppSettings

class AIService:
    def __init__(self, db: Session = None):
        self.provider = self._get_current_provider(db) or settings.ai_provider
        self.db = db
        self.prompt_service = PromptService(db) if db else None
        
        print(f"🤖 AI Service: Using provider '{self.provider}' (from {'database' if self._get_current_provider(db) else 'config'})")
        
        # Force settings to take precedence over environment variables
        # Clear any environment variables that might interfere
        env_vars_to_clear = ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY', 'GEMINI_API_KEY', 'GROK_API_KEY']
        for var in env_vars_to_clear:
            if var in os.environ:
                print(f"🔧 AI Service: Removing {var} environment variable to use .env settings")
                del os.environ[var]
        
        if self.provider == "openai" and settings.openai_api_key:
            self.client = openai.OpenAI(api_key=settings.openai_api_key)
            print(f"🔑 AI Service: Using OpenAI API key from .env file (ending with: ...{settings.openai_api_key[-6:]})")
        elif self.provider == "anthropic" and settings.anthropic_api_key:
            self.client = anthropic.Anthropic(api_key=settings.anthropic_api_key)
            print(f"🔑 AI Service: Using Anthropic API key from .env file (ending with: ...{settings.anthropic_api_key[-6:]})")
        elif self.provider == "gemini" and settings.gemini_api_key:
            genai.configure(api_key=settings.gemini_api_key)
            # Get the selected model from database or use default
            selected_model = self.get_current_model()
            self.client = genai.GenerativeModel(selected_model)
            print(f"🔑 AI Service: Using Gemini API key from .env file (ending with: ...{settings.gemini_api_key[-6:]})")
            print(f"🤖 AI Service: Initialized Gemini with model: {selected_model}")
        elif self.provider == "grok" and settings.grok_api_key:
            self.client = Groq(api_key=settings.grok_api_key)
            print(f"🔑 AI Service: Using Grok API key from .env file (ending with: ...{settings.grok_api_key[-6:]})")
        else:
            print(f"❌ AI Service: No valid API key found for provider: {self.provider}")
            print(f"   OpenAI key present: {bool(settings.openai_api_key)}")
            print(f"   Anthropic key present: {bool(settings.anthropic_api_key)}")
            print(f"   Gemini key present: {bool(settings.gemini_api_key)}")
            print(f"   Grok key present: {bool(settings.grok_api_key)}")
            raise ValueError(f"No valid API key found for provider: {self.provider}")
    
    def _get_current_provider(self, db: Session = None) -> Optional[str]:
        """Get the currently selected AI provider from database settings."""
        if not db:
            return None
        
        try:
            provider_setting = db.query(AppSettings).filter(
                AppSettings.setting_key == "ai_provider",
                AppSettings.is_active == True
            ).first()
            
            if provider_setting:
                return provider_setting.setting_value
            return None
                
        except Exception as e:
            print(f"❌ [AI DEBUG] Error getting provider from DB: {e}")
            return None

    def get_current_model(self) -> str:
        """Get the currently selected AI model from database settings."""
        if not self.db:
            print(f"⚠️ [AI DEBUG] No database session available, using default model")
            return self._get_default_model()
        
        try:
            # Get current model setting from database
            model_setting = self.db.query(AppSettings).filter(
                AppSettings.setting_key == "openai_model",
                AppSettings.is_active == True
            ).first()
            
            if model_setting:
                selected_model = model_setting.setting_value
                print(f"✅ [AI DEBUG] model ==> Using user-selected model from DB: {selected_model}")
                return selected_model
            else:
                def_model = self._get_default_model()
                print(f"⚠️ [AI DEBUG] model ==> No model setting found in DB, using default: {def_model}")
                return def_model
                
        except Exception as e:
            print(f"❌ [AI DEBUG] model ==> Error getting model from DB: {e}, using default")
            return self._get_default_model()
    
    def _get_default_model(self) -> str:
        """Get default model based on provider."""
        if self.provider == "openai":
            return "gpt-3.5-turbo"
        elif self.provider == "anthropic":
            return "claude-3-haiku-20240307"
        elif self.provider == "gemini":
            return "gemini-2.5-flash"  # Use stable flash model as default
        elif self.provider == "grok":
            return "grok-beta"
        else:
            return "gpt-3.5-turbo"  # Fallback
    
    def _map_to_anthropic_model(self, model_name: str) -> str:
        """Map OpenAI model names to Anthropic equivalents when using Anthropic provider."""
        # If already an Anthropic model, return as-is
        if model_name.startswith("claude"):
            return model_name
        
        # Map OpenAI models to Anthropic equivalents based on capability level
        model_mapping = {
            "gpt-3.5-turbo": "claude-3-haiku-20240307",
            "gpt-3.5-turbo-16k": "claude-3-haiku-20240307",
            "gpt-4": "claude-3-sonnet-20240229", 
            "gpt-4-turbo-preview": "claude-3-sonnet-20240229",
            "gpt-4o": "claude-3-opus-20240229",
            "gpt-4o-mini": "claude-3-haiku-20240307"
        }
        
        mapped_model = model_mapping.get(model_name, "claude-3-haiku-20240307")
        if mapped_model != model_name:
            print(f"🔄 [AI DEBUG] Mapped OpenAI model '{model_name}' to Anthropic model '{mapped_model}'")
        
        return mapped_model
    
    def get_model_output_limit(self, model_name: str = None) -> int:
        """Get the maximum output token limit for a specific model based on official Google AI documentation."""
        if not model_name:
            model_name = self.get_current_model()
        
        # Gemini 2.5 models - significantly higher output limits
        if model_name == "gemini-2.5-pro":
            return 65535     # Maximum output tokens for Gemini 2.5 Pro (65K)
        elif model_name == "gemini-2.5-flash":
            return 65535     # Maximum output tokens for Gemini 2.5 Flash (65K)
        elif model_name.startswith("gemini-2.5-flash-lite"):
            return 65535      # Maximum output tokens for 2.5 Flash Lite
        
        # Gemini 2.0 models
        elif model_name == "gemini-2.0-flash":
            return 8192      # Maximum output tokens for 2.0 Flash
        elif model_name == "gemini-2.0-flash-lite":
            return 8192      # Maximum output tokens for 2.0 Flash Lite
        
        # Gemini 1.5 models
        elif model_name == "gemini-1.5-flash":
            return 8192      # Maximum output tokens for 1.5 Flash
        elif model_name == "gemini-1.5-flash-8b":
            return 8192      # Maximum output tokens for 1.5 Flash 8B
        elif model_name == "gemini-1.5-pro":
            return 8192      # Maximum output tokens for 1.5 Pro
        
        # OpenAI models output limits
        elif "gpt-4o" in model_name:
            return 16384     # GPT-4o max output tokens
        elif "gpt-4" in model_name:
            return 8192      # GPT-4 max output tokens
        elif "gpt-3.5-turbo" in model_name:
            return 4096      # GPT-3.5 max output tokens
        
        # Anthropic models output limits
        elif "claude-3-opus" in model_name:
            return 4096      # Claude 3 Opus max output tokens
        elif "claude-3-sonnet" in model_name:
            return 4096      # Claude 3 Sonnet max output tokens
        elif "claude-3-haiku" in model_name:
            return 4096      # Claude 3 Haiku max output tokens
        
        # Grok models
        elif "grok" in model_name:
            return 4096      # Grok max output tokens
        
        # Default fallback
        return 2048

    def get_model_context_limit(self, model_name: str = None) -> int:
        """Get the context window limit for a specific model based on Google AI documentation."""
        if not model_name:
            model_name = self.get_current_model()
        
        # Gemini models with their context limits based on https://ai.google.dev/gemini-api/docs/models
        if model_name == "gemini-2.5-pro":
            return 2_000_000  # 2M tokens - Most powerful thinking model
        elif model_name == "gemini-2.5-flash":
            return 1_000_000  # 1M tokens - Best price-performance
        elif model_name.startswith("gemini-2.5-flash-lite"):
            return 1_000_000  # 1M tokens - Cost-efficient (experimental)
        elif model_name == "gemini-2.0-flash":
            return 1_000_000  # 1M tokens - Next generation features
        elif model_name == "gemini-2.0-flash-lite":
            return 1_000_000  # 1M tokens - Cost efficiency and low latency
        elif model_name == "gemini-1.5-flash":
            return 1_000_000  # 1M tokens - Fast and versatile
        elif model_name == "gemini-1.5-flash-8b":
            return 1_000_000  # 1M tokens - High performance with efficiency  
        elif model_name == "gemini-1.5-pro":
            return 1_000_000  # 1M tokens - Complex reasoning tasks
        
        # OpenAI models
        elif "gpt-4o" in model_name:
            return 128_000    # 128K tokens
        elif "gpt-4" in model_name:
            return 8_192 if "turbo" not in model_name else 128_000
        elif "gpt-3.5-turbo-16k" in model_name:
            return 16_385     # 16K tokens
        elif "gpt-3.5-turbo" in model_name:
            return 4_097      # 4K tokens
        
        # Anthropic models
        elif "claude-3-opus" in model_name:
            return 200_000    # 200K tokens
        elif "claude-3-sonnet" in model_name:
            return 200_000    # 200K tokens
        elif "claude-3-haiku" in model_name:
            return 200_000    # 200K tokens
        
        # Grok models
        elif "grok" in model_name:
            return 25_000     # ~25K tokens
        
        # Default fallback
        return 4_097

    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation: 4 chars = 1 token)."""
        return len(text) // 4

    def truncate_to_token_limit(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit."""
        estimated_tokens = self.estimate_tokens(text)
        if estimated_tokens <= max_tokens:
            return text
        
        # Calculate approximate character limit
        char_limit = max_tokens * 4
        
        # Try to cut at sentence boundary
        if len(text) > char_limit:
            truncated = text[:char_limit]
            # Find last sentence ending
            last_period = truncated.rfind('.')
            last_newline = truncated.rfind('\n')
            
            if last_period > char_limit - 200:  # If sentence end is near the limit
                truncated = truncated[:last_period + 1]
            elif last_newline > char_limit - 200:  # If newline is near the limit
                truncated = truncated[:last_newline]
            
            return truncated + "\n\n[Content truncated to fit token limit]"
        
        return text

    def prepare_safe_prompt(self, prompt: str, system_message: str, max_total_tokens: int = None) -> tuple[str, str]:
        """Prepare prompt and system message to fit within token limits."""
        
        # Get the actual context limit for the current model if not specified
        if max_total_tokens is None:
            model_limit = self.get_model_context_limit()
            # Use 85% of the model's context limit to be safe
            max_total_tokens = int(model_limit * 0.85)
        
        # Reserve tokens for system message and maximum output response
        system_tokens = self.estimate_tokens(system_message)
        max_output_tokens = self.get_model_output_limit()  # Use maximum output tokens for current model
        available_tokens = max_total_tokens - system_tokens - max_output_tokens
        
        # Truncate prompt if necessary
        safe_prompt = self.truncate_to_token_limit(prompt, available_tokens)
        
        print(f"🔢 Context allocation: {system_tokens} system + {len(safe_prompt)//4} prompt + {max_output_tokens} output = {system_tokens + len(safe_prompt)//4 + max_output_tokens} total tokens")
        
        return safe_prompt, system_message

    async def generate_completion(
        self, 
        prompt: str, 
        system_message: str, 
        max_tokens: int = None,  # Auto-detect max tokens if not specified
        temperature: float = 0.7,
        max_retries: int = 3
    ) -> str:
        """Generate completion with automatic prompt size management and optimized settings."""
        
        # Adjust temperature based on task type for better creativity and diversity
        if "biographer" in system_message.lower() or "writer" in system_message.lower():
            # Higher temperature for creative biographical writing
            temperature = max(temperature, 0.8)
        elif "iteration" in system_message.lower():
            # Even higher temperature for iterative content to avoid repetition
            temperature = max(temperature, 0.85)
        elif "thesis" in system_message.lower() or "summariz" in system_message.lower():
            # Lower temperature for precise summaries
            temperature = min(temperature, 0.6)
        
        # Auto-detect max tokens if not specified
        if max_tokens is None:
            max_tokens = self.get_model_output_limit()
            print(f"🚀 Auto-detected max output tokens: {max_tokens} for current model")
        
        # Prepare safe prompt
        safe_prompt, safe_system = self.prepare_safe_prompt(prompt, system_message)
        
        # Log token usage
        prompt_tokens = self.estimate_tokens(safe_prompt)
        system_tokens = self.estimate_tokens(safe_system)
        total_input_tokens = prompt_tokens + system_tokens
        
        print(f"🔢 Token usage: {total_input_tokens} input tokens (prompt: {prompt_tokens}, system: {system_tokens}), max_output: {max_tokens}, temp: {temperature:.2f}")
        
        if total_input_tokens > 14000:
            print(f"⚠️  Warning: Input tokens ({total_input_tokens}) approaching limit, consider further reduction")
        
        # Get the current model from database settings
        current_model = self.get_current_model()
        print(f"🤖 [AI DEBUG] Using model: {current_model} for generation")
        
        for attempt in range(max_retries):
            try:
                if self.provider == "openai":
                    response = self.client.chat.completions.create(
                        model=current_model,  # Use user-selected model from database
                        messages=[
                            {"role": "system", "content": safe_system},
                            {"role": "user", "content": safe_prompt}
                        ],
                        max_tokens=max_tokens,
                        temperature=temperature,
                        presence_penalty=0.3,  # Encourage new topics
                        frequency_penalty=0.2   # Discourage repetitive phrases
                    )
                    return response.choices[0].message.content
                    
                elif self.provider == "anthropic":
                    # Map OpenAI model names to Anthropic equivalents if needed
                    anthropic_model = self._map_to_anthropic_model(current_model)
                    response = self.client.messages.create(
                        model=anthropic_model,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        system=safe_system,
                        messages=[
                            {"role": "user", "content": safe_prompt}
                        ]
                    )
                    return response.content[0].text
                    
                elif self.provider == "gemini":
                    # Gemini handles system message differently
                    full_prompt = f"{safe_system}\n\n{safe_prompt}"
                    
                    # Configure generation with high token limit for Gemini 1.5 Pro
                    generation_config = {
                        'temperature': temperature,
                        'max_output_tokens': min(max_tokens, 8192),  # Gemini Pro limit
                    }
                    
                    response = self.client.generate_content(
                        full_prompt,
                        generation_config=generation_config
                    )
                    return response.text
                    
                elif self.provider == "grok":
                    # Grok uses OpenAI-compatible API
                    response = self.client.chat.completions.create(
                        model=current_model,
                        messages=[
                            {"role": "system", "content": safe_system},
                            {"role": "user", "content": safe_prompt}
                        ],
                        max_tokens=max_tokens,
                        temperature=temperature
                    )
                    return response.choices[0].message.content
                    
            except Exception as e:
                error_msg = str(e)
                
                # Check for context length errors
                if "context_length_exceeded" in error_msg or "maximum context length" in error_msg:
                    print(f"🚨 Context length exceeded on attempt {attempt + 1}, reducing prompt size...")
                    # Reduce prompt size more aggressively
                    safe_prompt = self.truncate_to_token_limit(safe_prompt, len(safe_prompt) // 2)
                    continue
                
                if attempt == max_retries - 1:
                    raise Exception(f"AI service error after {max_retries} attempts: {error_msg}")
                
                print(f"⚠️  Attempt {attempt + 1} failed: {error_msg}, retrying...")
                await asyncio.sleep(1 * (attempt + 1))  # Progressive backoff
        
        raise Exception("Failed to generate completion after all retries")
    
    async def process_outline_agent(self, interview_text: str, prompt: str = None) -> str:
        """Process the outline agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting outline agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading outline prompt from database...")
            prompt = self.prompt_service.get_prompt("outline")
            print(f"✅ [AI DEBUG] Loaded outline prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Create a structured outline for a biography based on the interview content."
            print(f"⚠️ [AI DEBUG] Using default outline prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided outline prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Outline prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Biography Outliner agent. Create a structured outline for a biography."
        
        # Truncate interview text to safe size for outline generation
        safe_interview_text = self.truncate_to_token_limit(interview_text, 8000)  # Conservative limit
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        
        full_prompt = f"""
{prompt}

=========== INTERVIEW TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

Please create the biography outline based on this interview transcript excerpt.
"""
        
        print(f"🚀 [AI DEBUG] Generating outline with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2000)
    
    async def process_writer_agent(self, interview_text: str, outline: str, prompt: str = None) -> str:
        """Process the writer agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting writer agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading writer prompt from database...")
            prompt = self.prompt_service.get_prompt("writer")
            print(f"✅ [AI DEBUG] Loaded writer prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Write a comprehensive, engaging biography based on the interview and outline provided."
            print(f"⚠️ [AI DEBUG] Using default writer prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided writer prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Writer prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Full Biography Writer agent. Write a comprehensive biography."
        
        # Truncate inputs to safe sizes
        safe_interview_text = self.truncate_to_token_limit(interview_text, 6000)
        safe_outline = self.truncate_to_token_limit(outline, 2000)
        
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        print(f"📏 [AI DEBUG] Outline truncated to {len(safe_outline)} chars (from {len(outline)})")
        
        full_prompt = f"""
{prompt[:1000]}  # Limit prompt size too

=========== INTERVIEW TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

=========== BIOGRAPHY OUTLINE ===========
{safe_outline}

Please write the full biography based on the interview transcript excerpt and outline.
"""
        
        print(f"🚀 [AI DEBUG] Generating biography with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2500)
    
    async def process_evaluation_agent(self, interview_text: str, biography: str, prompt: str = None) -> str:
        """Process the evaluation agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting evaluation agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading evaluation prompt from database...")
            prompt = self.prompt_service.get_prompt("evaluation")
            print(f"✅ [AI DEBUG] Loaded evaluation prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Review and evaluate the biography for accuracy, style, and completeness."
            print(f"⚠️ [AI DEBUG] Using default evaluation prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided evaluation prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Evaluation prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Evaluation Agent. Review and grade the biography."
        
        # Truncate inputs to safe sizes  
        safe_interview_text = self.truncate_to_token_limit(interview_text, 4000)
        safe_biography = self.truncate_to_token_limit(biography, 4000)
        
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        print(f"📏 [AI DEBUG] Biography truncated to {len(safe_biography)} chars (from {len(biography)})")
        
        full_prompt = f"""
{prompt[:800]}  # Limit prompt size

=========== ORIGINAL TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

=========== BIOGRAPHY TO EVALUATE ===========
{safe_biography}

Please evaluate the biography based on the criteria provided in the prompt.
"""
        
        print(f"🚀 [AI DEBUG] Generating evaluation with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2000)
    
    async def process_rewrite_agent(self, interview_text: str, biography: str, evaluation: str, prompt: str = None) -> str:
        """Process the rewrite agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting rewrite agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading rewrite prompt from database...")
            prompt = self.prompt_service.get_prompt("rewrite")
            print(f"✅ [AI DEBUG] Loaded rewrite prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Improve the biography based on the evaluation feedback provided."
            print(f"⚠️ [AI DEBUG] Using default rewrite prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided rewrite prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Rewrite prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Rewrite Agent. Improve the biography based on evaluation feedback."
        
        # Truncate inputs to safe sizes
        safe_interview_text = self.truncate_to_token_limit(interview_text, 3000)
        safe_biography = self.truncate_to_token_limit(biography, 4000)
        safe_evaluation = self.truncate_to_token_limit(evaluation, 1500)
        
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        print(f"📏 [AI DEBUG] Biography truncated to {len(safe_biography)} chars (from {len(biography)})")
        print(f"📏 [AI DEBUG] Evaluation truncated to {len(safe_evaluation)} chars (from {len(evaluation)})")
        
        full_prompt = f"""
{prompt[:800]}  # Limit prompt size

=========== ORIGINAL TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

=========== CURRENT BIOGRAPHY ===========
{safe_biography}

=========== EVALUATION FEEDBACK ===========
{safe_evaluation}

Please rewrite and improve the biography based on the evaluation feedback.
"""
        
        print(f"🚀 [AI DEBUG] Generating rewrite with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2500) 