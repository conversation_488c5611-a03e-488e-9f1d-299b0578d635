#!/usr/bin/env python3
"""
Enhanced Progressive Biography Service with iterative approach for long-form content generation.
Generates comprehensive 400+ page biographies through iterative content expansion.
"""

import asyncio
import json
import re
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.biography_job import <PERSON><PERSON><PERSON>, JobStatus
from app.services.pdf_service import PDFService
from app.services.ai_service import AIService
from app.services.websocket_manager import manager


class EnhancedProgressiveBiographyService:
    """Enhanced service for generating comprehensive long-form biographies."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService(db)
        
        # Enhanced configuration for long-form generation
        self.max_segment_size = 6000  # Increased from 3000 to 6000 for better context
        self.segment_overlap = 600    # Increased from 400 to 600
        self.iterations_per_section = 10  # Increased from 5 to 10 for more content
        self.sections_per_chapter = 4  # Multiple sections per chapter
        self.delay_between_iterations = 1.5  # Delay for rate limiting
        self.delay_between_sections = 3.0  # Longer delay between sections
        self.max_retries = 3
        self.target_words_per_page = 300  # Increased from 250 to 300 for better density
        self.target_pages_total = 600  # Increased from 500 to 600 for more content
        
        # Name tag system for consistent name replacement
        self.name_tags = {
            'USER_NAME': '{USER_NAME}',      # Full name
            'FIRST_NAME': '{FIRST_NAME}',    # First name only
            'HE_SHE': '{HE_SHE}',           # He/She pronoun
            'HIS_HER': '{HIS_HER}',         # His/Her possessive
            'HIM_HER': '{HIM_HER}',         # Him/Her object pronoun
        }
        
    async def process_enhanced_biography(self, job_id: str) -> bool:
        """Process biography with enhanced iterative generation for long-form content."""
        print(f"🚀 Starting ENHANCED progressive biography generation for job {job_id}")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        start_time = time.time()
        
        try:
            # Phase 1: Deep Analysis and Preparation
            await self._phase_1_deep_analysis(job)
            
            # Phase 2: Comprehensive Outline Generation  
            await self._phase_2_comprehensive_outline(job)
            
            # Phase 3: Iterative Chapter and Section Generation
            await self._phase_3_iterative_generation(job)
            
            # Phase 4: Content Expansion and Refinement
            # await self._phase_4_content_expansion(job)
            
            # Phase 5: Final Assembly and PDF Generation
            await self._phase_5_final_assembly_pdf(job)
            
            # Mark as completed
            job.processing_time_seconds = time.time() - start_time
            job.completed_at = datetime.utcnow()
            await self._update_job(job, JobStatus.COMPLETED, 100.0)
            
            print(f"✅ Enhanced biography generation completed in {job.processing_time_seconds:.2f}s")
            return True
            
        except Exception as e:
            job.error_message = str(e)
            job.processing_time_seconds = time.time() - start_time
            await self._update_job(job, JobStatus.FAILED, job.progress_percentage)
            print(f"❌ Enhanced biography generation failed: {e}")
            return False
    
    async def _phase_1_deep_analysis(self, job: BiographyJob):
        """Phase 1: Deep analysis with thematic content mapping."""
        print("📊 Phase 1: Deep Analysis and Content Mapping")
        await self._update_job(job, JobStatus.PROCESSING, 5.0)
        
        # Extract interview text
        interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
        if not interview_text.strip():
            raise Exception("Could not extract text from uploaded PDF")
        
        # Deep content analysis
        analysis = await self._perform_deep_analysis(interview_text)
        
        # Extract person name with improved logic
        extracted_name = self._extract_improved_name(job.uploaded_pdf_path, interview_text)
        
        # Calculate target structure based on content
        target_structure = self._calculate_target_structure(analysis)
        
        # Store comprehensive metadata
        job_metadata = {
            "extracted_name": extracted_name,
            "analysis": analysis,
            "target_structure": target_structure,
            "interview_length": len(interview_text),
            "processing_strategy": "enhanced_progressive",
            "target_pages": self.target_pages_total,
            "target_words": self.target_pages_total * self.target_words_per_page
        }
        
        job.evaluation_content = json.dumps(job_metadata)
        self.db.commit()
        
        print(f"✅ Deep analysis complete:")
        print(f"   • Content: {analysis['word_count']:,} words, {len(analysis['thematic_segments'])} themes")
        print(f"   • Target: {target_structure['total_chapters']} chapters, {target_structure['total_sections']} sections")
        print(f"   • Goal: {self.target_pages_total} pages ({self.target_pages_total * self.target_words_per_page:,} words)")
        
        await self._update_job(job, JobStatus.PROCESSING, 15.0)
    
    async def _perform_deep_analysis(self, interview_text: str) -> Dict:
        """Perform deep thematic analysis of interview content."""
        text_length = len(interview_text)
        word_count = len(interview_text.split())
        
        # Create thematic segments (more granular than before)
        thematic_segments = await self._create_thematic_segments(interview_text)
        
        # Detect life periods and major themes
        life_periods = await self._detect_life_periods(interview_text)
        major_themes = await self._extract_major_themes(interview_text)
        
        # Session and structure analysis
        session_markers = self._find_detailed_session_markers(interview_text)
        
        return {
            'total_length': text_length,
            'word_count': word_count,
            'thematic_segments': thematic_segments,
            'life_periods': life_periods,
            'major_themes': major_themes,
            'session_markers': session_markers,
            'content_density': word_count / max(len(thematic_segments), 1),
            'estimated_biography_complexity': min(max(word_count // 1000, 10), 50)
        }
    
    async def _create_thematic_segments(self, text: str) -> List[Dict]:
        """Create thematic segments with AI-assisted categorization."""
        # Split text into manageable chunks for thematic analysis
        chunks = []
        chunk_size = self.max_segment_size
        
        for i in range(0, len(text), chunk_size - self.segment_overlap):
            chunk_end = min(i + chunk_size, len(text))
            chunk_content = text[i:chunk_end]
            
            # Use AI to categorize chunk themes
            themes = await self._categorize_chunk_themes(chunk_content, i)
            
            chunks.append({
                'start_pos': i,
                'end_pos': chunk_end,
                'content': chunk_content,
                'themes': themes,
                'word_count': len(chunk_content.split()),
                'primary_theme': themes[0] if themes else 'general'
            })
        
        return chunks
    
    async def _categorize_chunk_themes(self, chunk: str, position: int) -> List[str]:
        """Use AI to categorize themes in a text chunk."""
        try:
            prompt = f"""
Analyze this interview segment and identify the main themes discussed.
Return only a JSON list of 2-4 main themes from this list:
["childhood", "family", "education", "career", "relationships", "challenges", "achievements", 
 "travel", "hobbies", "values", "beliefs", "health", "community", "creative_work", "business", 
 "parenthood", "marriage", "friendship", "loss", "growth", "retirement", "legacy", "general"]

Interview segment:
{chunk[:1500]}

Return format: ["theme1", "theme2", "theme3"]
"""
            
            response = await self.ai_service.generate_completion(
                prompt,
                "You are a biographical content analyzer. Return only valid JSON.",
                max_tokens=100
            )
            
            # Parse themes from response
            import re
            json_match = re.search(r'\[(.*?)\]', response)
            if json_match:
                themes_str = json_match.group(1)
                themes = [theme.strip(' "\'') for theme in themes_str.split(',')]
                return themes[:4]  # Max 4 themes
            
        except Exception as e:
            print(f"⚠️ Theme categorization failed: {e}")
        
        # Fallback to keyword-based detection
        return self._detect_themes_by_keywords(chunk)
    
    def _detect_themes_by_keywords(self, chunk: str) -> List[str]:
        """Fallback theme detection using keywords."""
        chunk_lower = chunk.lower()
        
        theme_keywords = {
            'childhood': ['child', 'young', 'kid', 'school', 'grade', 'playground'],
            'family': ['mother', 'father', 'parent', 'brother', 'sister', 'family'],
            'education': ['school', 'college', 'university', 'study', 'learn', 'degree'],
            'career': ['job', 'work', 'career', 'profession', 'business', 'company'],
            'relationships': ['friend', 'love', 'marriage', 'partner', 'relationship'],
            'challenges': ['difficult', 'struggle', 'problem', 'challenge', 'hard'],
            'achievements': ['success', 'achievement', 'award', 'accomplish', 'proud']
        }
        
        detected_themes = []
        for theme, keywords in theme_keywords.items():
            if any(keyword in chunk_lower for keyword in keywords):
                detected_themes.append(theme)
        
        return detected_themes[:3] if detected_themes else ['general']
    
    async def _detect_life_periods(self, text: str) -> List[Dict]:
        """Detect major life periods from interview content."""
        # Use AI to identify life periods
        try:
            prompt = f"""
Analyze this interview and identify major life periods with approximate time ranges. Dont repeat or rephrase the same period.
Return JSON format with periods like:
{{"periods": [
  {{"name": "Early Childhood", "age_range": "0-5", "themes": ["family", "home"]}},
  {{"name": "School Years", "age_range": "6-18", "themes": ["education", "friends"]}}
]}}

Interview content (first 3000 chars):
{text[:3000]}
"""
            
            response = await self.ai_service.generate_completion(
                prompt,
                "You are a biographical analyzer. Return only valid JSON.",
                max_tokens=500
            )
            
            # Parse periods
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                periods_data = json.loads(json_match.group())
                return periods_data.get('periods', [])
                
        except Exception as e:
            print(f"⚠️ Life periods detection failed: {e}")
        
        # Fallback to standard periods
        return [
            {"name": "Early Life", "age_range": "0-12", "themes": ["childhood", "family"]},
            {"name": "Adolescence", "age_range": "13-18", "themes": ["education", "growth"]},
            {"name": "Young Adulthood", "age_range": "19-30", "themes": ["career", "relationships"]},
            {"name": "Middle Years", "age_range": "31-50", "themes": ["career", "family"]},
            {"name": "Later Years", "age_range": "51+", "themes": ["reflection", "legacy"]}
        ]
    
    async def _extract_major_themes(self, text: str) -> List[str]:
        """Extract major recurring themes from the entire interview."""
        # Count theme occurrences across all segments
        theme_counts = {}
        text_lower = text.lower()
        
        theme_patterns = {
            'theater': ['theater', 'theatre', 'stage', 'performance', 'acting', 'drama'],
            'education': ['school', 'teaching', 'teacher', 'student', 'education', 'learn'],
            'family': ['family', 'mother', 'father', 'parent', 'sibling', 'children'],
            'music': ['music', 'sing', 'song', 'musical', 'choir', 'instrument'],
            'community': ['community', 'volunteer', 'church', 'group', 'organization'],
            'mentorship': ['mentor', 'guide', 'teach', 'influence', 'inspire', 'support'],
            'creativity': ['creative', 'art', 'artistic', 'express', 'imagination'],
            'resilience': ['challenge', 'overcome', 'persist', 'strength', 'endure']
        }
        
        for theme, patterns in theme_patterns.items():
            count = sum(text_lower.count(pattern) for pattern in patterns)
            if count > 0:
                theme_counts[theme] = count
        
        # Return top themes
        sorted_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)
        return [theme for theme, count in sorted_themes[:8]]  # Top 8 themes
    
    def _calculate_target_structure(self, analysis: Dict) -> Dict:
        """Calculate optimal chapter and section structure for target length."""
        content_complexity = analysis['estimated_biography_complexity']
        
        # Calculate chapters based on content richness
        base_chapters = max(8, min(content_complexity // 3, 12))  # 8-12 chapters
        
        # Calculate sections per chapter for target length
        target_words = self.target_pages_total * self.target_words_per_page
        words_per_chapter = target_words // base_chapters
        words_per_section = words_per_chapter // self.sections_per_chapter
        
        return {
            'total_chapters': base_chapters,
            'sections_per_chapter': self.sections_per_chapter,
            'total_sections': base_chapters * self.sections_per_chapter,
            'target_words_per_section': words_per_section,
            'target_words_per_chapter': words_per_chapter,
            'content_complexity_score': content_complexity
        }
    
    async def _phase_2_comprehensive_outline(self, job: BiographyJob):
        """Phase 2: Generate comprehensive outline with sections."""
        print("📋 Phase 2: Comprehensive Outline Generation")
        
        # Get stored metadata
        metadata = json.loads(job.evaluation_content) if job.evaluation_content else {}
        analysis = metadata.get('analysis', {})
        target_structure = metadata.get('target_structure', {})
        
        # Generate detailed outline with sections
        outline_data = await self._generate_comprehensive_outline(
            job.uploaded_pdf_path, analysis, target_structure
        )
        
        # Store outline
        job.outline_content = outline_data["outline_description"]
        job.total_chapters = outline_data["chapter_count"]
        job.completed_chapters = 0
        job.chapters_content = {}  # Initialize empty chapters
        
        # Store detailed chapter structure
        metadata['detailed_outline'] = outline_data
        job.evaluation_content = json.dumps(metadata)
        
        self.db.commit()
        print(f"✅ Comprehensive outline generated:")
        print(f"   • Chapters: {job.total_chapters}")
        print(f"   • Total sections: {len(outline_data.get('all_sections', []))}")
        print(f"   • Target words: {target_structure.get('target_words_per_chapter', 0):,} per chapter")
        
        await self._update_job(job, JobStatus.OUTLINE_COMPLETE, 25.0)
        return outline_data
    
    async def _generate_comprehensive_outline(self, pdf_path: str, analysis: Dict, target_structure: Dict) -> Dict:
        """Generate comprehensive outline with detailed sections."""
        interview_text = PDFService.extract_text_from_pdf(pdf_path)
        
        # Use thematic segments for outline
        thematic_segments = analysis.get('thematic_segments', [])
        life_periods = analysis.get('life_periods', [])
        major_themes = analysis.get('major_themes', [])
        
        # Create summary for outline generation
        theme_summaries = self._create_theme_summaries(thematic_segments)
        
        try:
            target_chapters = target_structure.get('total_chapters', 10)
            target_sections = target_structure.get('sections_per_chapter', 4)
            
            system_message = f"""You are creating a comprehensive {target_chapters}-chapter biography outline.
Each chapter should have {target_sections} detailed sections for rich, book-length content.

CRITICAL: Respond with ONLY valid JSON:
{{
    "outline_description": "Brief overview",
    "chapters": [
        {{
            "number": 1,
            "title": "Descriptive Chapter Title",
            "description": "What this chapter covers",
            "themes": ["theme1", "theme2"],
            "time_period": "life period",
            "sections": [
                {{
                    "number": 1,
                    "title": "Section Title",
                    "focus": "Specific focus area",
                    "content_guidance": "What to explore in this section"
                }}
            ]
        }}
    ],
    "chapter_count": {target_chapters}
}}"""
            
            prompt = f"""
Create a comprehensive {target_chapters}-chapter biography outline based on this interview analysis:

MAJOR THEMES: {', '.join(major_themes[:8])}
LIFE PERIODS: {', '.join([p.get('name', '') for p in life_periods])}

CONTENT SUMMARIES:
{theme_summaries[:3000]}

Create detailed chapters with {target_sections} sections each for comprehensive coverage.
Focus on rich storytelling and detailed exploration of experiences.
"""
            
            response = await self.ai_service.generate_completion(
                prompt, system_message, max_tokens=3000
            )
            
            # Log AI response before parsing
            print(f"\n🤖 [OUTLINE DEBUG] AI response received for outline generation:")
            print(f"📏 [OUTLINE DEBUG] Response length: {len(response)} chars")
            print(f"📄 [OUTLINE DEBUG] Response preview: {response[:300]}...")
            
            # Parse JSON with enhanced error handling
            outline_data = self._parse_comprehensive_outline(response, target_chapters, target_sections)
            
            # Add section metadata
            all_sections = []
            for chapter in outline_data['chapters']:
                for section in chapter.get('sections', []):
                    section['chapter_number'] = chapter['number']
                    section['chapter_title'] = chapter['title']
                    all_sections.append(section)
            
            outline_data['all_sections'] = all_sections
            outline_data['total_sections'] = len(all_sections)
            
            return outline_data
            
        except Exception as e:
            print(f"⚠️ [OUTLINE DEBUG] AI outline failed, using comprehensive fallback: {e}")
            print(f"📋 [OUTLINE DEBUG] Creating fallback outline with {target_chapters} chapters and {target_sections} sections each")
            return self._create_comprehensive_fallback_outline(target_structure)
    
    def _create_theme_summaries(self, thematic_segments: List[Dict]) -> str:
        """Create summaries of thematic content."""
        summaries = []
        
        # Group segments by primary theme
        theme_groups = {}
        for segment in thematic_segments:
            primary_theme = segment.get('primary_theme', 'general')
            if primary_theme not in theme_groups:
                theme_groups[primary_theme] = []
            theme_groups[primary_theme].append(segment)
        
        # Create summary for each theme
        for theme, segments in theme_groups.items():
            total_words = sum(seg['word_count'] for seg in segments)
            sample_content = segments[0]['content'][:300] if segments else ""
            
            summaries.append(f"{theme.upper()} ({total_words:,} words): {sample_content}...")
        
        return '\n\n'.join(summaries)
    
    def _parse_comprehensive_outline(self, response: str, target_chapters: int, target_sections: int) -> Dict:
        """Parse comprehensive outline with enhanced error handling."""
        print(f"\n🔍 [OUTLINE DEBUG] Starting outline parsing...")
        print(f"📏 [OUTLINE DEBUG] Response length: {len(response)} chars")
        print(f"📄 [OUTLINE DEBUG] Raw AI response (first 500 chars):")
        print(f"   {response[:500]}...")
        
        response = response.strip()
        
        # Try multiple JSON extraction patterns
        json_patterns = [
            (r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', "Nested JSON pattern"),
            (r'```json\s*(\{.*?\})\s*```', "Markdown JSON block"),
            (r'```\s*(\{.*?\})\s*```', "Generic code block"),
            (r'(\{.*?\})', "Simple JSON match")
        ]
        
        for pattern, pattern_name in json_patterns:
            print(f"🔎 [OUTLINE DEBUG] Trying pattern: {pattern_name}")
            matches = re.findall(pattern, response, re.DOTALL)
            
            if matches:
                print(f"✅ [OUTLINE DEBUG] Found {len(matches)} matches with {pattern_name}")
                
                for i, match in enumerate(matches):
                    json_content = match if isinstance(match, str) else match
                    print(f"📄 [OUTLINE DEBUG] Match {i+1} preview (first 200 chars): {json_content[:200]}...")
                    
                    try:
                        outline_data = json.loads(json_content)
                        print(f"✅ [OUTLINE DEBUG] Successfully parsed JSON from match {i+1}")
                        print(f"📊 [OUTLINE DEBUG] Parsed data keys: {list(outline_data.keys())}")
                        
                        # Validate and enhance outline
                        if 'chapters' in outline_data:
                            print(f"📚 [OUTLINE DEBUG] Found {len(outline_data['chapters'])} chapters in outline")
                            
                            # Ensure all chapters have sections
                            for chapter_idx, chapter in enumerate(outline_data['chapters']):
                                chapter_num = chapter.get('number', chapter_idx + 1)
                                chapter_title = chapter.get('title', f'Chapter {chapter_num}')
                                
                                if 'sections' not in chapter or len(chapter['sections']) < target_sections:
                                    print(f"⚠️ [OUTLINE DEBUG] Chapter {chapter_num} missing sections, generating fallback")
                                    chapter['sections'] = self._generate_fallback_sections(
                                        chapter, target_sections
                                    )
                                else:
                                    print(f"✅ [OUTLINE DEBUG] Chapter {chapter_num} has {len(chapter['sections'])} sections")
                            
                            outline_data['chapter_count'] = len(outline_data['chapters'])
                            print(f"🎉 [OUTLINE DEBUG] Successfully processed outline with {outline_data['chapter_count']} chapters")
                            return outline_data
                        else:
                            print(f"❌ [OUTLINE DEBUG] No 'chapters' key found in parsed data")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ [OUTLINE DEBUG] JSON decode error for match {i+1}: {e}")
                        print(f"📄 [OUTLINE DEBUG] Failed JSON content: {json_content[:300]}...")
                        continue
            else:
                print(f"❌ [OUTLINE DEBUG] No matches found for {pattern_name}")
        
        # Log the full response for debugging
        print(f"❌ [OUTLINE DEBUG] All JSON parsing attempts failed!")
        print(f"📄 [OUTLINE DEBUG] Full AI response:")
        print(f"---START---")
        print(response)
        print(f"---END---")
        
        # Try smart text parsing as last resort
        print(f"🔄 [OUTLINE DEBUG] Attempting smart text parsing as fallback...")
        try:
            return self._parse_outline_from_text(response, target_chapters, target_sections)
        except Exception as text_parse_error:
            print(f"❌ [OUTLINE DEBUG] Smart text parsing also failed: {text_parse_error}")
            
        # Fallback if all parsing fails
        raise Exception("Could not parse comprehensive outline from AI response")
    
    def _parse_outline_from_text(self, response: str, target_chapters: int, target_sections: int) -> Dict:
        """Parse outline from plain text response using intelligent pattern matching."""
        print(f"🧠 [OUTLINE DEBUG] Starting intelligent text parsing...")
        
        chapters = []
        lines = response.split('\n')
        
        # Patterns to identify chapter information
        chapter_patterns = [
            r'chapter\s+(\d+)[:.]?\s*(.+)',
            r'(\d+)\.\s*(.+)',
            r'#\s*(.+)',
            r'\*\*(.+)\*\*'
        ]
        
        current_chapter = None
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Try to match chapter patterns
            for pattern in chapter_patterns:
                match = re.match(pattern, line, re.IGNORECASE)
                if match:
                    if len(match.groups()) == 2:
                        # Pattern with chapter number
                        chapter_num = int(match.group(1))
                        chapter_title = match.group(2).strip()
                    else:
                        # Pattern without explicit number
                        chapter_num = len(chapters) + 1
                        chapter_title = match.group(1).strip()
                    
                    # Save previous chapter if exists
                    if current_chapter:
                        chapters.append(current_chapter)
                    
                    # Start new chapter
                    current_chapter = {
                        "number": chapter_num,
                        "title": chapter_title,
                        "description": f"Covers {chapter_title.lower()}",
                        "themes": self._extract_themes_from_title(chapter_title),
                        "time_period": self._guess_time_period(chapter_title),
                        "sections": []
                    }
                    
                    print(f"📖 [OUTLINE DEBUG] Found chapter {chapter_num}: {chapter_title}")
                    break
        
        # Add last chapter
        if current_chapter:
            chapters.append(current_chapter)
        
        # If we didn't find enough chapters, create fallback chapters
        if len(chapters) < target_chapters:
            print(f"⚠️ [OUTLINE DEBUG] Only found {len(chapters)} chapters, creating fallback for remaining {target_chapters - len(chapters)}")
            
            fallback_themes = [
                ("Early Life and Origins", ["childhood", "family", "origins"]),
                ("Growing Up", ["youth", "education", "development"]),
                ("Coming of Age", ["adolescence", "teenage", "transition"]),
                ("Early Adulthood", ["college", "early career", "independence"]),
                ("Professional Life", ["career", "work", "profession"]),
                ("Personal Relationships", ["relationships", "marriage", "family"]),
                ("Major Life Events", ["milestones", "achievements", "challenges"]),
                ("Later Life", ["maturity", "reflection", "legacy"])
            ]
            
            for i in range(len(chapters), target_chapters):
                if i < len(fallback_themes):
                    title, themes = fallback_themes[i]
                else:
                    title = f"Life Chapter {i + 1}"
                    themes = ["experiences", "memories"]
                
                chapters.append({
                    "number": i + 1,
                    "title": title,
                    "description": f"Explores {title.lower()} and related experiences",
                    "themes": themes,
                    "time_period": "various periods",
                    "sections": []
                })
        
        # Add sections to all chapters
        for chapter in chapters:
            if not chapter.get("sections"):
                chapter["sections"] = self._generate_fallback_sections(chapter, target_sections)
        
        outline_description = f"Comprehensive {len(chapters)}-chapter biography outline"
        if chapters:
            outline_description += f" covering {chapters[0]['title']} through {chapters[-1]['title']}"
        
        result = {
            "outline_description": outline_description,
            "chapters": chapters,
            "chapter_count": len(chapters)
        }
        
        print(f"🎉 [OUTLINE DEBUG] Successfully created outline from text with {len(chapters)} chapters")
        return result
    
    def _extract_themes_from_title(self, title: str) -> List[str]:
        """Extract likely themes from chapter title."""
        title_lower = title.lower()
        theme_keywords = {
            "childhood": ["childhood", "early", "young", "child"],
            "education": ["school", "education", "learning", "college", "university"],
            "career": ["career", "work", "job", "profession", "business"],
            "family": ["family", "marriage", "children", "spouse", "parents"],
            "challenges": ["challenge", "difficulty", "struggle", "hardship"],
            "achievement": ["success", "achievement", "accomplishment", "triumph"],
            "relationships": ["relationship", "friendship", "love", "community"],
            "growth": ["growth", "development", "change", "transformation"]
        }
        
        themes = []
        for theme, keywords in theme_keywords.items():
            if any(keyword in title_lower for keyword in keywords):
                themes.append(theme)
        
        # Default themes if none found
        if not themes:
            themes = ["life_experiences", "personal_growth"]
        
        return themes
    
    def _guess_time_period(self, title: str) -> str:
        """Guess time period from chapter title."""
        title_lower = title.lower()
        
        period_keywords = {
            "early childhood": ["childhood", "early", "young", "baby", "toddler"],
            "youth/education": ["school", "teenage", "adolescent", "high school"],
            "early adulthood": ["college", "university", "early adult", "twenties"],
            "professional years": ["career", "work", "professional", "job"],
            "family years": ["marriage", "children", "family", "parenting"],
            "mature years": ["later", "mature", "older", "reflection", "legacy"]
        }
        
        for period, keywords in period_keywords.items():
            if any(keyword in title_lower for keyword in keywords):
                return period
        
        return "various periods"
    
    def _generate_fallback_sections(self, chapter: Dict, target_sections: int) -> List[Dict]:
        """Generate fallback sections for a chapter."""
        chapter_title = chapter.get('title', 'Chapter')
        themes = chapter.get('themes', ['life'])
        
        section_templates = [
            "Early Experiences and Foundations",
            "Key Relationships and Influences", 
            "Challenges and Growth",
            "Achievements and Milestones",
            "Reflections and Impact",
            "Personal Development",
            "Community Connections",
            "Lasting Legacy"
        ]
        
        sections = []
        for i in range(target_sections):
            template = section_templates[i % len(section_templates)]
            sections.append({
                "number": i + 1,
                "title": f"{template}",
                "focus": f"Detailed exploration of {themes[0] if themes else 'life experiences'}",
                "content_guidance": f"Expand on specific experiences, relationships, and growth in this area"
            })
        
        return sections
    
    def _create_comprehensive_fallback_outline(self, target_structure: Dict) -> Dict:
        """Create comprehensive fallback outline."""
        target_chapters = target_structure.get('total_chapters', 10)
        sections_per_chapter = target_structure.get('sections_per_chapter', 4)
        
        chapter_themes = [
            ("Early Foundations", ["childhood", "family", "upbringing"], "early years"),
            ("Growing and Learning", ["education", "development", "discovery"], "youth"),
            ("Coming of Age", ["adolescence", "identity", "exploration"], "teenage years"),
            ("Educational Journey", ["school", "knowledge", "mentors"], "academic years"),
            ("Early Career and Ventures", ["work", "profession", "ambition"], "early career"),
            ("Relationships and Connections", ["family", "friends", "love"], "relationships"),
            ("Professional Growth", ["career", "success", "leadership"], "career development"),
            ("Challenges and Resilience", ["obstacles", "struggles", "perseverance"], "difficult times"),
            ("Achievements and Recognition", ["success", "accomplishments", "impact"], "peak years"),
            ("Wisdom and Legacy", ["lessons", "advice", "influence"], "later years"),
            ("Creative Expression", ["creativity", "arts", "innovation"], "creative periods"),
            ("Community Impact", ["service", "contribution", "leadership"], "community involvement")
        ]
        
        chapters = []
        for i in range(target_chapters):
            theme_idx = i % len(chapter_themes)
            title, themes, period = chapter_themes[theme_idx]
            
            # Generate sections for this chapter
            sections = []
            for j in range(sections_per_chapter):
                section_titles = [
                    f"Early {title.split()[0]} Experiences",
                    f"Key Relationships in {title}",
                    f"Challenges and Growth During {title}",
                    f"Lasting Impact of {title}"
                ]
                
                sections.append({
                    "number": j + 1,
                    "title": section_titles[j % len(section_titles)],
                    "focus": f"Detailed exploration of {themes[0]}",
                    "content_guidance": f"Expand on specific experiences and their significance"
                })
            
            chapters.append({
                "number": i + 1,
                "title": title,
                "description": f"Comprehensive exploration of {title.lower()} and related experiences",
                "themes": themes,
                "time_period": period,
                "sections": sections
            })
        
        return {
            "outline_description": f"A comprehensive {target_chapters}-chapter biography with detailed sections",
            "chapters": chapters,
            "chapter_count": target_chapters
        }
    
    async def _phase_3_iterative_generation(self, job: BiographyJob):
        """Phase 3: Iterative chapter and section generation."""
        print("📝 Phase 3: Iterative Chapter and Section Generation")
        await self._update_job(job, JobStatus.ITERATIVE_WRITING, 30.0)
        
        # Get metadata and outline
        metadata = json.loads(job.evaluation_content) if job.evaluation_content else {}
        analysis = metadata.get('analysis', {})
        detailed_outline = metadata.get('detailed_outline', {})
        
        if not detailed_outline:
            raise Exception("No detailed outline found for iterative generation")
        
        # Get interview content and person name
        interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
        thematic_segments = analysis.get('thematic_segments', [])
        person_name = job.user_name  # Get person name from job
        
        chapters_content = job.chapters_content or {}
        
        # Generate each chapter with iterative sections
        total_chapters = len(detailed_outline.get('chapters', []))
        
        for i, chapter_data in enumerate(detailed_outline.get('chapters', [])):
            chapter_num = chapter_data['number']
            chapter_key = f"chapter_{chapter_num}"
            
            # Skip if chapter already exists (resume capability)
            if chapter_key in chapters_content:
                print(f"⏭️ Chapter {chapter_num} already exists, skipping")
                continue
            
            print(f"\n🖋️ Generating Chapter {chapter_num}/{total_chapters}: {chapter_data['title']}")
            
            # Generate chapter through iterative section building
            chapter_content = await self._generate_chapter_iteratively(
                chapter_data, thematic_segments, interview_text, chapters_content, person_name
            )
            
            # Save chapter immediately
            chapters_content[chapter_key] = {
                "title": chapter_data["title"],
                "content": chapter_content,
                "number": chapter_num,
                "generated_at": datetime.utcnow().isoformat(),
                "word_count": len(chapter_content.split()),
                "sections_generated": len(chapter_data.get('sections', []))
            }
            
            job.chapters_content = chapters_content
            job.completed_chapters = i + 1
            self.db.commit()
            
            print(f"✅ Chapter {chapter_num} completed: {len(chapter_content):,} chars, {len(chapter_content.split()):,} words")
            
            # Update progress
            chapter_progress = 30.0 + ((i + 1) / total_chapters) * 40.0
            await self._update_job(job, JobStatus.ITERATIVE_WRITING, chapter_progress)
            
            # Delay between chapters
            await asyncio.sleep(self.delay_between_sections)
        
        print(f"✅ All {total_chapters} chapters generated through iterative process")
        await self._update_job(job, JobStatus.WRITING_COMPLETE, 70.0)
    
    async def _generate_chapter_iteratively(
        self, chapter_data: Dict, thematic_segments: List[Dict], 
        interview_text: str, existing_chapters: Dict, person_name: str
    ) -> str:
        """Generate a chapter through iterative section building."""
        
        chapter_title = chapter_data['title']
        chapter_themes = chapter_data.get('themes', [])
        sections = chapter_data.get('sections', [])
        
        # Find relevant content for this chapter
        relevant_segments = self._find_relevant_segments_for_chapter(
            thematic_segments, chapter_themes
        )
        
        # Generate chapter introduction
        chapter_content = f"# {chapter_title}\n\n"
        
        # Generate each section iteratively
        for section in sections:
            print(f"Generating Section {section['number']}: {section['title']}")
            
            section_content = await self._generate_section_iteratively(
                section, relevant_segments, interview_text, chapter_content, person_name
            )
            
            chapter_content += f"\n## {section['title']}\n\n{section_content}\n\n"
            
            # Short delay between sections
            await asyncio.sleep(self.delay_between_iterations)
        
        return chapter_content
    
    async def _generate_section_iteratively(
        self, section: Dict, relevant_segments: List[Dict], 
        interview_text: str, existing_content: str, person_name: str
    ) -> str:
        """Generate a section through multiple adaptive iterations with repetition detection."""
        
        section_title = section['title']
        section_focus = section.get('focus', '')
        content_guidance = section.get('content_guidance', '')
        
        # Calculate adaptive iterations for this section
        adaptive_iterations = self._calculate_adaptive_iterations(section, relevant_segments)
        print(f"     Section '{section_title}': Using {adaptive_iterations} adaptive iterations")
        
        # Prepare relevant context
        section_context = self._prepare_section_context(relevant_segments, section_focus)
        
        # Build section content through adaptive iterations
        section_content = ""
        
        for iteration in range(adaptive_iterations):
            # Get specific focus for this iteration
            iteration_focus = self._get_iteration_focus(iteration, adaptive_iterations)
            
            # Detect repetition before generating new content
            repetition_score = self._detect_content_repetition(section_content, existing_content)
            
            print(f"      🔄 Section iteration {iteration + 1}/{adaptive_iterations} - Repetition: {repetition_score:.2f}")
            
            # Early stopping if content quality is good and repetition is high
            if iteration > 2 and repetition_score > 0.7 and len(section_content) > 1000:
                print(f"      ⏹️ Early stopping at iteration {iteration + 1} due to high repetition ({repetition_score:.2f})")
                break
            
            # Create iterative prompt with focus
            base_prompt = self._create_section_iteration_prompt(
                section_title, section_focus, content_guidance,
                section_context, existing_content, section_content, iteration,
                person_name=person_name
            )
            
            # Enhance prompt if repetition detected
            enhanced_prompt = self._enhance_prompt_for_diversity(base_prompt, repetition_score, iteration)
            
            # Add iteration-specific focus to the prompt
            focused_prompt = f"""
{enhanced_prompt}

ITERATION {iteration + 1}/{adaptive_iterations} SPECIFIC FOCUS: {iteration_focus}
Emphasize this focus while maintaining narrative flow and avoiding repetition.
Use the provided name tags consistently throughout.
"""
            
            # Generate content for this iteration with adaptive temperature
            temperature = 0.8 + (repetition_score * 0.15)  # Increase creativity if repetition detected
            
            iteration_content = await self.ai_service.generate_completion(
                focused_prompt,
                f"You are writing a detailed biographical section. Focus on {iteration_focus.lower()} while avoiding repetition. Use the provided name tags ({self.name_tags['USER_NAME']}, {self.name_tags['FIRST_NAME']}, etc.) consistently - never use placeholders like [Subject].",
                max_tokens=None,  # Auto-detect max tokens for maximum output
                temperature=temperature
            )
            
            # Replace name tags in the generated content
            if iteration_content:
                iteration_content = self._replace_name_tags(iteration_content, person_name)
            
            # Append to section content
            if iteration == 0:
                section_content = iteration_content
            else:
                section_content += f"\n\n{iteration_content}"
            
            # Adaptive delay between iterations (longer if repetition detected)
            delay = self.delay_between_iterations + (repetition_score * 1.0)
            await asyncio.sleep(delay)
        
        return section_content
    
    def _prepare_section_context(self, relevant_segments: List[Dict], section_focus: str) -> str:
        """Prepare contextual content for section generation."""
        # Select most relevant segments for this section
        focus_keywords = section_focus.lower().split()
        
        scored_segments = []
        for segment in relevant_segments[:8]:  # Top 8 segments
            try:
                score = 0
                segment_content = segment.get('content', '').lower()
                
                # Score based on focus keywords
                for keyword in focus_keywords:
                    score += segment_content.count(keyword) * 2
                
                # Score based on themes
                for theme in segment.get('themes', []):
                    if any(keyword in theme for keyword in focus_keywords):
                        score += 5
                
                if score > 0:
                    scored_segments.append((score, segment))
                    
            except Exception as e:
                print(f"⚠️ Error scoring segment: {e}")
                continue
        
        # Sort by relevance with safe comparison and take top segments
        try:
            scored_segments.sort(key=lambda x: x[0], reverse=True)
            top_segments = [seg for score, seg in scored_segments[:4]]
        except Exception as e:
            print(f"⚠️ Error sorting segments, using first 4: {e}")
            top_segments = relevant_segments[:4]
        
        # Format context
        context_parts = []
        for segment in top_segments:
            try:
                content_preview = segment.get('content', '')[:800]  # Longer previews
                if content_preview.strip():
                    context_parts.append(f"--- Relevant Content ---\n{content_preview}")
            except Exception as e:
                print(f"⚠️ Error formatting segment: {e}")
                continue
        
        return '\n\n'.join(context_parts) if context_parts else "No relevant content found."
    
    def _create_section_iteration_prompt(
        self, section_title: str, section_focus: str, content_guidance: str,
        context: str, existing_content: str, section_content: str, iteration: int,
        person_name: str = None
    ) -> str:
        """Create detailed prompt for section iteration using name tags."""
        
        if iteration == 0:
            # First iteration - establish foundation
            return f"""
Write the opening content for this biographical section: "{section_title}"

CRITICAL NAME USAGE - USE ONLY THESE EXACT TAGS:
- Use {self.name_tags['USER_NAME']} for the full name
- Use {self.name_tags['FIRST_NAME']} for just the first name 
- Use {self.name_tags['HE_SHE']} for he/she pronouns
- Use {self.name_tags['HIS_HER']} for his/her possessives
- Use {self.name_tags['HIM_HER']} for him/her object pronouns

⚠️ IMPORTANT: Use ONLY the exact tags shown above. Do NOT create new tags like {{HIM_HERSELF}} or {{HIS_HERS}}. 
⚠️ Do NOT use placeholders like [Subject], [Name], or generic terms.
⚠️ The correct object pronoun tag is {{HIM_HER}}, NOT {{HIM_HERSELF}}.

WRITING STYLE REQUIREMENTS:
- Avoid overusing unique descriptive words like "tapestry", "palette", "mosaic", "journey", "path"
- Vary vocabulary and sentence structure throughout
- Focus on authentic, specific details rather than flowery metaphors
- Use clear, engaging narrative prose
- Prefer concrete examples over abstract descriptions
- Use strong verbs and vivid nouns instead of adverbs and adjectives

CONTENT REQUIREMENTS:
- Write about real, specific experiences from {self.name_tags['USER_NAME']}'s life
- Include dialogue, thoughts, and emotions where appropriate
- Describe settings, people, and circumstances in detail
- Show rather than tell - use scenes and examples
- Connect events to {self.name_tags['FIRST_NAME']}'s character development

Focus: {section_focus}
Guidance: {content_guidance}

Use this interview content for authentic details:
{context[:3000]}

Write 3-4 rich paragraphs that introduce the section topic with specific details, anecdotes, and engaging narrative.
Focus on storytelling and bringing {self.name_tags['USER_NAME']}'s experiences to life.
"""
        
        elif iteration < self.iterations_per_section // 2:
            # Middle iterations - develop content
            return f"""
Continue developing this biographical section about {self.name_tags['USER_NAME']}: "{section_title}"

CRITICAL NAME USAGE - USE ONLY THESE EXACT TAGS:
- Use {self.name_tags['USER_NAME']} for the full name
- Use {self.name_tags['FIRST_NAME']} for just the first name 
- Use {self.name_tags['HE_SHE']} for he/she pronouns
- Use {self.name_tags['HIS_HER']} for his/her possessives
- Use {self.name_tags['HIM_HER']} for him/her object pronouns

⚠️ REMINDER: Use ONLY the exact tags shown above. The correct object pronoun tag is {{HIM_HER}}, NOT {{HIM_HERSELF}}.

Current content (last 500 words):
{section_content[-2000:]}

Additional interview context:
{context[iteration*600:(iteration+1)*1200]}

Add 3-4 more paragraphs that:
- Expand on {self.name_tags['FIRST_NAME']}'s key experiences or relationships
- Include specific details and anecdotes about {self.name_tags['USER_NAME']}
- Show {self.name_tags['HIS_HER']} character development and growth
- Connect to broader themes in {self.name_tags['HIS_HER']} life

Maintain narrative flow from the existing content.
"""
        
        else:
            # Final iterations - conclude and connect
            return f"""
Conclude this biographical section about {self.name_tags['USER_NAME']}: "{section_title}"

CRITICAL NAME USAGE - USE ONLY THESE EXACT TAGS:
- Use {self.name_tags['USER_NAME']} for the full name
- Use {self.name_tags['FIRST_NAME']} for just the first name 
- Use {self.name_tags['HE_SHE']} for he/she pronouns
- Use {self.name_tags['HIS_HER']} for his/her possessives
- Use {self.name_tags['HIM_HER']} for him/her object pronouns

⚠️ FINAL REMINDER: Use ONLY the exact tags shown above. Do not invent new tag variations.

Current section content (last 600 words):
{section_content[-2500:]}

Broader chapter context:
{existing_content[-1200:]}

Add final 2-3 paragraphs that:
- Synthesize the key points of this section about {self.name_tags['USER_NAME']}
- Reflect on the lasting impact or significance for {self.name_tags['FIRST_NAME']}
- Connect to broader themes in {self.name_tags['HIS_HER']} life journey
- Provide smooth transition to next topics

Create a satisfying conclusion while maintaining narrative momentum.
"""
    
    def _find_relevant_segments_for_chapter(self, segments: List[Dict], themes: List[str]) -> List[Dict]:
        """Find segments most relevant to chapter themes."""
        relevant_segments = []
        
        for segment in segments:
            relevance_score = 0
            segment_themes = segment.get('themes', [])
            
            # Score based on theme overlap
            for theme in themes:
                if theme in segment_themes:
                    relevance_score += 10
                elif any(theme in seg_theme for seg_theme in segment_themes):
                    relevance_score += 5
            
            # Add content-based scoring
            content_lower = segment['content'].lower()
            for theme in themes:
                relevance_score += content_lower.count(theme.lower()) * 2
            
            if relevance_score > 0:
                segment['relevance_score'] = relevance_score
                relevant_segments.append(segment)
        
        # Sort by relevance
        relevant_segments.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        return relevant_segments[:10]  # Top 10 most relevant
    
    async def _phase_4_content_expansion(self, job: BiographyJob):
        """Phase 4: Content expansion and refinement."""
        print("🔧 Phase 4: Content Expansion and Refinement")
        await self._update_job(job, JobStatus.REWRITE_COMPLETE, 75.0)
        
        # Get person name for consistency
        person_name = job.user_name
        print(f"📋 Expanding content for: {person_name}")
        
        # Calculate current length
        chapters_content = job.chapters_content
        if not chapters_content:
            print("⚠️ No chapters content found for expansion")
            return
        
        # Calculate current size
        total_words = 0
        for chapter_data in chapters_content.values():
            content = chapter_data.get('content', '')
            total_words += len(content.split())
        
        current_pages = total_words // self.target_words_per_page
        print(f"📊 Current content: {total_words:,} words (~{current_pages} pages)")
        
        # Determine if expansion is needed
        if current_pages < 400:  # Збільшено мінімум з 250 до 400 сторінок
            print(f"🔧 Expanding content from {current_pages} to target 500+ pages")
            
            # Expand each chapter
            for chapter_key, chapter_data in chapters_content.items():
                expanded_content = await self._expand_chapter_content(
                    chapter_data, person_name, job
                )
                chapters_content[chapter_key]['content'] = expanded_content
                
                # Small delay between chapters
                await asyncio.sleep(2.0)
        
        # Post-process all content for quality improvements
        print("🔍 Post-processing content for quality improvements...")
        for chapter_key, chapter_data in chapters_content.items():
            # Replace name tags with actual names first
            content_with_names = self._replace_name_tags(
                chapter_data['content'], person_name
            )
            
            # Then clean any remaining issues
            cleaned_content = self._clean_content_issues(
                content_with_names, person_name
            )
            chapters_content[chapter_key]['content'] = cleaned_content
        
        # Update job with expanded content
        job.chapters_content = chapters_content
        self.db.commit()
        
        # Final statistics
        final_words = sum(len(chapter['content'].split()) for chapter in chapters_content.values())
        final_pages = final_words // self.target_words_per_page
        print(f"✅ Content expansion completed: {final_words:,} words (~{final_pages} pages)")
        
        await self._update_job(job, JobStatus.REWRITE_COMPLETE, 80.0)
    
    async def _expand_chapter_content(self, chapter_data: Dict, person_name: str, job: BiographyJob) -> str:
        """Expand individual chapter content."""
        chapter_title = chapter_data.get('title', 'Chapter')
        current_content = chapter_data.get('content', '')
        
        current_words = len(current_content.split())
        # More aggressive expansion targets
        target_words = max(6000, current_words * 2.5)  # Increased from 4500 to 6000, multiplier from 2.0 to 2.5
        
        print(f"📝 Expanding '{chapter_title}': {current_words} → {target_words:.0f} words")
        
        # Get interview content for additional context
        interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
        
        # Multiple expansion passes for richer content
        expanded_content = current_content
        
        for expansion_pass in range(3):  # Increased from 2 to 3 expansion passes
            print(f"   🔄 Expansion pass {expansion_pass + 1}/3")
            
            # Create expansion prompt for this pass
            if expansion_pass == 0:
                focus = "detailed scenes, character development, and emotional depth"
            elif expansion_pass == 1:
                focus = "additional anecdotes, context, and vivid descriptions"
            else:
                focus = "rich backstory, relationships, and environmental details"
            
            expansion_prompt = f"""
Expand and enrich this biographical chapter.

CRITICAL NAME USAGE - USE ONLY THESE EXACT TAGS:
- Use {self.name_tags['USER_NAME']} for the full name
- Use {self.name_tags['FIRST_NAME']} for just the first name 
- Use {self.name_tags['HE_SHE']} for he/she pronouns
- Use {self.name_tags['HIS_HER']} for his/her possessives
- Use {self.name_tags['HIM_HER']} for him/her object pronouns

⚠️ CRITICAL: Use ONLY the exact tags shown above. Do NOT create new tags like {{HIM_HERSELF}} or {{HIS_HERS}}.
⚠️ The correct object pronoun tag is {{HIM_HER}}, NOT {{HIM_HERSELF}}.
⚠️ NEVER use placeholders like [Subject], [Name], or generic terms.

EXPANSION REQUIREMENTS:
- Add more {focus}
- Include sensory details and emotional context
- Expand on relationships, challenges, and personal growth
- Add transitional paragraphs for smoother flow
- Maintain the existing narrative structure while adding depth
- Avoid repetitive words like "tapestry", "mosaic", "journey", "palette" - use varied vocabulary

Current Chapter: "{chapter_title}"

Existing Content (last 3000 characters):
{expanded_content[-3000:]}

Interview Context (for additional details):
{interview_text[:4000]}

Expansion Task ({expansion_pass + 1}/3):
Focus on {focus}. Add approximately {(target_words - current_words) // 3:.0f} more words for this pass.

Requirements:
1. Add rich, specific scenes with dialogue where appropriate
2. Include more background context and setting descriptions
3. Develop emotional aspects and internal thoughts of {self.name_tags['USER_NAME']}
4. Add more detailed examples and anecdotes about {self.name_tags['FIRST_NAME']}
5. Create better narrative transitions and flow
6. Include more descriptive language and vivid imagery
7. Expand on relationships and their impact on {self.name_tags['HIS_HER']} life

Use the name tags consistently throughout the expansion.
Avoid overusing metaphorical words - use clear, direct language with varied vocabulary.
"""
            
            try:
                expansion_content = await self.ai_service.generate_completion(
                    expansion_prompt,
                    f"You are expanding a biographical chapter. Use the provided name tags consistently ({self.name_tags['USER_NAME']}, {self.name_tags['FIRST_NAME']}, etc.) instead of any placeholders or other names. Add rich detail while maintaining narrative flow.",
                    max_tokens=4000,  # Increased from 3000 to 4000 for more content
                    temperature=0.8   # Increased from 0.75 to 0.8 for more creativity
                )
                
                # Replace name tags in the generated content
                if expansion_content:
                    expansion_content = self._replace_name_tags(expansion_content, person_name)
                
                # Combine with existing content
                if expansion_content and len(expansion_content.strip()) > 100:
                    expanded_content += f"\n\n{expansion_content}"
                    
                # Small delay between passes
                await asyncio.sleep(1.5)
                    
            except Exception as e:
                print(f"⚠️ Error in expansion pass {expansion_pass + 1} for '{chapter_title}': {e}")
                continue
        
        final_words = len(expanded_content.split())
        print(f"   ✅ Final expansion: {current_words} → {final_words} words")
        
        return expanded_content
    
    def _clean_content_issues(self, content: str, person_name: str) -> str:
        """Clean content issues including [Subject] placeholders and name inconsistencies."""
        
        # Fix [Subject] and other placeholder issues
        cleaned = content
        
        # Replace common placeholder patterns
        placeholder_patterns = [
            (r'\[Subject\]', person_name),
            (r'\[Name\]', person_name),
            (r'\[Person\]', person_name),
            (r'\[Individual\]', person_name),
            (r'\[He/She\]', 'she' if self._is_female_name(person_name) else 'he'),
            (r'\[His/Her\]', 'her' if self._is_female_name(person_name) else 'his'),
            (r'\[Him/Her\]', 'her' if self._is_female_name(person_name) else 'him'),
        ]
        
        for pattern, replacement in placeholder_patterns:
            cleaned = re.sub(pattern, replacement, cleaned, flags=re.IGNORECASE)
        
        # Additional cleanup for remaining name tags that might have been missed
        is_female = self._is_female_name(person_name)
        first_name = person_name.split()[0] if person_name.split() else person_name
        he_she = 'she' if is_female else 'he'
        his_her = 'her' if is_female else 'his'
        him_her = 'her' if is_female else 'him'
        
        # Clean up any remaining malformed name tags
        remaining_tag_patterns = [
            (r'\{HIM_HERSELF\}', him_her),
            (r'\{HIS_HERS\}', his_her),
            (r'\{USER_NAME\}', person_name),
            (r'\{FIRST_NAME\}', first_name),
            (r'\{HE_SHE\}', he_she),
            (r'\{HIS_HER\}', his_her),
            (r'\{HIM_HER\}', him_her),
            (r'\{NAME\}', person_name),
            (r'\{PERSON_NAME\}', person_name),
        ]
        
        for pattern, replacement in remaining_tag_patterns:
            cleaned = re.sub(pattern, replacement, cleaned, flags=re.IGNORECASE)
        
        # More aggressive name inconsistency fixing
        correct_first_name = person_name.split()[0]
        common_wrong_names = ['Sarah', 'Jennifer', 'Michael', 'John', 'Mary', 'Lisa', 'David', 'Susan', 'James', 'Robert', 'William', 'Patricia', 'Christopher', 'Matthew', 'Anthony', 'Mark', 'Donald', 'Steven', 'Paul', 'Andrew', 'Emma', 'Olivia', 'Sophia', 'Isabella', 'Charlotte', 'Amelia', 'Mia', 'Harper', 'Evelyn']
        
        for wrong_name in common_wrong_names:
            if wrong_name != correct_first_name:
                # Replace standalone wrong names with word boundaries
                # More aggressive pattern to catch variations
                patterns_to_replace = [
                    rf'\b{wrong_name}\b',  # Basic word boundary
                    rf'{wrong_name}\'s',   # Possessive
                    rf'{wrong_name},',     # With comma
                    rf'{wrong_name}\.',    # With period
                    rf'"{wrong_name}"',    # In quotes
                    rf"'{wrong_name}'",    # In single quotes
                ]
                
                for pattern in patterns_to_replace:
                    replacement = pattern.replace(wrong_name, correct_first_name)
                    cleaned = re.sub(pattern, replacement, cleaned)
        
        # Enhanced repetitive phrase removal with more comprehensive patterns
        redundant_phrases = [
            # Exact phrase matching
            r'the mosaic of[^.]*?painted a portrait[^.]*?\.',
            r'the tapestry of[^.]*?woven together[^.]*?\.',
            r'the journey[^.]*?path[^.]*?destination[^.]*?\.',
            r'reflected on[^.]*?realized that[^.]*?gateway[^.]*?\.',
            
            # Word frequency reduction patterns
            r'\btapestry\b(?=[^.]*\btapestry\b)',  # Remove duplicate tapestry in same sentence
            r'\bmosaic\b(?=[^.]*\bmosaic\b)',      # Remove duplicate mosaic in same sentence
            r'\bjourney\b(?=[^.]*\bjourney\b)',    # Remove duplicate journey in same sentence
            r'\bpalette\b(?=[^.]*\bpalette\b)',    # Remove duplicate palette in same sentence
        ]
        
        for phrase_pattern in redundant_phrases:
            cleaned = re.sub(phrase_pattern, '', cleaned, flags=re.IGNORECASE | re.DOTALL)
        
        # Replace overused metaphorical words with alternatives
        word_replacements = {
            'tapestry': ['fabric', 'story', 'narrative', 'composition', 'blend'],
            'mosaic': ['collection', 'array', 'combination', 'mixture', 'assembly'],
            'journey': ['experience', 'progression', 'development', 'evolution', 'path'],
            'palette': ['range', 'spectrum', 'variety', 'selection', 'array'],
            'woven': ['integrated', 'combined', 'blended', 'merged', 'connected'],
            'painted': ['created', 'formed', 'shaped', 'crafted', 'built']
        }
        
        # Count word usage and replace excess occurrences
        for word, alternatives in word_replacements.items():
            word_count = len(re.findall(rf'\b{word}\b', cleaned, re.IGNORECASE))
            if word_count > 3:  # If word appears more than 3 times, replace some
                # Replace every 3rd occurrence with an alternative
                replacement_count = 0
                alt_index = 0
                
                def replace_function(match):
                    nonlocal replacement_count, alt_index
                    replacement_count += 1
                    if replacement_count % 3 == 0:  # Every 3rd occurrence
                        alt_word = alternatives[alt_index % len(alternatives)]
                        alt_index += 1
                        return alt_word
                    return match.group(0)
                
                cleaned = re.sub(rf'\b{word}\b', replace_function, cleaned, flags=re.IGNORECASE)
        
        # Clean up any double spaces or excessive line breaks
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned)
        
        # Remove any leftover fragments from aggressive cleaning
        cleaned = re.sub(r'\s*\.\s*\.', '.', cleaned)  # Double periods
        cleaned = re.sub(r'^\s*\.\s*', '', cleaned, flags=re.MULTILINE)
        
        return cleaned.strip()
    
    def _is_female_name(self, name: str) -> bool:
        """Simple heuristic to determine if name is likely female."""
        first_name = name.split()[0].lower()
        female_indicators = ['jennifer', 'sarah', 'mary', 'lisa', 'susan', 'maria', 'anna', 'emma', 'olivia']
        male_indicators = ['john', 'michael', 'david', 'james', 'robert', 'william', 'thomas', 'richard']
        
        if first_name in female_indicators:
            return True
        elif first_name in male_indicators:
            return False
        else:
            # Default to female for ambiguous names (can be improved with better name database)
            return first_name.endswith(('a', 'e', 'ie', 'y'))
    
    async def _phase_5_final_assembly_pdf(self, job: BiographyJob):
        """Phase 5: Final assembly and PDF generation."""
        print("📖 Phase 5: Final Assembly and PDF Generation")
        await self._update_job(job, JobStatus.GENERATING_PDF, 85.0)
        
        if not job.chapters_content:
            raise Exception("No chapters content found for assembly")
        
        # Assemble final biography
        combined_biography = self._assemble_enhanced_biography(job.chapters_content)
        
        # FINAL REPETITION CHECK AND CLEANUP
        print(f"🔍 [FINAL CLEANUP] Starting final repetition check...")
        cleaned_biography = self._final_repetition_check_and_cleanup(combined_biography)
        
        if len(cleaned_biography) != len(combined_biography):
            print(f"🧹 [FINAL CLEANUP] Removed repetitive content:")
            print(f"   Original: {len(combined_biography):,} chars")
            print(f"   Cleaned: {len(cleaned_biography):,} chars")
            print(f"   Removed: {len(combined_biography) - len(cleaned_biography):,} chars")
        else:
            print(f"✅ [FINAL CLEANUP] No repetitive content found - content is clean")
        
        # Store final content
        job.final_biography_content = cleaned_biography
        
        # Get existing metadata
        metadata = json.loads(job.evaluation_content) if job.evaluation_content else {}
        
        # Generate enhanced PDF
        # Always use user_name from the form instead of extracted name
        person_name = job.user_name
        
        # Reconstruct chapter data for PDF
        chapter_data = []
        if job.chapters_content:
            for key, chapter in job.chapters_content.items():
                chapter_data.append({
                    "number": chapter["number"],
                    "title": chapter["title"],
                    "description": f"Chapter covering {chapter['title'].lower()}"
                })
        
        # Generate PDF
        output_filename = f"enhanced_biography_{job.id}_{int(time.time())}.pdf"
        output_path = f"./output/{output_filename}"
        
        success = PDFService.generate_enhanced_biography_pdf(
            combined_biography, person_name, chapter_data, output_path
        )
        
        if success:
            job.output_pdf_path = output_path
            
            # Calculate final statistics
            final_word_count = len(combined_biography.split())
            final_page_estimate = final_word_count // self.target_words_per_page
            
            # Update metadata with final statistics
            metadata.update({
                "final_word_count": final_word_count,
                "final_page_estimate": final_page_estimate,
                "final_character_count": len(combined_biography),
                "chapters_generated": len(job.chapters_content),
                "generation_completed_at": datetime.utcnow().isoformat()
            })
            job.evaluation_content = json.dumps(metadata)
            
            print(f"✅ Enhanced biography PDF generated:")
            print(f"   • File: {output_path}")
            print(f"   • Content: {final_word_count:,} words (~{final_page_estimate} pages)")
            print(f"   • Chapters: {len(job.chapters_content)}")
        else:
            raise Exception("Failed to generate enhanced PDF")
        
        await self._update_job(job, JobStatus.GENERATING_PDF, 95.0)
    
    def _assemble_enhanced_biography(self, chapters_content: Dict) -> str:
        """Assemble enhanced biography with rich formatting."""
        combined = ""
        
        # Sort chapters by number
        sorted_chapters = sorted(
            chapters_content.items(),
            key=lambda x: x[1]["number"]
        )
        
        # Add title page content
        combined += "# Biography\n\n"
        combined += "## A Life Story\n\n"
        combined += "---\n\n"
        
        # Add each chapter
        for chapter_key, chapter_data in sorted_chapters:
            chapter_title = chapter_data['title']
            chapter_number = chapter_data['number']
            chapter_content = chapter_data["content"]
            
            # Enhanced chapter formatting
            combined += f"\n\n# Chapter {chapter_number}: {chapter_title}\n\n"
            combined += chapter_content
            combined += "\n\n" + "="*80 + "\n\n"
        
        return combined
    
    def _find_detailed_session_markers(self, text: str) -> List[Dict]:
        """Find detailed session markers in interview text."""
        markers = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            line_clean = line.strip().lower()
            if any(marker in line_clean for marker in [
                'session', 'ai:', 'user:', 'eternal ai', 'welcome back',
                '--- page', 'chapter', 'part', 'interview'
            ]):
                markers.append({
                    'line_number': i,
                    'content': line.strip(),
                    'type': self._classify_marker_type(line_clean),
                    'position': text.find(line)
                })
        
        return markers
    
    def _classify_marker_type(self, line: str) -> str:
        """Classify the type of session marker."""
        if 'session' in line:
            return 'session_break'
        elif any(x in line for x in ['ai:', 'eternal ai']):
            return 'ai_response'
        elif 'user:' in line:
            return 'user_input'
        elif 'page' in line:
            return 'page_break'
        else:
            return 'dialogue'
    
    def _extract_improved_name(self, pdf_path: str, interview_text: str) -> str:
        """Extract person name with improved AI filtering logic."""
        try:
            # Enhanced patterns excluding AI names
            excluded_patterns = [
                r'alfred\s+from\s+eternal',
                r'eternal\s+ai',
                r'ai\s+assistant',
                r'chatgpt',
                r'assistant'
            ]
            
            # Look for user introductions
            user_intro_patterns = [
                r'user:\s*(?:my name is |i am |i\'m |this is )?([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
                r'user:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*here',
                r'hello.*?i\'m\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            ]
            
            text_lower = interview_text.lower()
            
            # Try user introduction patterns first
            for pattern in user_intro_patterns:
                matches = re.findall(pattern, interview_text, re.IGNORECASE)
                for match in matches:
                    # Skip if it's an excluded AI name
                    if not any(re.search(excl, match.lower()) for excl in excluded_patterns):
                        if len(match.split()) <= 3:  # Reasonable name length
                            return match.strip()
            
            # Fallback to filename extraction
            import os
            filename = os.path.basename(pdf_path)
            name_from_filename = re.search(r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)', filename)
            if name_from_filename:
                potential_name = name_from_filename.group(1)
                excluded_words = ['interview', 'transcript', 'session', 'eternal', 'document']
                if potential_name.lower() not in excluded_words:
                    return potential_name
            
            return "Unknown Person"
            
        except Exception as e:
            print(f"⚠️ Name extraction error: {e}")
            return "Unknown Person"
    
    async def _update_job(self, job: BiographyJob, status: JobStatus, progress: float):
        """Update job status and progress."""
        job.status = status
        job.progress_percentage = progress
        job.updated_at = datetime.utcnow()
        self.db.commit()
        
        # Send WebSocket update
        try:
            await manager.broadcast_job_update(job.id)
        except Exception as e:
            print(f"⚠️ WebSocket update failed: {e}")
    
    async def resume_enhanced_biography(self, job_id: str) -> bool:
        """Resume an interrupted enhanced biography generation."""
        print(f"🔄 Resuming enhanced biography generation for job {job_id}")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            print(f"❌ Job {job_id} not found")
            return False
        
        if job.status not in [JobStatus.PROCESSING, JobStatus.FAILED]:
            print(f"⚠️ Job {job_id} is in status {job.status}, cannot resume")
            return False
        
        try:
            # Reset status to processing
            await self._update_job(job, JobStatus.PROCESSING, job.progress_percentage)
            
            # Continue from where we left off
            return await self.process_enhanced_biography(job_id)
            
        except Exception as e:
            print(f"❌ Failed to resume job {job_id}: {e}")
            await self._update_job(job, JobStatus.FAILED, job.progress_percentage)
            return False

    # ========== ANTI-REPETITION IMPROVEMENTS ==========
    
    def _detect_content_repetition(self, new_content: str, existing_content: str) -> float:
        """Detect potential repetition in section content."""
        if not existing_content or not new_content:
            return 0.0
        
        new_content_lower = new_content.lower()
        existing_content_lower = existing_content.lower()
        
        # Extract key phrases from new content
        new_sentences = [s.strip() for s in new_content.split('.') if len(s.strip()) > 20]
        existing_sentences = [s.strip() for s in existing_content.split('.') if len(s.strip()) > 20]
        
        repetition_score = 0.0
        
        # Check for similar sentence structures
        for new_sentence in new_sentences[:5]:  # Check first 5 sentences
            new_words = set(new_sentence.lower().split())
            
            for existing_sentence in existing_sentences[-10:]:  # Check recent sentences
                existing_words = set(existing_sentence.lower().split())
                
                if len(new_words) > 3 and len(existing_words) > 3:
                    overlap = len(new_words.intersection(existing_words))
                    similarity = overlap / len(new_words.union(existing_words))
                    
                    if similarity > 0.4:  # High similarity threshold
                        repetition_score += similarity
        
        return min(repetition_score, 1.0)  # Cap at 1.0

    def _calculate_adaptive_iterations(self, section: Dict, relevant_segments: List[Dict]) -> int:
        """Calculate adaptive number of iterations for a section."""
        base_iterations = 4  # Increased minimum from 3 to 4 for longer content
        
        # Factors that increase iterations
        bonus_iterations = 0
        
        # More iterations for sections with rich source material
        if len(relevant_segments) > 8:  # Rich content
            bonus_iterations += 2
        elif len(relevant_segments) > 4:
            bonus_iterations += 1
            
        # More iterations for important sections (early chapters)
        section_focus = section.get('focus', '')
        important_keywords = ['childhood', 'family', 'early', 'foundation', 'birth', 'background']
        if any(keyword in section_focus.lower() for keyword in important_keywords):
            bonus_iterations += 1
            
        # Fewer iterations for sections with limited relevant content
        high_relevance_count = len([s for s in relevant_segments if s.get('relevance_score', 0) > 5])
        if high_relevance_count < 2:
            bonus_iterations -= 1
            
        # Final calculation with bounds - increased maximum to 7
        optimal = max(4, min(7, base_iterations + bonus_iterations))
        
        return optimal

    def _get_iteration_focus(self, iteration: int, total_iterations: int) -> str:
        """Get specific focus for each iteration to encourage diverse content."""
        if total_iterations <= 3:
            focuses = [
                "foundational events and setting the scene",
                "personal relationships and emotional development", 
                "reflection and connection to broader life themes"
            ]
        else:
            focuses = [
                "foundational events and setting the scene",
                "personal relationships and emotional aspects", 
                "challenges, conflicts, and how they were overcome",
                "achievements, growth, and positive developments",
                "reflection, lessons learned, and deeper insights",
                "connecting to broader life themes and future impact"
            ]
        
        focus_index = iteration % len(focuses)
        return focuses[focus_index]

    def _enhance_prompt_for_diversity(self, base_prompt: str, repetition_score: float, iteration: int) -> str:
        """Enhance prompt with specific instructions to avoid repetition."""
        if repetition_score < 0.2:
            return base_prompt
        
        diversity_instructions = ""
        
        if repetition_score > 0.6:
            diversity_instructions = """
⚠️ HIGH REPETITION DETECTED ⚠️
CRITICAL: This content appears similar to previous iterations. 
- Focus on COMPLETELY NEW aspects of this life period
- Introduce different characters, events, or perspectives
- Use varied sentence structures and vocabulary
- Explore unexplored dimensions of the themes
"""
        elif repetition_score > 0.3:
            diversity_instructions = """
⚠️ MODERATE REPETITION DETECTED ⚠️
- Shift focus to different aspects of the same themes
- Introduce new examples or stories
- Vary your narrative approach and style
- Explore different emotional or chronological angles
"""
        
        return base_prompt + "\n\n" + diversity_instructions

    def _replace_name_tags(self, content: str, person_name: str) -> str:
        """Replace name tags with actual person information."""
        if not person_name:
            return content
        
        # Extract components
        name_parts = person_name.split()
        first_name = name_parts[0] if name_parts else person_name
        full_name = person_name
        
        # Determine gender pronouns
        is_female = self._is_female_name(first_name)
        he_she = 'she' if is_female else 'he'
        his_her = 'her' if is_female else 'his'
        him_her = 'her' if is_female else 'him'
        
        # Replace all name tags
        replacements = {
            self.name_tags['USER_NAME']: full_name,
            self.name_tags['FIRST_NAME']: first_name,
            self.name_tags['HE_SHE']: he_she,
            self.name_tags['HIS_HER']: his_her,
            self.name_tags['HIM_HER']: him_her,
            
        }
        
        # Apply replacements
        result = content
        for tag, replacement in replacements.items():
            result = result.replace(tag, replacement)
        
        # Additional cleanup for malformed tags that AI might generate
        malformed_tag_patterns = [
            (r'\{HIM_HERSELF\}', him_her),
            (r'\{HIS_HERS\}', his_her),
            (r'\{HE_SHE_THEY\}', he_she),
            (r'\{USER_FIRST_NAME\}', first_name),
            (r'\{PERSON_NAME\}', full_name),
            (r'\{NAME\}', full_name),
            (r'\{FIRST\}', first_name),
            (r'\{HIM_HER_THEM\}', him_her),
            (r'\{HIS_HER_THEIR\}', his_her),
        ]
        
        for pattern, replacement in malformed_tag_patterns:
            result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)
        
        # Clean up any remaining unrecognized tags in format {WORD_WORD}
        # This catches tags AI might invent that we haven't seen before
        def replace_unknown_tags(match):
            tag_content = match.group(1)
            print(f"⚠️ [NAME TAG WARNING] Found unrecognized tag: {{{tag_content}}} - replacing with appropriate pronoun")
            
            # Try to guess intent based on tag content
            tag_lower = tag_content.lower()
            if any(word in tag_lower for word in ['him', 'her', 'object']):
                return him_her
            elif any(word in tag_lower for word in ['his', 'hers', 'possess']):
                return his_her
            elif any(word in tag_lower for word in ['he', 'she', 'subject']):
                return he_she
            elif any(word in tag_lower for word in ['name', 'user', 'person']):
                if 'first' in tag_lower:
                    return first_name
                else:
                    return full_name
            else:
                # Default fallback
                return first_name
        
        # Pattern to catch any remaining {WORD_WORD} style tags
        result = re.sub(r'\{([A-Z_]+)\}', replace_unknown_tags, result)
        
        return result

    def _final_repetition_check_and_cleanup(self, biography_content: str) -> str:
        """Final check to remove all repetitive content before PDF generation."""
        if not biography_content or not biography_content.strip():
            return biography_content
        
        print(f"🔍 [FINAL CLEANUP] Analyzing {len(biography_content):,} characters for repetition...")
        
        # Split content into paragraphs
        paragraphs = [p.strip() for p in biography_content.split('\n\n') if p.strip()]
        
        # Track unique paragraph hashes to detect exact duplicates
        unique_paragraphs = []
        seen_hashes = set()
        
        # Track similar paragraphs using similarity threshold
        removed_count = 0
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) < 50:  # Keep very short paragraphs (likely headers/transitions)
                unique_paragraphs.append(paragraph)
                continue
            
            # Create normalized hash for exact duplicate detection
            normalized = re.sub(r'[^\w\s]', '', paragraph.lower())
            normalized = re.sub(r'\s+', ' ', normalized).strip()
            paragraph_hash = hash(normalized)
            
            # Check for exact duplicates
            if paragraph_hash in seen_hashes:
                print(f"🗑️ [FINAL CLEANUP] Removing exact duplicate paragraph {i+1}")
                removed_count += 1
                continue
            
            # Check for high similarity to existing paragraphs
            is_similar = False
            for existing_para in unique_paragraphs[-10:]:  # Check last 10 paragraphs only
                if len(existing_para) < 50:
                    continue
                    
                similarity = self._calculate_content_similarity(paragraph, existing_para)
                if similarity > 0.8:  # Very high similarity threshold for final cleanup
                    print(f"🗑️ [FINAL CLEANUP] Removing highly similar paragraph {i+1} (similarity: {similarity:.2f})")
                    is_similar = True
                    removed_count += 1
                    break
            
            if not is_similar:
                unique_paragraphs.append(paragraph)
                seen_hashes.add(paragraph_hash)
        
        # Reconstruct cleaned content
        cleaned_content = '\n\n'.join(unique_paragraphs)
        
        # Additional cleanup: Remove chapter headers that are numbered > 6
        lines = cleaned_content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line_stripped = line.strip()
            # Skip chapter headers with numbers > 6
            if re.match(r'^Chapter\s+([7-9]|\d{2,})\s*:', line_stripped, re.IGNORECASE):
                print(f"🗑️ [FINAL CLEANUP] Removing chapter header: {line_stripped}")
                continue
            # Skip standalone chapter numbers > 6
            elif re.match(r'^([7-9]|\d{2,})\s*$', line_stripped):
                print(f"🗑️ [FINAL CLEANUP] Removing chapter number: {line_stripped}")
                continue
            else:
                cleaned_lines.append(line)
        
        final_cleaned_content = '\n'.join(cleaned_lines)
        
        print(f"📊 [FINAL CLEANUP] Cleanup complete:")
        print(f"   Original paragraphs: {len(paragraphs)}")
        print(f"   Removed paragraphs: {removed_count}")
        print(f"   Final paragraphs: {len(unique_paragraphs)}")
        print(f"   Character reduction: {len(biography_content) - len(final_cleaned_content):,} chars")
        
        return final_cleaned_content

    def _calculate_content_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text pieces to detect repetition."""
        if not text1 or not text2:
            return 0.0
        
        # Simple word-based similarity check
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0