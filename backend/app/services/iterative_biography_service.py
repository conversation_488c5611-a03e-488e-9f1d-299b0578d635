from sqlalchemy.orm import Session
from app.models.biography_job import <PERSON><PERSON><PERSON>, JobStatus
from app.models.agent_prompt import Agent<PERSON>rompt
from app.services.ai_service import AIService
from app.services.pdf_service import PDFService
from app.services.websocket_manager import manager
from typing import Optional, Dict, List, Tuple
import os
import time
import json
from datetime import datetime
import asyncio
import math

class IterativeBiographyService:
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService(db)
        # Configuration for large interview processing (reduced for token limits)
        self.max_segment_size = 8000   # characters per segment (reduced for token safety)
        self.segment_overlap = 300     # overlap between segments to maintain context
        
    async def process_iterative_biography_job(self, job_id: str) -> bool:
        """Process a biography generation job with iterative chapter generation and large interview support."""
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        start_time = time.time()
        
        try:
            # Job is already in PROCESSING status, just update progress
            await self._update_job(job, JobStatus.PROCESSING, 5.0)
            
            # Extract and analyze interview text
            interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
            if not interview_text.strip():
                raise Exception("Could not extract text from uploaded PDF")
            
            # Always use user_name from the form instead of extracting from PDF
            actual_person_name = job.user_name
            print(f"📝 Using user name from form: {actual_person_name}")
            
            # Analyze interview size and determine processing strategy
            interview_analysis = self._analyze_interview_structure(interview_text)
            await self._update_job(job, JobStatus.PROCESSING, 10.0)
            
            # Get agent prompts
            prompts = self._get_agent_prompts()
            
            # Step 1: Enhanced Outline Agent with chapter structure (adapted for large interviews)
            await self._update_job(job, JobStatus.PROCESSING, 15.0)
            outline_data = await self._process_enhanced_outline_agent_for_large_interview(
                interview_text, 
                interview_analysis,
                prompts["outline"]
            )
            job.outline_content = outline_data["outline"]
            job.total_chapters = outline_data["chapter_count"]
            job.completed_chapters = 0
            await self._update_job(job, JobStatus.OUTLINE_COMPLETE, 20.0)
            
            # Step 2: Iterative Chapter Generation with segmented interview processing
            await self._update_job(job, JobStatus.ITERATIVE_WRITING, 25.0)
            chapters_content = await self._process_iterative_chapters_with_segments(
                job, interview_text, interview_analysis, outline_data["chapters"], prompts["writer"]
            )
            job.chapters_content = chapters_content
            await self._update_job(job, JobStatus.WRITING_COMPLETE, 80.0)
            
            # Step 3: Combine chapters into full biography
            full_biography = self._combine_chapters(chapters_content)
            job.biography_content = full_biography
            
            # Step 4: Enhanced Evaluation
            await self._update_job(job, JobStatus.PROCESSING, 85.0)
            evaluation = await self._process_enhanced_evaluation_agent(
                interview_text, full_biography, prompts["evaluation"]
            )
            job.evaluation_content = evaluation
            await self._update_job(job, JobStatus.EVALUATION_COMPLETE, 90.0)
            
            # Step 5: For iterative mode, use full biography directly 
            # (each chapter already went through 10 iterations of refinement)
            await self._update_job(job, JobStatus.PROCESSING, 92.0)
            
            # FINAL REPETITION CHECK AND CLEANUP
            print(f"🔍 [FINAL CLEANUP] Starting final repetition check...")
            cleaned_biography = self._final_repetition_check_and_cleanup(full_biography)
            
            if len(cleaned_biography) != len(full_biography):
                print(f"🧹 [FINAL CLEANUP] Removed repetitive content:")
                print(f"   Original: {len(full_biography):,} chars")
                print(f"   Cleaned: {len(cleaned_biography):,} chars")
                print(f"   Removed: {len(full_biography) - len(cleaned_biography):,} chars")
            else:
                print(f"✅ [FINAL CLEANUP] No repetitive content found - content is clean")
            
            job.final_biography_content = cleaned_biography  # Use cleaned content
            await self._update_job(job, JobStatus.REWRITE_COMPLETE, 95.0)
            
            # Step 6: Generate Enhanced PDF with correct name
            await self._update_job(job, JobStatus.GENERATING_PDF, 97.0)
            output_filename = f"biography_{job.id}_{int(time.time())}.pdf"
            output_path = os.path.join("./output", output_filename)
            
            success = PDFService.generate_enhanced_biography_pdf(
                full_biography,  # Use full combined biography
                actual_person_name,  # Use extracted name instead of job.user_name
                outline_data["chapters"],
                output_path
            )
            
            if success:
                job.output_pdf_path = output_path
                job.completed_at = datetime.utcnow()
                job.processing_time_seconds = time.time() - start_time
                await self._update_job(job, JobStatus.COMPLETED, 100.0)
                return True
            else:
                raise Exception("Failed to generate PDF")
                
        except Exception as e:
            job.error_message = str(e)
            job.processing_time_seconds = time.time() - start_time
            await self._update_job(job, JobStatus.FAILED, job.progress_percentage)
            return False

    def _analyze_interview_structure(self, interview_text: str) -> Dict:
        """Analyze the interview structure to determine optimal processing strategy."""
        text_length = len(interview_text)
        word_count = len(interview_text.split())
        
        # Detect session breaks
        session_markers = []
        lines = interview_text.split('\n')
        
        for i, line in enumerate(lines):
            line_clean = line.strip().lower()
            # Look for session indicators
            if any(marker in line_clean for marker in [
                'session', 'ai:', 'user:', 'eternal ai', 'welcome back',
                '--- page', 'chapter', 'part'
            ]):
                session_markers.append({
                    'line_number': i,
                    'content': line.strip(),
                    'marker_type': self._classify_marker(line.strip())
                })
        
        # Determine if we need segmented processing
        needs_segmentation = text_length > self.max_segment_size
        
        # Calculate optimal segments
        if needs_segmentation:
            segments = self._create_interview_segments(interview_text, session_markers)
        else:
            segments = [{'start': 0, 'end': text_length, 'content': interview_text}]
        
        return {
            'total_length': text_length,
            'word_count': word_count,
            'needs_segmentation': needs_segmentation,
            'session_markers': session_markers,
            'segments': segments,
            'estimated_biography_pages': min(max(word_count // 250, 50), 500)  # 250 words per page estimate
        }

    def _classify_marker(self, line: str) -> str:
        """Classify the type of marker found in the interview."""
        line_lower = line.lower()
        if 'session' in line_lower:
            return 'session_break'
        elif 'ai:' in line_lower or 'user:' in line_lower:
            return 'speaker_change'
        elif 'chapter' in line_lower:
            return 'chapter_marker'
        elif 'page' in line_lower:
            return 'page_marker'
        else:
            return 'content_marker'

    def _create_interview_segments(self, interview_text: str, session_markers: List[Dict]) -> List[Dict]:
        """Create optimal segments for processing large interviews."""
        segments = []
        text_length = len(interview_text)
        
        if not session_markers:
            # No clear session breaks, use character-based segmentation
            return self._create_character_based_segments(interview_text)
        
        # Use session markers to create logical segments
        segment_start = 0
        current_segment_size = 0
        
        for i, marker in enumerate(session_markers):
            marker_position = interview_text.find(marker['content'])
            if marker_position == -1:
                continue
                
            # Calculate segment size up to this marker
            segment_size = marker_position - segment_start
            
            # If adding this section would exceed max size, create a segment
            if current_segment_size + segment_size > self.max_segment_size and current_segment_size > 0:
                # Create segment with overlap
                segment_end = segment_start + current_segment_size + self.segment_overlap
                segments.append({
                    'start': segment_start,
                    'end': min(segment_end, text_length),
                    'content': interview_text[segment_start:min(segment_end, text_length)],
                    'session_info': f"Sessions {len(segments)+1}"
                })
                
                # Start new segment with overlap
                segment_start = max(0, segment_start + current_segment_size - self.segment_overlap)
                current_segment_size = marker_position - segment_start
            else:
                current_segment_size += segment_size
        
        # Add final segment
        if segment_start < text_length:
            segments.append({
                'start': segment_start,
                'end': text_length,
                'content': interview_text[segment_start:],
                'session_info': f"Final Sessions"
            })
        
        return segments

    def _create_character_based_segments(self, interview_text: str) -> List[Dict]:
        """Create segments based on character count when no clear breaks are found."""
        segments = []
        text_length = len(interview_text)
        
        for start in range(0, text_length, self.max_segment_size - self.segment_overlap):
            end = min(start + self.max_segment_size, text_length)
            
            # Try to break at sentence end
            if end < text_length:
                # Look for sentence break within last 200 characters
                segment_text = interview_text[start:end]
                last_period = segment_text.rfind('.')
                last_newline = segment_text.rfind('\n')
                
                if last_period > len(segment_text) - 200:
                    end = start + last_period + 1
                elif last_newline > len(segment_text) - 200:
                    end = start + last_newline + 1
            
            segments.append({
                'start': start,
                'end': end,
                'content': interview_text[start:end],
                'segment_info': f"Segment {len(segments)+1}"
            })
            
            if end >= text_length:
                break
        
        return segments

    async def _process_enhanced_outline_agent_for_large_interview(
        self, 
        interview_text: str, 
        interview_analysis: Dict,
        prompt: str
    ) -> Dict:
        """Enhanced outline agent optimized for large interviews."""
        
        # Create a comprehensive summary for outline generation
        if interview_analysis['needs_segmentation']:
            interview_summary = await self._create_interview_summary(
                interview_analysis['segments']
            )
        else:
            interview_summary = interview_text[:10000]  # Use first 10k characters
        
        # Adjust chapter count based on interview size
        estimated_pages = interview_analysis['estimated_biography_pages']
        recommended_chapters = min(max(estimated_pages // 35, 8), 12)  # 8-12 chapters for large interviews
        
        system_message = f"""You are the Enhanced Biography Outliner agent for a comprehensive {estimated_pages}-page biography.
        
        The interview contains {interview_analysis['word_count']:,} words and requires {recommended_chapters} chapters.
        
        CRITICAL: Your response MUST be valid JSON only. Do not include any explanatory text before or after the JSON.
        
        Return ONLY this exact JSON structure:
        {{
            "outline": "detailed outline text",
            "chapters": [
                {{
                    "number": 1,
                    "title": "Chapter Title",
                    "description": "What this chapter covers",
                    "key_themes": ["theme1", "theme2"],
                    "estimated_sections": 3,
                    "time_period": "childhood/early years/etc"
                }}
            ],
            "chapter_count": {recommended_chapters}
        }}
        """
        
        full_prompt = f"""
{prompt}

=========== INTERVIEW ANALYSIS ===========
Interview Length: {interview_analysis['word_count']:,} words
Estimated Biography Length: {estimated_pages} pages
Recommended Chapters: {recommended_chapters}

=========== INTERVIEW CONTENT SUMMARY ===========
{interview_summary}

INSTRUCTIONS:
- Create {recommended_chapters} chapters for a comprehensive book-length biography
- Each chapter should cover 40-50 pages of final content
- Return ONLY valid JSON, no other text
- Base chapters on the extensive interview content
- Include meaningful themes and time periods for each chapter

RESPOND WITH JSON ONLY:
"""
        
        response = await self.ai_service.generate_completion(
            full_prompt, system_message  # Auto-detect max tokens
        )
        
        # Log AI response before parsing
        print(f"\n🤖 [OUTLINE DEBUG] AI response received for large interview outline:")
        print(f"📏 [OUTLINE DEBUG] Response length: {len(response)} chars")
        print(f"📄 [OUTLINE DEBUG] Response preview: {response[:300]}...")
        
        try:
            # Try to extract JSON from response
            import re
            print(f"🔍 [OUTLINE DEBUG] Starting JSON parsing for large interview outline...")
            
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_content = json_match.group()
                print(f"✅ [OUTLINE DEBUG] Found JSON match")
                print(f"📄 [OUTLINE DEBUG] JSON preview: {json_content[:200]}...")
                
                outline_data = json.loads(json_content)
                print(f"✅ [OUTLINE DEBUG] Successfully parsed JSON")
                print(f"📊 [OUTLINE DEBUG] Parsed data keys: {list(outline_data.keys())}")
                if 'chapters' in outline_data:
                    print(f"📚 [OUTLINE DEBUG] Found {len(outline_data['chapters'])} chapters")
                return outline_data
            else:
                print(f"❌ [OUTLINE DEBUG] No JSON pattern found, using fallback")
                print(f"📄 [OUTLINE DEBUG] Full AI response:")
                print(f"---START---")
                print(response)
                print(f"---END---")
                # Fallback: create structure from text response
                return self._parse_outline_fallback_large(response, recommended_chapters)
        except json.JSONDecodeError as e:
            print(f"❌ [OUTLINE DEBUG] JSON decode error: {e}")
            print(f"📄 [OUTLINE DEBUG] Failed JSON content: {json_match.group() if json_match else 'No match'}...")
            print(f"📄 [OUTLINE DEBUG] Using fallback parsing")
            return self._parse_outline_fallback_large(response, recommended_chapters)

    async def _create_interview_summary(self, segments: List[Dict]) -> str:
        """Create a comprehensive summary from interview segments with token safety."""
        summaries = []
        
        # Process first few segments to create overview
        for i, segment in enumerate(segments[:2]):  # Process only first 2 segments to stay within limits
            # Limit segment content to 3000 characters for safety
            segment_content = segment['content'][:3000]
            if len(segment['content']) > 3000:
                segment_content += "..."
            
            summary_prompt = f"""
            Summarize the key life events, themes, and chronological information from this interview segment:

            {segment_content}

            Focus on:
            - Major life events and time periods
            - Key relationships and family information
            - Career and education milestones
            - Significant experiences and turning points
            
            Provide a concise but comprehensive summary in 2-3 paragraphs.
            """
            
            summary = await self.ai_service.generate_completion(
                summary_prompt, 
                "You are summarizing interview content to help create a biography outline."
                # Auto-detect max tokens
            )
            summaries.append(f"Segment {i+1}: {summary}")
        
        return "\n\n".join(summaries)

    async def _process_iterative_chapters_with_segments(
        self, 
        job: BiographyJob, 
        interview_text: str, 
        interview_analysis: Dict,
        chapters: List[Dict], 
        writer_prompt: str
    ) -> Dict[str, str]:
        """Process each chapter with segmented interview data and iterations."""
        chapters_content = {}
        all_theses = {}
        
        for i, chapter in enumerate(chapters):
            chapter_key = f"chapter_{chapter['number']}"
            
            # Generate chapter with relevant interview segments
            chapter_content, chapter_theses = await self._generate_chapter_with_segments(
                interview_analysis['segments'], chapter, writer_prompt, all_theses
            )
            
            chapters_content[chapter_key] = {
                "title": chapter["title"],
                "content": chapter_content,
                "number": chapter["number"]
            }
            all_theses[chapter_key] = chapter_theses
            
            # Update progress
            progress = 25.0 + ((i + 1) / len(chapters)) * 55.0
            job.completed_chapters = i + 1
            job.chapter_theses = all_theses
            await self._update_job(job, JobStatus.ITERATIVE_WRITING, progress)
            
        return chapters_content

    async def _generate_chapter_with_segments(
        self,
        segments: List[Dict],
        chapter: Dict,
        writer_prompt: str,
        existing_theses: Dict[str, List[str]]
    ) -> Tuple[str, List[str]]:
        """Generate complete chapter content with natural book-like flow (replacing iterative approach)."""
        
        # Find relevant segments for this chapter
        relevant_segments = await self._find_relevant_segments_for_chapter(segments, chapter)
        
        # Collect all existing themes to avoid repetition
        all_existing_theses = []
        for chapter_theses in existing_theses.values():
            all_existing_theses.extend(chapter_theses)
        
        print(f"📖 Generating complete chapter {chapter['number']}: {chapter['title']}")
        print(f"📊 Using {len(relevant_segments)} relevant segments for content")
        
        # Create comprehensive chapter prompt for natural book-like writing
        chapter_prompt = f"""
{writer_prompt}

=========== CHAPTER INFORMATION ===========
Chapter {chapter['number']}: {chapter['title']}
Description: {chapter['description']}
Key Themes: {', '.join(chapter['key_themes'][:5])}
Time Period: {chapter.get('time_period', 'Not specified')}

=========== RELEVANT INTERVIEW SEGMENTS ===========
{self._format_segments_for_prompt(relevant_segments)}

=========== PREVIOUSLY COVERED THEMES (AVOID REPETITION) ===========
{chr(10).join(all_existing_theses[-8:]) if all_existing_theses else "No previous themes - this is early in the biography."}

=========== CHAPTER GENERATION INSTRUCTIONS ===========
Write a COMPLETE chapter as a single, flowing narrative that reads like a chapter from a professionally published biography. 

**CRITICAL REQUIREMENTS:**
• Write as ONE continuous narrative - do not break into sections or iterations
• Use VARIED paragraph lengths naturally (some short, some long) like a real book
• Target 3000-5000 words for a substantial, comprehensive chapter
• Create natural paragraph breaks based on content flow, not artificial divisions
• Start with an engaging opening that draws readers in
• End with a natural conclusion or transition to the next life period
• Weave quotes and anecdotes seamlessly throughout the narrative
• Maintain chronological flow while allowing for natural digressions
• Focus on authentic storytelling with rich detail and emotional truth

**AVOID:**
• Uniform paragraph lengths (this is critical!)
• Artificial section breaks or headers within the chapter
• Repetitive content from previous chapters
• Generic or bland storytelling
• Lists or bullet points - write in narrative form

This should read like a chapter from a Walter Isaacson biography - engaging, detailed, and naturally flowing.
"""
        
        print(f"🔄 Generating comprehensive chapter content...")
        
        # Generate complete chapter in one pass with enhanced settings
        chapter_content = await self.ai_service.generate_completion(
            chapter_prompt,
            f"You are writing Chapter {chapter['number']} of a professional biography. Create engaging, book-quality content with natural paragraph variety.",
            max_tokens=None,  # Auto-detect max tokens for maximum output
            temperature=0.8   # Good balance of creativity and coherence
        )
        
        # Create a single comprehensive thesis for the chapter
        chapter_thesis = ""
        if chapter_content.strip():
            chapter_thesis = await self._create_comprehensive_chapter_thesis(
                chapter_content, chapter['title']
            )
        
        print(f"✅ Generated chapter {chapter['number']}: {len(chapter_content)} characters")
        
        # Return single thesis in list format for compatibility
        return chapter_content, [chapter_thesis] if chapter_thesis else []
    
    async def _create_comprehensive_chapter_thesis(self, content: str, chapter_title: str) -> str:
        """Create a comprehensive thesis for the entire chapter."""
        system_message = "Create a detailed 3-4 sentence summary of the key themes, events, and narrative elements covered in this biography chapter."
        
        # Use more content for better thesis creation
        truncated_content = content[:4000] if len(content) > 4000 else content
        
        prompt = f"""
Chapter: {chapter_title}

Content to summarize:
{truncated_content}

Create a comprehensive thesis that captures:
- Main life events and experiences covered
- Key relationships and influences discussed  
- Important themes and personal development
- Significant quotes or moments highlighted
- Overall narrative arc of this life period

Format as 3-4 flowing sentences that summarize the chapter's essence.
"""
        
        try:
            thesis = await self.ai_service.generate_completion(
                prompt,
                system_message,
                max_tokens=None,  # Auto-detect max tokens  
                temperature=0.6
            )
            return thesis.strip()
        except Exception as e:
            print(f"⚠️ Could not create chapter thesis: {e}")
            return f"Chapter {chapter_title}: Key life experiences and personal development during this period."

    def _parse_outline_fallback_large(self, response: str, recommended_chapters: int) -> Dict:
        """Fallback parser for large interview outlines."""
        chapters = []
        
        # Create chapters based on common biographical themes
        chapter_themes = [
            ("Early Life and Family Origins", ["family", "childhood", "parents"], "early childhood"),
            ("Growing Up and Education", ["school", "education", "learning"], "youth/education"),
            ("Coming of Age", ["teenage", "adolescence", "high school"], "teenage years"),
            ("Early Adulthood", ["college", "university", "early career"], "early adulthood"),
            ("Career and Professional Life", ["work", "career", "job"], "professional years"),
            ("Personal Relationships", ["marriage", "spouse", "dating", "romance"], "relationship years"),
            ("Family Life and Children", ["children", "parenting", "family"], "family years"),
            ("Major Life Challenges", ["difficulties", "challenges", "struggles"], "challenging periods"),
            ("Achievements and Milestones", ["success", "accomplishments", "achievements"], "peak years"),
            ("Hobbies and Personal Interests", ["hobbies", "interests", "passions"], "personal fulfillment"),
            ("Later Life and Reflection", ["reflection", "wisdom", "later years"], "mature years"),
            ("Legacy and Future", ["legacy", "future", "advice"], "legacy years")
        ]
        
        for i in range(min(recommended_chapters, len(chapter_themes))):
            title, themes, period = chapter_themes[i]
            chapters.append({
                "number": i + 1,
                "title": title,
                "description": f"Explores {title.lower()} and related experiences",
                "key_themes": themes,
                "estimated_sections": 3,
                "time_period": period
            })
        
        return {
            "outline": f"Comprehensive {recommended_chapters}-chapter biography covering the complete life story",
            "chapters": chapters,
            "chapter_count": len(chapters)
        }

    # Removed unused iterative helper methods - now using single comprehensive chapter generation

    async def _find_relevant_segments_for_chapter(
        self, 
        segments: List[Dict], 
        chapter: Dict
    ) -> List[Dict]:
        """Find interview segments most relevant to a specific chapter with improved scoring."""
        relevant_segments = []
        
        # Enhanced keywords based on chapter themes and time period
        chapter_keywords = chapter['key_themes'].copy()
        if 'time_period' in chapter:
            chapter_keywords.append(chapter['time_period'])
        
        # Add chapter title words as keywords
        title_words = [word.lower() for word in chapter['title'].split() 
                      if len(word) > 3 and word.lower() not in ['and', 'the', 'for', 'with']]
        chapter_keywords.extend(title_words)
        
        # Score each segment for relevance with improved algorithm
        for segment in segments:
            relevance_score = 0
            segment_content_lower = segment['content'].lower()
            
            # Check for keyword matches with weighted scoring
            for keyword in chapter_keywords:
                keyword_lower = keyword.lower()
                keyword_count = segment_content_lower.count(keyword_lower)
                
                # Weight by keyword importance and frequency
                if keyword in chapter['key_themes']:
                    relevance_score += keyword_count * 3  # Key themes are more important
                elif keyword in title_words:
                    relevance_score += keyword_count * 2  # Title words are important
                else:
                    relevance_score += keyword_count * 1
            
            # Bonus for segments with multiple relevant keywords
            matching_keywords = sum(1 for keyword in chapter_keywords 
                                  if keyword.lower() in segment_content_lower)
            if matching_keywords >= 2:
                relevance_score += matching_keywords * 2
            
            # Add segment if it has relevance or if we need more content
            if relevance_score > 0 or len(relevant_segments) < 4:
                segment['relevance_score'] = relevance_score
                relevant_segments.append(segment)
        
        # Sort by relevance and return more segments for better content diversity
        relevant_segments.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        return relevant_segments[:8]  # Increased from 5 to 8 for more content variety

    def _format_segments_for_prompt(self, segments: List[Dict]) -> str:
        """Format interview segments for inclusion in prompts with improved structure."""
        formatted_segments = []
        total_chars = 0
        max_total_chars = 25000  # Increased limit for richer context
        
        for i, segment in enumerate(segments[:4]):  # Show top 4 segments (increased from 3)
            segment_info = segment.get('session_info', segment.get('segment_info', f'Interview Segment {i+1}'))
            relevance_score = segment.get('relevance_score', 0)
            
            # Increased segment size for better context
            segment_content = segment['content'][:3000]  # Increased from 2000
            if len(segment['content']) > 3000:
                segment_content += "..."
            
            segment_text = f"--- {segment_info} (Relevance: {relevance_score}) ---\n{segment_content}"
            
            # Check if adding this segment would exceed our limit
            if total_chars + len(segment_text) > max_total_chars:
                break
                
            formatted_segments.append(segment_text)
            total_chars += len(segment_text)
        
        result = "\n\n".join(formatted_segments)
        
        # Add helpful context about remaining segments
        if len(segments) > len(formatted_segments):
            remaining_count = len(segments) - len(formatted_segments)
            total_relevance = sum(seg.get('relevance_score', 0) for seg in segments)
            shown_relevance = sum(seg.get('relevance_score', 0) for seg in segments[:len(formatted_segments)])
            
            result += f"\n\n[Context: Showing top {len(formatted_segments)} of {len(segments)} relevant segments (relevance coverage: {shown_relevance}/{total_relevance})]"
        
        return result

    async def _process_enhanced_evaluation_agent(
        self, interview_text: str, biography: str, prompt: str
    ) -> str:
        """Enhanced evaluation with focus on book-length content quality."""
        system_message = """You are the Enhanced Evaluation Agent. Evaluate this book-length biography for:
        1. Comprehensiveness and depth
        2. Narrative flow and readability
        3. Authenticity to the source material
        4. Avoiding repetition across chapters
        5. Book-quality writing standards
        """
        
        # Truncate inputs to prevent context overflow
        truncated_interview = interview_text[:3000]
        truncated_biography = biography[:8000]
        
        full_prompt = f"""
{prompt}

=========== ORIGINAL TRANSCRIPT (EXCERPT) ===========
{truncated_interview}

=========== BOOK-LENGTH BIOGRAPHY TO EVALUATE (EXCERPT) ===========
{truncated_biography}

Please provide detailed evaluation focusing on book-quality standards and comprehensive coverage.
Note: This is an excerpt of the full biography for evaluation purposes.
"""
        
        return await self.ai_service.generate_completion(
            full_prompt, system_message  # Auto-detect max tokens
        )

    def _combine_chapters(self, chapters_content: Dict[str, str]) -> str:
        """Combine all chapters into a single biography text."""
        combined = ""
        
        # Sort chapters by number
        sorted_chapters = sorted(
            chapters_content.items(),
            key=lambda x: x[1]["number"]
        )
        
        for chapter_key, chapter_data in sorted_chapters:
            # Fixed: Don't duplicate "Chapter X:" in the title since it's already in the title
            chapter_title = chapter_data['title']
            chapter_number = chapter_data['number']
            
            # Only add Chapter X: if it's not already in the title
            if not chapter_title.startswith(f"Chapter {chapter_number}"):
                combined += f"\n\nChapter {chapter_number}: {chapter_title}\n\n"
            else:
                combined += f"\n\n{chapter_title}\n\n"
                
            combined += chapter_data["content"]
            combined += "\n" + "="*80 + "\n"
        
        return combined

    async def _update_job(self, job: BiographyJob, status: JobStatus, progress: float):
        """Update job status and progress."""
        job.status = status
        job.progress_percentage = progress
        job.updated_at = datetime.utcnow()
        self.db.commit()
        
        # Send WebSocket update
        try:
            await manager.broadcast_job_update(job.id)
        except Exception as e:
            print(f"Failed to broadcast WebSocket update for job {job.id}: {e}")
            pass

    def _get_agent_prompts(self) -> dict:
        """Get current active prompts for all agents."""
        print(f"\n🔍 [ITERATIVE DEBUG] Loading agent prompts for biography generation...")
        
        prompts = {}
        
        agent_names = ["outline", "writer", "evaluation", "rewrite"]
        for agent_name in agent_names:
            print(f"🔎 [ITERATIVE DEBUG] Querying prompt for agent: {agent_name}")
            
            prompt_record = (
                self.db.query(AgentPrompt)
                .filter(AgentPrompt.agent_name == agent_name, AgentPrompt.is_active == True)
                .first()
            )
            
            if prompt_record:
                prompts[agent_name] = prompt_record.prompt_content
                print(f"✅ [ITERATIVE DEBUG] Found DB prompt for '{agent_name}': v{prompt_record.version}, {len(prompt_record.prompt_content)} chars")
                print(f"📄 [ITERATIVE DEBUG] Prompt preview: {prompt_record.prompt_content[:200]}...")
            else:
                # Fallback to default prompts
                fallback_prompt = self._get_default_prompt(agent_name)
                prompts[agent_name] = fallback_prompt
                print(f"⚠️ [ITERATIVE DEBUG] No DB prompt for '{agent_name}', using fallback ({len(fallback_prompt)} chars)")
                print(f"📄 [ITERATIVE DEBUG] Fallback preview: {fallback_prompt[:200]}...")
        
        print(f"📊 [ITERATIVE DEBUG] Loaded {len(prompts)} agent prompts successfully")
        
        return prompts

    def _get_default_prompt(self, agent_name: str) -> str:
        """Get default prompt for agent if none exists in database."""
        default_prompts = {
            "outline": """You are the Enhanced Biography Outliner for creating comprehensive book-length biographies.
            Create a detailed chapter structure that will support extensive content generation.
            Focus on creating 6-12 substantial chapters that can each support multiple iterations of content.""",
            "writer": """You are the Enhanced Biography Writer creating detailed, book-length content.
            Your task is to write compelling biographical content that reads like a professionally published biography.
            Focus on storytelling, character development, and rich detail while maintaining authenticity.""",
            "evaluation": """You are the Enhanced Evaluation Agent reviewing book-length biographical content.
            Evaluate for depth, coherence, authenticity, and professional writing quality.
            Consider whether this meets the standards of a published biography.""",
            "rewrite": """You are the Enhanced Rewrite Agent polishing book-length biographical content.
            Improve flow, eliminate repetition, enhance narrative structure, and ensure publication quality."""
        }
        return default_prompts.get(agent_name, "Default prompt for " + agent_name)

    async def process_iterative_biography_job_sequential(self, job_id: str, chapter_number: int = None) -> bool:
        """Process a biography generation job with sequential chapter generation."""
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        start_time = time.time()
        
        try:
            if chapter_number is None:
                # Initialize the job for sequential processing
                await self._initialize_sequential_job(job)
                chapter_number = 1
            
            # Extract text from uploaded PDF
            interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
            if not interview_text.strip():
                raise Exception("Could not extract text from uploaded PDF")
            
            # Get agent prompts
            prompts = self._get_agent_prompts()
            
            # Get outline data (already generated or create new)
            if not job.outline_content:
                outline_data = await self._process_enhanced_outline_agent(
                    interview_text, prompts["outline"]
                )
                job.outline_content = outline_data["outline"]
                job.total_chapters = outline_data["chapter_count"]
                self.db.commit()
            else:
                # Reconstruct outline data from stored data
                outline_data = self._reconstruct_outline_data(job)
            
            # Process specific chapter
            chapter_info = outline_data["chapters"][chapter_number - 1]
            existing_theses = job.chapter_theses or {}
            
            await self._update_job(job, JobStatus.ITERATIVE_WRITING, 
                                 25.0 + (chapter_number - 1) / job.total_chapters * 55.0)
            
            # Generate single chapter iteratively
            chapter_content, chapter_theses = await self._generate_chapter_iteratively(
                interview_text, chapter_info, prompts["writer"], existing_theses
            )
            
            # Store chapter data
            chapters_content = job.chapters_content or {}
            chapter_key = f"chapter_{chapter_number}"
            chapters_content[chapter_key] = {
                "title": chapter_info["title"],
                "content": chapter_content,
                "number": chapter_number
            }
            
            existing_theses[chapter_key] = chapter_theses
            
            job.chapters_content = chapters_content
            job.chapter_theses = existing_theses
            job.completed_chapters = chapter_number
            
            # Update progress
            progress = 25.0 + (chapter_number / job.total_chapters) * 55.0
            await self._update_job(job, JobStatus.ITERATIVE_WRITING, progress)
            
            # Check if all chapters completed
            if chapter_number >= job.total_chapters:
                # Complete the biography
                await self._complete_sequential_biography(job, outline_data, prompts)
            
            job.processing_time_seconds = time.time() - start_time
            self.db.commit()
            return True
                
        except Exception as e:
            job.error_message = str(e)
            job.processing_time_seconds = time.time() - start_time
            await self._update_job(job, JobStatus.FAILED, job.progress_percentage)
            return False

    async def _initialize_sequential_job(self, job: BiographyJob):
        """Initialize job for sequential chapter processing."""
        job.chapters_content = {}
        job.chapter_theses = {}
        job.completed_chapters = 0
        await self._update_job(job, JobStatus.PROCESSING, 5.0)

    def _reconstruct_outline_data(self, job: BiographyJob) -> Dict:
        """Reconstruct outline data from job data."""
        # Create chapters from stored data or fallback
        chapters = []
        for i in range(1, job.total_chapters + 1):
            chapters.append({
                "number": i,
                "title": f"Chapter {i}",
                "description": f"Chapter {i} content",
                "key_themes": ["life_experience"],
                "estimated_sections": 3
            })
        
        return {
            "outline": job.outline_content,
            "chapters": chapters,
            "chapter_count": job.total_chapters
        }

    async def _complete_sequential_biography(self, job: BiographyJob, outline_data: Dict, prompts: Dict):
        """Complete sequential biography processing."""
        # Combine all chapters
        full_biography = self._combine_chapters(job.chapters_content)
        job.biography_content = full_biography
        
        await self._update_job(job, JobStatus.WRITING_COMPLETE, 80.0)
        
        # Generate final biography (skip evaluation for now to save tokens)
        job.final_biography_content = full_biography
        await self._update_job(job, JobStatus.REWRITE_COMPLETE, 95.0)
        
        # Generate PDF
        await self._update_job(job, JobStatus.GENERATING_PDF, 97.0)
        output_filename = f"biography_{job.id}_{int(time.time())}.pdf"
        output_path = os.path.join("./output", output_filename)
        
        success = PDFService.generate_enhanced_biography_pdf(
            full_biography,
            job.user_name,
            outline_data["chapters"],
            output_path
        )
        
        if success:
            job.output_pdf_path = output_path
            job.completed_at = datetime.utcnow()
            await self._update_job(job, JobStatus.COMPLETED, 100.0)
        else:
            raise Exception("Failed to generate PDF")

    def _final_repetition_check_and_cleanup(self, biography_content: str) -> str:
        """Final check to remove all repetitive content before PDF generation."""
        if not biography_content or not biography_content.strip():
            return biography_content
        
        print(f"🔍 [FINAL CLEANUP] Analyzing {len(biography_content):,} characters for repetition...")
        
        # Split content into paragraphs
        paragraphs = [p.strip() for p in biography_content.split('\n\n') if p.strip()]
        
        # Track unique paragraph hashes to detect exact duplicates
        unique_paragraphs = []
        seen_hashes = set()
        
        # Track similar paragraphs using similarity threshold
        removed_count = 0
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) < 50:  # Keep very short paragraphs (likely headers/transitions)
                unique_paragraphs.append(paragraph)
                continue
            
            # Create normalized hash for exact duplicate detection
            normalized = re.sub(r'[^\w\s]', '', paragraph.lower())
            normalized = re.sub(r'\s+', ' ', normalized).strip()
            paragraph_hash = hash(normalized)
            
            # Check for exact duplicates
            if paragraph_hash in seen_hashes:
                print(f"🗑️ [FINAL CLEANUP] Removing exact duplicate paragraph {i+1}")
                removed_count += 1
                continue
            
            # Check for high similarity to existing paragraphs
            is_similar = False
            for existing_para in unique_paragraphs[-10:]:  # Check last 10 paragraphs only
                if len(existing_para) < 50:
                    continue
                    
                similarity = self._calculate_content_similarity(paragraph, existing_para)
                if similarity > 0.8:  # Very high similarity threshold for final cleanup
                    print(f"🗑️ [FINAL CLEANUP] Removing highly similar paragraph {i+1} (similarity: {similarity:.2f})")
                    is_similar = True
                    removed_count += 1
                    break
            
            if not is_similar:
                unique_paragraphs.append(paragraph)
                seen_hashes.add(paragraph_hash)
        
        # Reconstruct cleaned content
        cleaned_content = '\n\n'.join(unique_paragraphs)
        
        # Additional cleanup: Remove chapter headers that are numbered > 6
        lines = cleaned_content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line_stripped = line.strip()
            # Skip chapter headers with numbers > 6
            if re.match(r'^Chapter\s+([7-9]|\d{2,})\s*:', line_stripped, re.IGNORECASE):
                print(f"🗑️ [FINAL CLEANUP] Removing chapter header: {line_stripped}")
                continue
            # Skip standalone chapter numbers > 6
            elif re.match(r'^([7-9]|\d{2,})\s*$', line_stripped):
                print(f"🗑️ [FINAL CLEANUP] Removing chapter number: {line_stripped}")
                continue
            else:
                cleaned_lines.append(line)
        
        final_cleaned_content = '\n'.join(cleaned_lines)
        
        print(f"📊 [FINAL CLEANUP] Cleanup complete:")
        print(f"   Original paragraphs: {len(paragraphs)}")
        print(f"   Removed paragraphs: {removed_count}")
        print(f"   Final paragraphs: {len(unique_paragraphs)}")
        print(f"   Character reduction: {len(biography_content) - len(final_cleaned_content):,} chars")
        
        return final_cleaned_content

    def _calculate_content_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text pieces to detect repetition."""
        if not text1 or not text2:
            return 0.0
        
        # Simple word-based similarity check
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0 