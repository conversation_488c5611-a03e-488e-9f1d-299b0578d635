#!/usr/bin/env python3
"""
Enhanced Iterative Biography Service with context-aware generation.
Creates book-like biographies by building upon previously generated content.
"""

import asyncio
import json
import re
import time
import math
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.biography_job import <PERSON><PERSON><PERSON>, JobStatus
from app.services.pdf_service import PDFService
from app.services.ai_service import AIService
from app.services.websocket_manager import manager
from app.services.quality_assurance_service import QualityAssuranceService
from app.services.word_tracking_service import GlobalWordTracker
from app.models.agent_prompt import AgentPrompt


class EnhancedIterativeService:
    """Service for context-aware iterative biography generation."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService(db)
        self.quality_assurance_service = QualityAssuranceService(db)
        
        # Enhanced iterative configuration optimized for 800+ page biographies
        self.segment_size = 2500  # DECREASED: Characters per segment for more detailed processing
        self.segment_overlap = 1500  # Increased overlap for better continuity
        self.context_preserve_ratio = 0.35  # Reduced to allow more new content
        self.max_input_tokens = 80000  # Maximum input tokens before aggressive trimming
        self.safe_input_tokens = 50000  # Safe input token limit
        self.min_context_chars = 8000  # Minimum context to preserve
        
        # Target content volume for DOUBLE the previous output (800+ pages)
        self.target_words_per_page = 300  # Standard book page word count
        self.target_total_pages = 800  # DOUBLED from 400 to 800
        self.target_total_words = self.target_words_per_page * self.target_total_pages  # 240,000 words
        self.target_chars_per_segment = 35000  # INCREASED: ~5,500+ words per segment for richer content
        
        # Iterative enhancement configuration
        self.iterations_per_segment = 3  # 3 iterative improvements per segment
        self.use_evaluation_rewrite = True  # Use evaluation + rewrite prompts from DB
        self.anti_repetition_enabled = True  # Enable anti-repetition checking
        self.repetition_similarity_threshold = 0.7  # Similarity threshold for repetition detection
        
        # Prompt caching to avoid repeated DB calls
        self._cached_prompts = None
        
        self.name_tags = {
            'USER_NAME': '{USER_NAME}',
            'FIRST_NAME': '{FIRST_NAME}',
            'HE_SHE': '{HE_SHE}',
            'HIS_HER': '{HIS_HER}',
            'HIM_HER': '{HIM_HER}'
        }
        
        print(f"🚀 Enhanced Iterative Service initialized for 800-PAGE BIOGRAPHY")
        print(f"📊 Segment size: {self.segment_size:,} chars with {self.segment_overlap} overlap")
        print(f"🎯 Target: {self.target_total_pages} pages ({self.target_total_words:,} words)")
        print(f"📝 Target per segment: {self.target_chars_per_segment:,} chars (~5,500+ words)")
        print(f"🔄 Iterations per segment: {self.iterations_per_segment}")
        print(f"🔍 Anti-repetition: {'ENABLED' if self.anti_repetition_enabled else 'DISABLED'}")
        print(f"📋 Evaluation+Rewrite: {'ENABLED' if self.use_evaluation_rewrite else 'DISABLED'}")
        print(f"⚡ Token limits: Safe={self.safe_input_tokens:,}, Max={self.max_input_tokens:,}")

    async def _get_agent_prompts(self) -> dict:
        """Load agent prompts from database with fallback to defaults."""
        
        # Return cached prompts if available
        if hasattr(self, '_cached_prompts') and self._cached_prompts:
            return self._cached_prompts
        
        print(f"🔍 [ENHANCED ITERATIVE] Loading agent prompts from database...")
        
        try:
            from app.models.agent_prompt import AgentPrompt
        
            prompts = {}
        
            # Load all required prompts
            for agent_name in ["outline", "writer", "evaluation", "rewrite"]:
                print(f"🔎 [ENHANCED ITERATIVE] Querying prompt for agent: {agent_name}")
            
                agent_prompt = self.db.query(AgentPrompt).filter(
                    AgentPrompt.agent_name == agent_name,
                    AgentPrompt.is_active == True
                ).order_by(AgentPrompt.version.desc()).first()
                
                if agent_prompt:
                    prompts[agent_name] = agent_prompt.prompt_content
                    print(f"✅ [ENHANCED ITERATIVE] Found DB prompt for '{agent_name}': v{agent_prompt.version}, {len(agent_prompt.prompt_content)} chars")
                else:
                    default_prompt = self._get_default_prompt(agent_name)
                    prompts[agent_name] = default_prompt
                    print(f"⚠️ [ENHANCED ITERATIVE] Using default prompt for '{agent_name}': {len(default_prompt)} chars")
            
            # Cache the prompts
            self._cached_prompts = prompts
            print(f"📊 [ENHANCED ITERATIVE] Loaded and cached {len(prompts)} agent prompts successfully")
            
            return prompts
            
        except Exception as e:
            print(f"❌ [ENHANCED ITERATIVE] Error loading prompts: {e}")
            # Fallback to default prompts
            default_prompts = {
                "outline": self._get_default_prompt("outline"),
                "writer": self._get_default_prompt("writer"), 
                "evaluation": self._get_default_prompt("evaluation"),
                "rewrite": self._get_default_prompt("rewrite")
            }
            self._cached_prompts = default_prompts
            return default_prompts

    def _get_default_prompt(self, agent_name: str) -> str:
        """Get default prompt for agent if none exists in database."""
        default_prompts = {
            "writer": """You are a master biographer writing a comprehensive life story. Write detailed, engaging biographical content that reads like a professionally published biography.

Style guidelines:
- Write in an engaging, narrative style similar to Walter Isaacson
- Include specific details and quotes from the interview
- Maintain chronological flow with smooth transitions
- Focus on the person's unique experiences and personality
- Avoid repetition and filler content
- Use varied paragraph lengths for readability
- Create rich, detailed biography that captures the person's life story"""
        }
        return default_prompts.get(agent_name, "Default prompt for biography writing.")

    async def process_enhanced_iterative_job(self, job_id: str) -> bool:
        """Process biography with enhanced context-aware iterative generation."""
        print(f"\n" + "="*80)
        print(f"🚀 ENHANCED ITERATIVE SERVICE: Starting processing for job {job_id}")
        print(f"🔍 ENHANCED ITERATIVE SERVICE: This is the context-aware service with 75% context building")
        print(f"🎯 ENHANCED ITERATIVE SERVICE: You should see prompts from database and large context usage")
        print(f"�� ENHANCED ITERATIVE SERVICE: NOT Enhanced Progressive! This is the NEW service!")
        print(f"="*80 + "\n")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")

        start_time = time.time()
        
        try:
            await self._update_job(job, JobStatus.PROCESSING, 5.0)
            
            # Load and analyze interview
            interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
            if not interview_text.strip():
                raise Exception("Could not extract text from uploaded PDF")
            
            person_name = job.user_name
            print(f"📝 ENHANCED ITERATIVE: Processing enhanced iterative biography for: {person_name}")

            # Initialize word tracking for this job
            word_tracker = GlobalWordTracker.get_tracker(job_id)
            print(f"📊 ENHANCED ITERATIVE: Word tracking initialized for job {job_id[:8]}")

            # Analyze and segment interview
            interview_analysis = self._analyze_and_segment_interview(interview_text)
            await self._update_job(job, JobStatus.PROCESSING, 15.0)
            
            print(f"🔢 ENHANCED ITERATIVE: Interview analysis complete - {len(interview_analysis['segments'])} segments")
            print(f"🔢 ENHANCED ITERATIVE: Context limit: {interview_analysis.get('context_chars_limit', 'unknown')} chars")
            
            # Generate comprehensive outline
            outline_data = await self._generate_comprehensive_outline(
                interview_text, person_name, interview_analysis
            )
            job.outline_content = json.dumps(outline_data)
            await self._update_job(job, JobStatus.OUTLINE_COMPLETE, 25.0)
            
            # Enhanced iterative generation with context building
            print("🚀 ENHANCED ITERATIVE: Starting biography generation...")
            biography_content = await self._generate_context_aware_biography(
                job, interview_analysis, outline_data, person_name
            )
            print(f"🎯 ENHANCED ITERATIVE: Biography generation returned {len(biography_content):,} chars")
            
            # Apply final tag replacement to the complete biography
            original_tags = len([m for m in re.finditer(r'\{[A-Z_]+\}', biography_content)])
            final_biography_content = self._replace_name_tags(biography_content, person_name)
            remaining_tags = len([m for m in re.finditer(r'\{[A-Z_]+\}', final_biography_content)])
            
            print(f"🏷️  Final tag replacement: {original_tags} → {remaining_tags} tags in complete biography")
            if remaining_tags > 0:
                print(f"⚠️  Warning: {remaining_tags} unresolved tags remain in final biography")
            
            # FINAL REPETITION CHECK AND CLEANUP
            print(f"🔍 [FINAL CLEANUP] Starting final repetition check...")
            cleaned_biography_content = self._final_repetition_check_and_cleanup(final_biography_content)
            
            if len(cleaned_biography_content) != len(final_biography_content):
                print(f"🧹 [FINAL CLEANUP] Removed repetitive content:")
                print(f"   Original: {len(final_biography_content):,} chars")
                print(f"   Cleaned: {len(cleaned_biography_content):,} chars")
                print(f"   Removed: {len(final_biography_content) - len(cleaned_biography_content):,} chars")
            else:
                print(f"✅ [FINAL CLEANUP] No repetitive content found - content is clean")
            
            # QUALITY ASSURANCE STEP
            print(f"🔍 [QUALITY ASSURANCE] Starting final quality assurance...")
            qa_result = await self.quality_assurance_service.process_biography_quality_assurance(
                cleaned_biography_content, person_name
            )

            final_qa_content = qa_result['improved_biography']
            improvements = qa_result['improvements_made']

            print(f"✅ [QUALITY ASSURANCE] Quality assurance complete:")
            print(f"   Quality score improvement: {improvements['quality_score_improvement']:+.1f}")
            print(f"   Overused words fixed: {improvements['overused_words_fixed']}")
            print(f"   Repetitive phrases removed: {improvements['repetitive_phrases_removed']}")
            print(f"   Final length: {len(final_qa_content):,} chars")

            job.biography_content = final_qa_content
            job.final_biography_content = final_qa_content
            await self._update_job(job, JobStatus.WRITING_COMPLETE, 85.0)

            # Clean up word tracker
            GlobalWordTracker.remove_tracker(job_id)

            # Generate enhanced PDF
            await self._update_job(job, JobStatus.GENERATING_PDF, 95.0)
            output_filename = f"enhanced_biography_{job.id}_{int(time.time())}.pdf"
            output_path = f"./output/{output_filename}"
            
            print(f"📄 ENHANCED ITERATIVE: Passing {len(final_biography_content):,} chars to PDF generator")
            print(f"📖 Biography preview: {final_biography_content[:200]}...")
            
            success = PDFService.generate_enhanced_biography_pdf(
                final_biography_content,
                person_name,
                outline_data["chapters"],
                output_path
            )
            
            if success:
                job.output_pdf_path = output_path
                job.completed_at = datetime.utcnow()
                job.processing_time_seconds = time.time() - start_time
                await self._update_job(job, JobStatus.COMPLETED, 100.0)
                return True
            else:
                raise Exception("Failed to generate PDF")
                
        except Exception as e:
            job.error_message = str(e)
            job.processing_time_seconds = time.time() - start_time
            await self._update_job(job, JobStatus.FAILED, job.progress_percentage)
            return False

    def _analyze_and_segment_interview(self, interview_text: str) -> Dict:
        """Analyze interview and create segments with context-aware limits."""
        
        # Basic analysis
        total_chars = len(interview_text)
        word_count = len(interview_text.split())
        
        print(f"🔍 ENHANCED ITERATIVE ANALYSIS:")
        print(f"   📊 Interview: {total_chars:,} chars, {word_count:,} words")
        
        # Calculate token-aware context limits using current model
        model_max_input = self.ai_service.get_model_context_limit()
        model_max_output = self.ai_service.get_model_output_limit()
        
        print(f"   🧠 Model limits: {model_max_input:,} input, {model_max_output:,} output")
        
        # Reserve tokens for system instructions and safety margin
        system_instruction_tokens = 1500  # Estimated tokens for system instructions
        safety_margin = 1000  # Safety margin
        
        available_context_tokens = model_max_input - system_instruction_tokens - safety_margin
        context_chars_limit = int(available_context_tokens * self.context_preserve_ratio * 3.5)  # ~3.5 chars per token
        
        print(f"   📏 Context calculation:")
        print(f"       Available tokens: {available_context_tokens:,}")
        print(f"       Context ratio: {self.context_preserve_ratio:.0%}")
        print(f"       Context limit: {context_chars_limit:,} chars")
        
        # Create segments of interview
        segments = []
        current_pos = 0
        segment_num = 1
        
        while current_pos < total_chars:
            # Calculate segment end position
            segment_end = min(current_pos + self.segment_size, total_chars)
            
            # Try to end at sentence boundary if possible
            if segment_end < total_chars:
                for punct in ['. ', '! ', '? ', '\n\n']:
                    punct_pos = interview_text.rfind(punct, current_pos, segment_end)
                    if punct_pos > current_pos + self.segment_size * 0.7:
                        segment_end = punct_pos + len(punct)
                        break
            
            # Extract segment content
            segment_content = interview_text[current_pos:segment_end].strip()
            
            if segment_content:
                segments.append({
                    "number": segment_num,
                    "content": segment_content,
                    "start_pos": current_pos,
                    "end_pos": segment_end,
                    "char_count": len(segment_content)
                })
                segment_num += 1
            
            # Move to next segment with overlap
            current_pos = max(segment_end - self.segment_overlap, segment_end)
        
        print(f"   📄 Created {len(segments)} interview segments")
        
        return {
            "total_chars": total_chars,
            "word_count": word_count,
            "segments": segments,
            "context_chars_limit": context_chars_limit,
            "model_max_input": model_max_input,
            "model_max_output": model_max_output
        }

    async def _generate_comprehensive_outline(
        self, interview_text: str, person_name: str, interview_analysis: Dict
    ) -> Dict:
        """Generate a comprehensive outline based on automatic interview analysis with personalized chapters."""
        
        print(f"📋 [OUTLINE GENERATION] Starting automatic analysis-based outline for {person_name}")
        
        try:
            # Step 1: Analyze interview content to extract themes and structure
            content_analysis = await self._analyze_interview_content(interview_text, person_name)
            
            # Step 2: Use the analysis to create a structured outline
            estimated_pages = max(800, interview_analysis["word_count"] // 125)  # Minimum 800 pages (DOUBLED)
            
            # Extract suggested chapters from analysis
            suggested_chapters = content_analysis.get('suggested_chapters', [])
            major_themes = content_analysis.get('major_themes', [])
            life_periods = content_analysis.get('life_periods', [])
            
            print(f"🔍 [OUTLINE GENERATION] Analysis results:")
            print(f"    📚 AI suggested chapters: {len(suggested_chapters)}")
            print(f"    🎯 Major themes identified: {len(major_themes)}")
            print(f"    📅 Life periods found: {len(life_periods)}")
            
            # Step 3: If we have good AI suggestions, use them as base
            final_chapters = []
            chapter_number = 1
            
            if suggested_chapters and len(suggested_chapters) >= 3:  # Lowered threshold
                print(f"✅ [OUTLINE GENERATION] Using AI-suggested chapters as primary structure")
                
                # Use only the best AI-suggested chapters, limit to 6 maximum
                for suggestion in suggested_chapters[:6]:  # Maximum 6 AI suggestions
                    final_chapters.append({
                        "number": chapter_number,
                        "title": suggestion.get('chapter_title', f'Chapter {chapter_number}'),
                        "description": suggestion.get('chapter_focus', 'Chapter content'),
                        "themes": suggestion.get('themes_covered', ['general']),
                        "time_period": self._extract_timeframe_from_themes(suggestion.get('themes_covered', [])),
                        "key_focus": suggestion.get('chapter_focus', 'Chapter content'),
                        "content_sources": suggestion.get('content_sources', 'Interview content')
                    })
                    chapter_number += 1
                
                # Only add additional chapters if we have less than 4 core chapters
                target_chapters = max(4, min(6, len(suggested_chapters)))  # 4-6 chapters total
                
                if len(final_chapters) < target_chapters:
                    # Generate only essential additional chapters
                    additional_chapters = self._generate_essential_chapters(
                        content_analysis, final_chapters, target_chapters - len(final_chapters), person_name
                    )
                    final_chapters.extend(additional_chapters)
                
            else:
                print(f"⚠️ [OUTLINE GENERATION] AI suggestions insufficient, creating essential thematic structure")
                # Create minimal essential chapters
                final_chapters = self._create_essential_chapters(content_analysis, person_name)
            
            # Step 4: Ensure we have 4-6 quality chapters, never more
            if len(final_chapters) < 4:
                print(f"📈 [OUTLINE GENERATION] Expanding to minimum 4 chapters")
                additional_needed = 4 - len(final_chapters)
                extra_chapters = self._generate_essential_chapters(
                    content_analysis, final_chapters, additional_needed, person_name
                )
                final_chapters.extend(extra_chapters)
            elif len(final_chapters) > 6:
                print(f"📉 [OUTLINE GENERATION] Too many chapters ({len(final_chapters)}), reducing to 6 best ones")
                # Keep only the most important chapters
                final_chapters = self._select_best_chapters(final_chapters, 6)
            
            # Step 5: Create final outline structure
            outline = {
                "title": f"The Life Story of {person_name}",
                "estimated_pages": estimated_pages,
                "chapters": final_chapters,
                "chapter_count": len(final_chapters),
                "writing_approach": "Analysis-based biographical narrative with personalized chapters",
                "analysis_summary": content_analysis.get('analysis_summary', ''),
                "major_themes": [theme.get('theme', '') for theme in major_themes],
                "key_periods": [period.get('period', '') for period in life_periods]
            }
            
            print(f"✅ [OUTLINE GENERATION] Generated comprehensive outline:")
            print(f"    📚 Total chapters: {len(final_chapters)}")
            print(f"    📄 Target pages: {estimated_pages}")
            print(f"    🎯 Based on: {len(major_themes)} themes, {len(life_periods)} periods")
            
            # Show first few chapter titles
            print(f"    📝 Sample chapters:")
            for i, chapter in enumerate(final_chapters[:5]):
                title = chapter.get('title', 'Untitled')
                print(f"        {i+1}. {title}")
            
            return outline
            
        except Exception as e:
            print(f"❌ [OUTLINE GENERATION] Failed to generate analysis-based outline: {e}")
            print(f"🔄 [OUTLINE GENERATION] Using fallback outline generation")
            
            # Fallback to simple analysis-based outline
            estimated_pages = max(800, interview_analysis["word_count"] // 125)
            fallback_analysis = self._create_fallback_analysis(person_name, interview_text)
            return self._create_fallback_outline_from_analysis(person_name, fallback_analysis, estimated_pages)
        
    def _extract_timeframe_from_themes(self, themes: list) -> str:
        """Extract likely timeframe from chapter themes."""
        theme_text = ' '.join(themes).lower()
        
        if any(word in theme_text for word in ['childhood', 'early', 'birth', 'young']):
            return "Early life"
        elif any(word in theme_text for word in ['school', 'education', 'college', 'university']):
            return "Education years"
        elif any(word in theme_text for word in ['career', 'work', 'job', 'professional']):
            return "Career period"
        elif any(word in theme_text for word in ['family', 'marriage', 'children', 'parent']):
            return "Family building"
        elif any(word in theme_text for word in ['later', 'mature', 'wisdom', 'reflection']):
            return "Later life"
        else:
            return "Life journey"
    

    

    
    def _generate_core_additional_chapters(self, content_analysis: Dict, existing_chapters: list, 
                                         count_needed: int, person_name: str) -> list:
        """Generate only high-quality additional chapters without duplicates."""
        
        additional_chapters = []
        start_number = len(existing_chapters) + 1
        
        major_themes = content_analysis.get('major_themes', [])
        life_periods = content_analysis.get('life_periods', [])
        relationships = content_analysis.get('key_relationships', [])
        places = content_analysis.get('significant_places', [])
        events = content_analysis.get('major_events', [])
        
        # Get existing chapter themes to avoid duplicates
        existing_themes = set()
        for chapter in existing_chapters:
            themes = chapter.get('themes', [])
            for theme in themes:
                existing_themes.add(theme.lower())
            # Also check title keywords
            title = chapter.get('title', '').lower()
            existing_themes.update(title.split())
        
        chapter_ideas = []
        
        # Only add themes not already covered
        for theme in major_themes:
            theme_name = theme.get('theme', '').lower()
            if theme_name not in existing_themes and theme.get('importance') == 'high':
                chapter_ideas.append({
                    "title": f"{theme.get('theme', 'Life Theme')} in {person_name}'s Story",
                    "description": f"Comprehensive exploration of {theme.get('description', '')}",
                    "themes": [theme.get('theme', 'general')],
                    "focus": theme.get('description', 'Theme exploration'),
                    "priority": 3
                })
        
        # Add significant relationships (only top ones not already covered)
        covered_relationships = {rel.lower() for chapter in existing_chapters 
                               for rel in chapter.get('title', '').split() 
                               if rel.lower() in ['family', 'parents', 'mother', 'father', 'siblings']}
        
        for rel in relationships[:3]:  # Top 3 relationships
            rel_name = rel.get('person', '').lower()
            if rel_name not in covered_relationships and rel_name not in existing_themes:
                chapter_ideas.append({
                    "title": f"{person_name} and {rel.get('person', 'Key Person')}: A Defining Relationship",
                    "description": f"The relationship with {rel.get('person', '')} and its significance",
                    "themes": ['relationships', 'personal_connections'],
                    "focus": rel.get('significance', 'Relationship impact'),
                    "priority": 2
                })
        
        # Add major life events (only most significant ones)
        for event in events[:2]:  # Top 2 events
            event_key = event.get('event', '').lower()
            if not any(word in existing_themes for word in event_key.split()[:3]):
                chapter_ideas.append({
                    "title": f"Turning Point: {event.get('event', 'Important Event')}",
                    "description": f"The impact and significance of {event.get('event', '')}",
                    "themes": ['major_events', 'turning_points'],
                    "focus": event.get('impact', 'Event impact'),
                    "priority": 2
                })
        
        # Sort by priority and select the best ideas
        chapter_ideas.sort(key=lambda x: x.get('priority', 1), reverse=True)
        selected_ideas = chapter_ideas[:count_needed]
        
        for i, idea in enumerate(selected_ideas):
            additional_chapters.append({
                "number": start_number + i,
                "title": idea['title'],
                "description": idea['description'],
                "themes": idea['themes'],
                "time_period": self._extract_timeframe_from_themes(idea['themes']),
                "key_focus": idea['focus'],
                "content_sources": "Interview analysis and content extraction"
            })
        
        print(f"📈 [OUTLINE GENERATION] Generated {len(additional_chapters)} high-quality additional chapters")
        return additional_chapters
    

    
    def _select_best_chapters(self, chapters: list, max_count: int) -> list:
        """Select the best chapters based on theme importance and uniqueness."""
        
        # Score chapters based on various criteria
        scored_chapters = []
        
        for chapter in chapters:
            score = 0
            title = chapter.get('title', '').lower()
            themes = chapter.get('themes', [])
            
            # Higher score for life periods
            if any(period_word in title for period_word in ['childhood', 'early', 'youth', 'career', 'family']):
                score += 3
            
            # Higher score for unique themes
            if not any(dup_word in title for dup_word in ['deep dive', 'the role of', 'important connection']):
                score += 2
            
            # Higher score for broader themes
            if len(themes) > 1:
                score += 1
            
            # Lower score for repetitive patterns
            if title.count(':') > 1 or 'pivotal moment' in title:
                score -= 1
            
            scored_chapters.append((score, chapter))
        
        # Sort by score and take the best ones
        scored_chapters.sort(key=lambda x: x[0], reverse=True)
        selected_chapters = [chapter for score, chapter in scored_chapters[:max_count]]
        
        # Renumber chapters
        for i, chapter in enumerate(selected_chapters):
            chapter['number'] = i + 1
        
        print(f"📊 [OUTLINE GENERATION] Selected {len(selected_chapters)} best chapters from {len(chapters)}")
        return selected_chapters

    def _generate_essential_chapters(self, content_analysis: Dict, existing_chapters: list, 
                                   count_needed: int, person_name: str) -> list:
        """Generate only the most essential chapters without any duplication."""
        
        additional_chapters = []
        start_number = len(existing_chapters) + 1
        
        major_themes = content_analysis.get('major_themes', [])
        life_periods = content_analysis.get('life_periods', [])
        
        # Get existing chapter themes to avoid duplicates
        existing_themes = set()
        existing_keywords = set()
        for chapter in existing_chapters:
            themes = chapter.get('themes', [])
            for theme in themes:
                existing_themes.add(theme.lower().strip())
            # Extract keywords from title
            title_words = chapter.get('title', '').lower().split()
            existing_keywords.update(word.strip('.:,') for word in title_words)
        
        essential_ideas = []
        
        # Only add the most important life periods not already covered
        for period in life_periods[:2]:  # Max 2 life periods
            period_name = period.get('period', '').lower().strip()
            if period_name and not any(keyword in existing_keywords for keyword in period_name.split()[:2]):
                essential_ideas.append({
                    "title": f"{period.get('period', 'Life Period')}: {person_name}'s Story",
                    "description": period.get('description', 'Life period exploration'),
                    "themes": period.get('key_themes', ['life_period']),
                    "focus": period.get('description', 'Period focus'),
                    "priority": 5,
                    "type": "period"
                })
        
        # Only add the highest priority themes not already covered
        high_priority_themes = [theme for theme in major_themes 
                               if theme.get('importance') == 'high'][:2]  # Max 2 themes
        
        for theme in high_priority_themes:
            theme_name = theme.get('theme', '').lower().strip()
            if theme_name and not any(keyword in existing_keywords for keyword in theme_name.split()[:2]):
                essential_ideas.append({
                    "title": f"{theme.get('theme', 'Core Theme')} in {person_name}'s Life",
                    "description": theme.get('description', 'Theme exploration'),
                    "themes": [theme.get('theme', 'general')],
                    "focus": theme.get('description', 'Theme focus'),
                    "priority": 4,
                    "type": "theme"
                })
        
        # Sort by priority and select only what's needed
        essential_ideas.sort(key=lambda x: x.get('priority', 1), reverse=True)
        selected_ideas = essential_ideas[:count_needed]
        
        for i, idea in enumerate(selected_ideas):
            additional_chapters.append({
                "number": start_number + i,
                "title": idea['title'],
                "description": idea['description'],
                "themes": idea['themes'],
                "time_period": self._extract_timeframe_from_themes(idea['themes']),
                "key_focus": idea['focus'],
                "content_sources": "Essential interview content"
            })
        
        print(f"📈 [OUTLINE GENERATION] Generated {len(additional_chapters)} essential chapters")
        return additional_chapters
    
    def _create_essential_chapters(self, content_analysis: Dict, person_name: str) -> list:
        """Create 4-6 essential chapters based on most critical themes and periods."""
        
        chapters = []
        chapter_number = 1
        
        life_periods = content_analysis.get('life_periods', [])
        major_themes = content_analysis.get('major_themes', [])
        
        # Strategy: Create chronological + thematic mix, but keep minimal
        
        # 1. Add the most important life period (usually childhood/early life)
        if life_periods:
            primary_period = life_periods[0]
            chapters.append({
                "number": chapter_number,
                "title": f"{primary_period.get('period', 'Early Life')}: {person_name}'s Foundation",
                "description": primary_period.get('description', 'Foundational life period'),
                "themes": primary_period.get('key_themes', ['early_life']),
                "time_period": primary_period.get('timeframe', 'Early years'),
                "key_focus": primary_period.get('description', 'Foundational experiences'),
                "content_sources": f"Interview content about {primary_period.get('period', 'early life')}"
            })
            chapter_number += 1
        
        # 2. Add 2-3 highest priority themes (avoid duplicates)
        high_priority_themes = [theme for theme in major_themes 
                               if theme.get('importance') == 'high'][:3]
        
        used_keywords = set()
        if chapters:
            # Extract keywords from existing chapters
            for chapter in chapters:
                title_words = chapter.get('title', '').lower().split()
                used_keywords.update(word.strip('.:,') for word in title_words)
        
        for theme in high_priority_themes:
            theme_name = theme.get('theme', '').lower()
            theme_keywords = theme_name.split()[:2]  # First 2 words
            
            # Skip if similar theme already exists
            if not any(keyword in used_keywords for keyword in theme_keywords):
                chapters.append({
                    "number": chapter_number,
                    "title": f"{theme.get('theme', 'Key Theme')}: {person_name}'s Journey",
                    "description": theme.get('description', 'Important life theme'),
                    "themes": [theme.get('theme', 'general')],
                    "time_period": "Throughout life",
                    "key_focus": theme.get('description', 'Theme exploration'),
                    "content_sources": f"Interview discussions about {theme.get('theme', 'this theme')}"
                })
                chapter_number += 1
                
                # Update used keywords
                used_keywords.update(theme_keywords)
                
                # Stop if we have enough chapters
                if len(chapters) >= 6:
                    break
        
        # 3. Add a concluding chapter if we have less than 4
        if len(chapters) < 4:
            chapters.append({
                "number": chapter_number,
                "title": f"Reflections and Legacy: {person_name}'s Impact",
                "description": "Life lessons, wisdom, and lasting impact",
                "themes": ["legacy", "reflection", "wisdom"],
                "time_period": "Present and future",
                "key_focus": "Personal growth and lasting influence",
                "content_sources": "Reflective interview content and life overview"
            })
        
        print(f"🎭 [OUTLINE GENERATION] Created {len(chapters)} essential chapters")
        return chapters

    def _create_fallback_outline_from_analysis(self, person_name: str, content_analysis: Dict, estimated_pages: int) -> Dict:
        """Create a fallback outline when AI analysis is available but outline generation fails."""
        
        print(f"🔄 [OUTLINE GENERATION] Creating fallback outline from analysis for {person_name}")
        
        # Use fallback analysis if needed
        if not content_analysis or not content_analysis.get('life_periods'):
            content_analysis = self._create_fallback_analysis(person_name, "")
        
        # Create chapters based on analysis using essential methods
        chapters = self._create_essential_chapters(content_analysis, person_name)
        
        # Ensure 4-6 chapter count
        if len(chapters) < 4:
            additional_needed = 4 - len(chapters)
            extra_chapters = self._generate_essential_chapters(
                content_analysis, chapters, additional_needed, person_name
            )
            chapters.extend(extra_chapters)
        elif len(chapters) > 6:
            chapters = self._select_best_chapters(chapters, 6)
        
        return {
            "title": f"The Life Story of {person_name}",
            "estimated_pages": estimated_pages,
            "chapters": chapters,
            "chapter_count": len(chapters),
            "writing_approach": "Essential biographical narrative with focused structure",
            "analysis_summary": content_analysis.get('analysis_summary', ''),
            "major_themes": [theme.get('theme', '') for theme in content_analysis.get('major_themes', [])],
            "key_periods": [period.get('period', '') for period in content_analysis.get('life_periods', [])]
        }

    async def _generate_context_aware_biography(
        self, job: BiographyJob, interview_analysis: Dict, outline_data: Dict, person_name: str
    ) -> str:
        """Generate biography using context-aware iterative approach with proper chapter structure."""
        
        segments = interview_analysis["segments"]
        total_segments = len(segments)
        chapters = outline_data.get("chapters", [])
        total_chapters = len(chapters)
        
        print(f"🎯 ENHANCED ITERATIVE: Starting context-aware generation")
        print(f"   📊 Total segments to process: {total_segments}")
        print(f"   📚 Total chapters in outline: {total_chapters}")
        print(f"   🔄 Context preserve ratio: {self.context_preserve_ratio:.0%}")
        
        # Load prompts from database
        prompts = await self._get_agent_prompts()
        writer_prompt = prompts["writer"]
        print(f"📋 ENHANCED ITERATIVE: Using writer prompt from database ({len(writer_prompt)} chars)")
        print(f"📄 Writer prompt preview: {writer_prompt[:200]}...")
        
        # Progress tracking
        base_progress = 25.0
        progress_range = 60.0
        
        # Track generated biography with proper chapter structure
        generated_biography = ""
        current_chapter_content = ""
        current_chapter_index = 0
        segments_per_chapter = max(1, total_segments // total_chapters) if total_chapters > 0 else 1
        
        print(f"   📊 Segments per chapter (initial): {segments_per_chapter}")
        
        # CRITICAL FIX: Adjust chapters to segments if needed
        if total_chapters > total_segments:
            print(f"   ⚠️ More chapters ({total_chapters}) than segments ({total_segments})")
            print(f"   🔧 Reducing chapters to match segments")
            total_chapters = max(1, total_segments)  # Can't have more chapters than segments
            # Update chapters list
            chapters = chapters[:total_chapters]
            print(f"   ✅ Adjusted to {total_chapters} chapters")
        
        # ADDITIONAL CRITICAL FIX: Enforce maximum 6 chapters regardless of outline
        if total_chapters > 6:
            print(f"   ⚠️ Too many chapters ({total_chapters}), enforcing maximum of 6 chapters")
            total_chapters = 6
            chapters = chapters[:6]
            print(f"   ✅ Enforced maximum: {total_chapters} chapters")
        
        # Create more balanced segment distribution
        chapter_segment_distribution = []
        remaining_segments = total_segments
        
        # Simple and reliable distribution
        base_segments_per_chapter = max(1, total_segments // total_chapters)
        extra_segments = total_segments % total_chapters
        
        for chapter_idx in range(total_chapters):
            # Each chapter gets base amount + 1 extra if available
            segments_for_chapter = base_segments_per_chapter
            if chapter_idx < extra_segments:
                segments_for_chapter += 1
            
            chapter_segment_distribution.append(segments_for_chapter)
        
        # Double-check: ensure sum equals total_segments
        actual_sum = sum(chapter_segment_distribution)
        if actual_sum != total_segments:
            print(f"   🔧 Adjusting distribution: {actual_sum} -> {total_segments}")
            # Simple fix: adjust last chapter
            chapter_segment_distribution[-1] += (total_segments - actual_sum)
        
        # Final safety: ensure all positive
        chapter_segment_distribution = [max(1, x) for x in chapter_segment_distribution]
        
        print(f"   📊 Balanced distribution: {chapter_segment_distribution}")
        
        current_chapter_segments_processed = 0
        
        for i, segment in enumerate(segments, 1):
            print(f"\n📝 PROCESSING SEGMENT {i}/{total_segments}")
            print(f"   📄 Segment content: {segment['content']}")
            print(f"   📄 Segment content: {len(segment['content']):,} chars")
            print(f"   📖 Current biography length: {len(generated_biography):,} chars")
            
            # Determine if we need to start a new chapter using balanced distribution
            current_chapter_segments_needed = chapter_segment_distribution[current_chapter_index] if current_chapter_index < len(chapter_segment_distribution) else 1
            is_new_chapter = current_chapter_segments_processed == 0 and current_chapter_index < total_chapters
            is_first_segment_in_chapter = current_chapter_segments_processed == 0
            
            # Get current chapter info
            current_chapter = chapters[current_chapter_index] if current_chapter_index < total_chapters else chapters[-1]
            chapter_title = current_chapter.get("title", f"Chapter {current_chapter_index + 1}")
            chapter_number = current_chapter.get("number", current_chapter_index + 1)
            
            print(f"   📚 Working on Chapter {chapter_number}: {chapter_title}")
            print(f"   📍 Segment {current_chapter_segments_processed + 1} of {current_chapter_segments_needed} for this chapter")
            
            # Generate content for this segment with chapter context
            new_content = await self._generate_chapter_segment_continuation(
                current_segment=segment,
                previous_biography=generated_biography,
                current_chapter_content=current_chapter_content,
                interview_analysis=interview_analysis,
                outline_data=outline_data,
                current_chapter=current_chapter,
                person_name=person_name,
                segment_num=i,
                total_segments=total_segments,
                is_new_chapter=is_new_chapter,
                is_first_segment_in_chapter=is_first_segment_in_chapter,
                writer_prompt=writer_prompt,
                job_id=job.id
            )
            
            # Add the new content
            if new_content.strip():
                # Clean duplicate chapter headers from AI response
                cleaned_content = self._remove_duplicate_chapter_headers(new_content, chapter_number, chapter_title)
                
                # If this is a new chapter, add chapter header
                if is_new_chapter:
                    if current_chapter_content.strip():
                        # Finish previous chapter
                        generated_biography += current_chapter_content + "\n\n"
                        current_chapter_content = ""
                    
                    # Start new chapter with proper header - ENFORCE MAXIMUM 6 CHAPTERS
                    if chapter_number <= 6:  # Only create chapters 1-6
                        chapter_header = f"Chapter {chapter_number}: {chapter_title}\n\n"
                        current_chapter_content = chapter_header
                        print(f"   ✨ Started new chapter: {chapter_header.strip()}")
                    else:
                        print(f"   🚫 Skipping chapter {chapter_number} - exceeds maximum of 6 chapters")
                        # Don't create new chapter, add content to previous chapter instead
                        pass
                
                # Add cleaned content to current chapter
                if current_chapter_content and not current_chapter_content.endswith('\n\n'):
                    current_chapter_content += '\n\n'
                current_chapter_content += cleaned_content.strip()
                
                print(f"   ✅ Added {len(cleaned_content):,} chars to chapter {chapter_number}")
                print(f"   📊 Chapter {chapter_number} now has {len(current_chapter_content):,} chars")
            else:
                print(f"   ⚠️ Empty response from AI for segment {i}")
            
            # Increment segments processed for current chapter
            current_chapter_segments_processed += 1
            
            # Move to next chapter if we've processed enough segments for current chapter
            current_chapter_segments_needed = chapter_segment_distribution[current_chapter_index] if current_chapter_index < len(chapter_segment_distribution) else 1
            if (current_chapter_segments_processed >= current_chapter_segments_needed and 
                current_chapter_index < total_chapters - 1):
                current_chapter_index += 1
                current_chapter_segments_processed = 0
                print(f"   📚 Moving to next chapter: Chapter {current_chapter_index + 1}")
            
            # Update progress
            progress = base_progress + (i / total_segments) * progress_range
            await self._update_job(job, JobStatus.ITERATIVE_WRITING, progress)
        
        # Add the final chapter content
        if current_chapter_content.strip():
            generated_biography += current_chapter_content + "\n\n"
        
        print(f"\n🎉 ENHANCED ITERATIVE: Completed all {total_segments} segments")
        print(f"   📊 Final biography length: {len(generated_biography):,} chars")
        print(f"   📚 Generated {total_chapters} chapters")
        print(f"   📖 Biography preview: {generated_biography[:300]}...")
        
        if not generated_biography.strip():
            print("⚠️  WARNING: Generated biography is empty!")
            return "No content generated"
        
        print("✅ ENHANCED ITERATIVE: Biography generation completed successfully")
        return generated_biography

    def _remove_duplicate_chapter_headers(self, content: str, chapter_number: int, chapter_title: str) -> str:
        """Remove ALL chapter headers from AI-generated content to prevent extra chapters in PDF."""
        
        if not content.strip():
            return content
        
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line_stripped = line.strip()
            
            # Skip various patterns of ALL chapter headers (not just duplicates)
            skip_line = False
            
            # Pattern 1: Any "Chapter X: ..." pattern
            if line_stripped.startswith("Chapter ") and ":" in line_stripped:
                skip_line = True
                print(f"   🧹 Removed chapter header: {line_stripped[:60]}...")
            
            # Pattern 2: Any "# Chapter X: ..." (markdown style)
            elif line_stripped.startswith("# Chapter ") and ":" in line_stripped:
                skip_line = True
                print(f"   🧹 Removed markdown chapter header: {line_stripped[:60]}...")
            
            # Pattern 3: Any "## Chapter X: ..." (markdown subheader style)
            elif line_stripped.startswith("## Chapter ") and ":" in line_stripped:
                skip_line = True
                print(f"   🧹 Removed markdown subheader: {line_stripped[:60]}...")
            
            # Pattern 4: Lines that look like chapter titles with numbers
            elif (len(line_stripped) < 150 and 
                  ("chapter" in line_stripped.lower()) and 
                  line_stripped.count(':') == 1 and
                  any(char.isdigit() for char in line_stripped)):
                skip_line = True
                print(f"   🧹 Removed potential chapter title: {line_stripped[:60]}...")
            
            # Pattern 5: Generic "Chapter X" patterns without colon
            elif line_stripped.startswith("Chapter ") and len(line_stripped.split()) <= 3:
                skip_line = True
                print(f"   🧹 Removed generic chapter number: {line_stripped}")
            
            # Pattern 6: Standalone chapter numbers at start of content
            elif (len(cleaned_lines) < 5 and  # Check first few lines
                  line_stripped.isdigit() and 
                  1 <= int(line_stripped) <= 20):  # Reasonable chapter number range
                skip_line = True
                print(f"   🧹 Removed standalone chapter number: {line_stripped}")
            
            # Pattern 7: Lines with "Navigating the Currents" type patterns (specific to user's issue)
            elif (len(line_stripped) < 100 and 
                  ("navigating" in line_stripped.lower() or 
                   "currents" in line_stripped.lower() or
                   line_stripped.count(":") == 1 and len(line_stripped.split()) > 5)):
                skip_line = True
                print(f"   🧹 Removed potential unwanted header: {line_stripped[:60]}...")
            
            # Pattern 8: Any line that starts with a number followed by colon (potential chapter header)
            elif (len(line_stripped) < 120 and 
                  line_stripped[0:2].replace(' ', '').replace('.', '').isdigit() and 
                  ':' in line_stripped and
                  len(line_stripped.split()) > 2):
                skip_line = True
                print(f"   🧹 Removed numbered header: {line_stripped[:60]}...")
            
            # Pattern 9: Lines that have typical chapter/section header patterns
            elif (len(line_stripped) < 150 and 
                  line_stripped.count(':') == 1 and
                  len(line_stripped.split()) >= 4 and
                  len(line_stripped.split()) <= 15 and
                  not line_stripped.lower().startswith(('in ', 'on ', 'at ', 'by ', 'with ', 'during ', 'after ', 'before '))):
                # This catches patterns like "From a different perspective, specifically considering advice"
                skip_line = True
                print(f"   🧹 Removed suspected header pattern: {line_stripped[:60]}...")
            
            # Pattern 10: Lines that contain "from a different perspective" or similar transitional headers
            elif (len(line_stripped) < 120 and 
                  any(phrase in line_stripped.lower() for phrase in [
                      'from a different perspective', 'from another perspective', 
                      'specifically considering', 'chapter', 'section'
                  ]) and 
                  (',' in line_stripped or ':' in line_stripped)):
                skip_line = True
                print(f"   🧹 Removed perspective/transitional header: {line_stripped[:60]}...")
            
            if not skip_line:
                cleaned_lines.append(line)
        
        cleaned_content = '\n'.join(cleaned_lines)
        
        # Additional cleanup: remove extra blank lines at the beginning
        cleaned_content = cleaned_content.lstrip('\n')
        
        # Log cleaning results
        if len(cleaned_content) < len(content):
            chars_removed = len(content) - len(cleaned_content)
            print(f"   🧹 Chapter header cleaning: removed {chars_removed} characters")
        
        return cleaned_content

    def _smart_trim_context(self, previous_biography: str, token_limit: int) -> str:
        """Smart context trimming based on token limit for iterative generation."""
        if not previous_biography:
            return ""
        
        # Estimate character to token ratio (roughly 1 token = 4 characters)
        char_limit = token_limit * 4
        
        # If content is already within limit, return as is
        if len(previous_biography) <= char_limit:
            return previous_biography
        
        # Preserve more recent content (from the end)
        preserved_chars = min(char_limit, self.min_context_chars * 3)
        trimmed_context = previous_biography[-preserved_chars:]
        
        # Try to start from a paragraph boundary for cleaner context
        lines = trimmed_context.split('\n')
        if len(lines) > 2:
            # Skip first partial line if it exists
            if not lines[0].strip().startswith(('Chapter ', '**')):
                trimmed_context = '\n'.join(lines[1:])
        
        return trimmed_context

    async def _generate_segment_continuation(
        self, current_segment: Dict, previous_biography: str, 
        interview_analysis: Dict, outline_data: Dict, person_name: str,
        segment_num: int, total_segments: int, writer_prompt: str
    ) -> str:
        """Generate continuation of biography based on previous context and current segment."""
        
        print(f"🔢 ENHANCED ITERATIVE SEGMENT {segment_num}: Applying smart context trimming")
        
        # Apply smart context trimming
        biography_context = self._smart_trim_context(previous_biography, self.safe_input_tokens)
        
        if biography_context:
            print(f"   📖 Smart trimmed context: {len(biography_context):,} chars (from {len(previous_biography):,} chars)")
        else:
            print(f"   📖 No previous biography - this is the first segment")
        
        # Get current segment content
        current_segment_text = current_segment.get('content', '')
        print(f"   📄 Current segment content: {len(current_segment_text):,} chars")
        
        # Estimate initial prompt size and trim if needed
        outline_section = json.dumps(outline_data, indent=2)
        estimated_tokens = (
            self.ai_service.estimate_tokens(writer_prompt) +
            self.ai_service.estimate_tokens(outline_section) +
            self.ai_service.estimate_tokens(biography_context) +
            self.ai_service.estimate_tokens(current_segment_text) +
            2000  # Buffer for instructions
        )
        
        print(f"   🧮 Initial token estimate: {estimated_tokens:,} tokens")
        
        # Apply additional trimming if over safe limit
        if estimated_tokens > self.safe_input_tokens:
            print(f"   ⚠️  Over safe token limit ({self.safe_input_tokens:,}), applying additional trimming")
            
            # Trim biography context more aggressively
            if len(biography_context) > 8000:
                new_context_size = max(8000, len(biography_context) // 2)
                biography_context = self._smart_trim_context(previous_biography, self.safe_input_tokens)
                if len(biography_context) > new_context_size:
                    biography_context = "..." + biography_context[-new_context_size:]
                print(f"   📖 Aggressively trimmed context to: {len(biography_context):,} chars")
            
            # Trim outline if still too large
            if estimated_tokens > self.max_input_tokens and len(outline_section) > 3000:
                # Keep only essential outline info
                minimal_outline = {
                    "title": outline_data.get("title", "Biography"),
                    "chapters": [
                        {"number": ch["number"], "title": ch["title"]} 
                        for ch in outline_data.get("chapters", [])[:6]  # Limit to 6 chapters
                    ]
                }
                outline_section = json.dumps(minimal_outline, indent=2)
                print(f"   📋 Trimmed outline to: {len(outline_section):,} chars")
            
            # Trim writer prompt if absolutely necessary
            if estimated_tokens > self.max_input_tokens and len(writer_prompt) > 2000:
                writer_prompt = writer_prompt[:2000] + "...\n\n[Writer prompt truncated for token limits]"
                print(f"   📝 Trimmed writer prompt to: {len(writer_prompt):,} chars")
        
        # Create the main prompt by combining DB prompt with content
        prompt = f"""=========== WRITER AGENT INSTRUCTIONS (FROM DATABASE) ===========
{writer_prompt}

=========== OUTLINE FOR REFERENCE ===========
{json.dumps(outline_data, indent=2)}

=========== PREVIOUS BIOGRAPHY CONTENT (MAINTAIN CONTINUITY) ===========
{biography_context}

=========== NEW INTERVIEW CONTENT TO INCORPORATE ===========
{current_segment_text}

=========== ENHANCED ITERATIVE GENERATION INSTRUCTIONS ===========
You are continuing a biography using enhanced iterative generation. This is segment {segment_num} of {total_segments}.

CRITICAL NAME USAGE - USE ONLY THESE EXACT TAGS:
- Use {self.name_tags['USER_NAME']} for the full name
- Use {self.name_tags['FIRST_NAME']} for just the first name 
- Use {self.name_tags['HE_SHE']} for he/she pronouns
- Use {self.name_tags['HIS_HER']} for his/her possessives
- Use {self.name_tags['HIM_HER']} for him/her object pronouns

CONTEXT-AWARE REQUIREMENTS:
- Continue naturally from where the previous content ended
- Incorporate the new interview information seamlessly  
- Maintain the established voice and style from previous sections
- DO NOT repeat information already covered in the previous biography
- Build upon existing content to create a flowing, book-quality narrative
- Write 1,500-4,000 words for this segment

ANTI-REPETITION:
- Focus on NEW insights, details, and perspectives from the current interview segment
- Reference earlier content when appropriate but don't rehash it
- If similar themes arise, explore them from fresh angles

NAME TAG SYSTEM:
- Use the specified name tags exactly as shown
- These will be automatically replaced with correct information

This is segment {segment_num} of {total_segments} in the enhanced iterative process."""
        
        # Create system message based on generation type
        if segment_num == 1:
            generation_type = "opening"
            system_instruction = "Begin the biography with an engaging opening that draws readers in."
        elif segment_num == total_segments:
            generation_type = "conclusion"  
            system_instruction = "Bring the biography to a thoughtful conclusion that reflects on the person's legacy and impact."
        else:
            generation_type = "continuation"
            system_instruction = f"Continue the biography naturally from where it left off. This is segment {segment_num} of {total_segments}."
        
        system_message = f"""You are a master biographer writing a comprehensive life story using enhanced iterative generation.

CRITICAL REQUIREMENTS:
- {system_instruction}
- Use the database writer prompt instructions as your foundation
- Maintain narrative continuity with the previous biography content
- Never repeat information already covered in previous sections
- Build upon existing content to create a flowing, book-quality narrative
- Write 1,500-4,000 words for this segment

ANTI-REPETITION:
- Focus on NEW insights, details, and perspectives from the current interview segment
- Reference earlier content when appropriate but don't rehash it
- If similar themes arise, explore them from fresh angles

NAME TAG SYSTEM:
- Use the specified name tags exactly as shown
- These will be automatically replaced with correct information

This is segment {segment_num} of {total_segments} in the enhanced iterative process."""

        # Log the actual prompt structure before sending to AI
        print(f"🚀 ENHANCED ITERATIVE SEGMENT {segment_num} - PROMPT STRUCTURE:")
        print(f"   📏 Total prompt: {len(prompt):,} chars (~{self.ai_service.estimate_tokens(prompt):,} tokens)")
        print(f"   📏 System message: {len(system_message):,} chars (~{self.ai_service.estimate_tokens(system_message):,} tokens)")
        print(f"   📏 TOTAL: {len(prompt) + len(system_message):,} chars (~{self.ai_service.estimate_tokens(prompt) + self.ai_service.estimate_tokens(system_message):,} tokens)")
        print(f"")
        print(f"   📋 DB Writer prompt section: {len(writer_prompt):,} chars")
        print(f"   📋 Outline section: {len(outline_section):,} chars")
        print(f"   📖 Previous biography context: {len(biography_context):,} chars")
        print(f"   📄 New interview content: {len(current_segment_text):,} chars") 
        print(f"   📝 Instructions section: ~{len(prompt) - len(writer_prompt) - len(outline_section) - len(biography_context) - len(current_segment_text):,} chars")

        try:
            # Log the actual prompt size before sending to AI
            full_prompt = prompt
            system_msg = system_message
            
            prompt_length = len(full_prompt)
            system_length = len(system_msg)
            total_length = prompt_length + system_length
            
            # Estimate tokens
            estimated_prompt_tokens = self.ai_service.estimate_tokens(full_prompt)
            estimated_system_tokens = self.ai_service.estimate_tokens(system_msg)
            estimated_total_tokens = estimated_prompt_tokens + estimated_system_tokens
            
            print(f"🚀 ENHANCED ITERATIVE SEGMENT {segment_num} - FINAL PROMPT:")
            print(f"   📏 Prompt length: {prompt_length:,} chars (~{estimated_prompt_tokens:,} tokens)")
            print(f"   📏 System length: {system_length:,} chars (~{estimated_system_tokens:,} tokens)")
            print(f"   📏 TOTAL INPUT: {total_length:,} chars (~{estimated_total_tokens:,} tokens)")
            print(f"   📖 Previous context included: {'YES' if biography_context else 'NO'} ({len(biography_context):,} chars)")
            
            # Final safety check
            if estimated_total_tokens > self.max_input_tokens:
                print(f"   🚨 WARNING: Still over max token limit ({self.max_input_tokens:,})!")
                print(f"   🔧 Consider reducing segment size or context preserve ratio")
            elif estimated_total_tokens > self.safe_input_tokens:
                print(f"   ⚠️  Above safe limit but within max - monitoring performance")
            else:
                print(f"   ✅ Within safe token limits")
            
            response = await self.ai_service.generate_completion(
                prompt, 
                system_message,
                max_tokens=None,  # Use maximum output tokens
                temperature=0.8   # Higher creativity for better narrative flow
            )
            
            print(f"   🤖 AI RESPONSE RECEIVED:")
            print(f"       Generated: {len(response):,} chars")
            print(f"       Preview: {response[:200]}...")
            
            # Replace name tags in the generated content
            original_response = response.strip()
            cleaned_response = self._replace_name_tags(original_response, person_name)
            
            # Log tag replacements if any occurred
            if original_response != cleaned_response:
                print(f"   🏷️ APPLIED NAME TAG REPLACEMENTS:")
                print(f"       Original length: {len(original_response):,} chars")
                print(f"       After replacement: {len(cleaned_response):,} chars")
                print(f"       Sample change: {original_response[:100]} -> {cleaned_response[:100]}")
            else:
                print(f"   🏷️ No name tag replacements needed")
            
            return cleaned_response
            
        except Exception as e:
            print(f"❌ Error generating segment {segment_num}: {e}")
            return ""

    async def _generate_chapter_segment_continuation(
        self, current_segment: Dict, previous_biography: str, current_chapter_content: str,
        interview_analysis: Dict, outline_data: Dict, current_chapter: Dict, person_name: str,
        segment_num: int, total_segments: int, is_new_chapter: bool, is_first_segment_in_chapter: bool,
        writer_prompt: str, job_id: str = None
    ) -> str:
        """Generate continuation of biography using iterative improvement with all DB prompts."""
        
        print(f"🔢 ENHANCED ITERATIVE CHAPTER SEGMENT {segment_num}: Starting iterative generation")
        print(f"🎯 Target per segment: {self.target_chars_per_segment:,} chars (~5,500+ words)")
        print(f"🔄 Iterations planned: {self.iterations_per_segment}")
        
        # Get all required prompts from database
        prompts = await self._get_agent_prompts()
        evaluation_prompt = prompts.get("evaluation", "Evaluate the content quality and provide improvement suggestions.")
        rewrite_prompt = prompts.get("rewrite", "Rewrite and improve the content based on the evaluation feedback.")
        
        # Apply smart context trimming
        biography_context = self._smart_trim_context(previous_biography, self.safe_input_tokens)
        chapter_context = current_chapter_content[-5000:] if current_chapter_content else ""
        
        # Create enhanced system message with anti-repetition instructions
        system_message = f"""You are the Enhanced Biography Writer specializing in iterative content generation.

CRITICAL OBJECTIVES:
- Generate HIGH-QUALITY biographical content (~{self.target_chars_per_segment:,} characters minimum)
- Maintain narrative flow and continuity 
- Extract EVERY detail from interview segment
- Avoid ANY repetition from previous content
- Create engaging, book-quality prose
- Focus on depth, storytelling, and comprehensive coverage

STRICT FORMATTING RULES:
- NEVER generate chapter headers or titles (e.g., "Chapter X:", "# Chapter", etc.)
- NEVER create new section breaks or chapter divisions
- NEVER write numbered headings (e.g., "1.", "2.", etc.)
- NEVER write lines with colons that could be headers (e.g., "From a different perspective:")
- Only generate flowing narrative prose paragraphs
- Start writing immediately with narrative text, not headers or titles

This is segment {segment_num}/{total_segments} - make it substantial and unique."""

        # Generate initial content using writer prompt
        content_request = f"""
{writer_prompt}

Person: {person_name}
Current Chapter: {current_chapter.get('title', 'Chapter')} - {current_chapter.get('description', '')}

=========== CONTEXT: PREVIOUS BIOGRAPHY ===========
{biography_context[-3000:] if biography_context else 'This is the beginning of the biography.'}

=========== CONTEXT: CURRENT CHAPTER CONTENT ===========
{chapter_context}

=========== INTERVIEW SEGMENT TO EXPAND ===========
{current_segment['content']}

GENERATION INSTRUCTIONS:
- Create substantial biographical content (target {self.target_chars_per_segment:,}+ characters)
- Extract and expand on EVERY detail from the interview segment
- Maintain perfect continuity with previous content
- Use rich narrative techniques and storytelling
- Ensure zero repetition from previous biography
- Focus on depth, personality insights, and vivid descriptions
- Format with proper paragraph structure and flow

CRITICAL FORMATTING RESTRICTIONS:
- NEVER write chapter headers, titles, or section breaks (e.g., "Chapter 8:", "# Chapter", etc.)
- NEVER create new chapter divisions or numbered sections  
- NEVER write lines with colons that look like headers (e.g., "From a different perspective:", "Chapter 9 from a different perspective:")
- NEVER write numbered lists or bullet points at the beginning of paragraphs
- Only generate flowing narrative prose that continues seamlessly from previous content
- Start immediately with narrative text, not headers, titles, or structural elements

Return comprehensive biographical content that thoroughly explores this segment."""

        print(f"📝 [INITIAL] Generating base content for segment {segment_num}")
        
        try:
            # Initial generation
            initial_content = await self.ai_service.generate_completion(
                content_request, system_message, max_tokens=None, temperature=0.8
            )
            
            if not initial_content or not initial_content.strip():
                print(f"❌ [INITIAL] Empty response from AI for segment {segment_num}")
                fallback = f"**{current_chapter.get('title', 'Chapter')}**\n\n" + current_segment['content'][:2000] + "..."
                return fallback
            
            current_content = initial_content.strip()
            print(f"✅ [INITIAL] Generated {len(current_content):,} chars (target: {self.target_chars_per_segment:,})")
            
            # Check for repetition against existing content  
            existing_content = previous_biography + current_chapter_content
            if self._check_for_repetition(current_content, existing_content):
                print(f"⚠️ [ANTI-REPETITION] Initial content has repetition - will address in iterations")
            
            # Apply iterative improvements if enabled
            if self.use_evaluation_rewrite and self.iterations_per_segment > 1:
                print(f"🔄 [ITERATIVE] Starting {self.iterations_per_segment - 1} improvement iterations")
                
                for iteration in range(1, self.iterations_per_segment):
                    print(f"📊 [ITERATION {iteration}] Evaluating and improving content...")
                    
                    try:
                        # Evaluate current content quality
                        evaluation = await self._evaluate_content_quality(
                            current_content, current_segment['content'], evaluation_prompt
                        )
                        
                        # Check for repetition again
                        if self._check_for_repetition(current_content, existing_content):
                            evaluation['has_repetition'] = True
                            evaluation['evaluation'] += "\n\nIMPORTANT: Detected repetitive content that MUST be completely rewritten with new details."
                        
                        # Rewrite content based on evaluation
                        improved_content = await self._rewrite_content_iteratively(
                            current_content, evaluation, current_segment['content'], 
                            rewrite_prompt, iteration
                        )
                        
                        # Verify improvement
                        if improved_content and len(improved_content) > len(current_content) * 0.8:  # Ensure substantial content
                            current_content = improved_content
                            print(f"✅ [ITERATION {iteration}] Content improved and expanded")
                        else:
                            print(f"⚠️ [ITERATION {iteration}] Content too short or failed, keeping previous version")
                            break
                            
                    except Exception as iteration_error:
                        print(f"❌ [ITERATION {iteration}] Error during iteration: {iteration_error}")
                        print(f"🔄 [ITERATION {iteration}] Continuing with current content...")
                        break
                
                print(f"🎯 [ITERATIVE] All iterations completed:")
                print(f"    Final length: {len(current_content):,} chars")
                print(f"    Target achievement: {(len(current_content) / self.target_chars_per_segment * 100):.1f}%")
            
            # Final repetition check and content validation
            if self._check_for_repetition(current_content, existing_content):
                print(f"❌ [FINAL CHECK] Content still has repetition after iterations")
                # Could add emergency rewrite here if needed
            else:
                print(f"✅ [FINAL CHECK] Content is unique and substantial")
            
            # Update word tracking with generated content
            if job_id:
                word_tracker = GlobalWordTracker.get_tracker(job_id)
                word_tracker.update_word_counts(current_content)

                # Check for overused words and suggest replacements
                overused_words = word_tracker.get_overused_words()
                if overused_words:
                    print(f"⚠️ [WORD TRACKING] Found {len(overused_words)} overused words in segment {segment_num}")
                    for word, info in overused_words.items():
                        print(f"   '{word}': {info['count']} times ({info['frequency_per_1000']:.1f}/1000 words)")

                    # Apply word replacement suggestions
                    current_content = word_tracker.suggest_replacement_text(current_content, max_replacements=2)

            print(f"📚 [COMPLETED] Segment {segment_num} generation finished: {len(current_content):,} chars")
            return current_content
            
        except Exception as e:
            print(f"❌ [ERROR] Failed to generate segment {segment_num}: {e}")
            import traceback
            print(f"📊 [ERROR DETAILS] {traceback.format_exc()}")
            
            # Fallback to simple generation
            fallback = f"**{current_chapter.get('title', 'Chapter')}**\n\n" + current_segment['content'][:2000] + "..."
            print(f"🔄 [FALLBACK] Using fallback content: {len(fallback)} chars")
            return fallback

    async def _update_job(self, job: BiographyJob, status: JobStatus, progress: float):
        """Update job status and progress."""
        job.status = status
        job.progress_percentage = progress
        self.db.commit()
        
        # Send WebSocket update
        await manager.send_job_update(job.id, {
            "status": status.value,
            "progress_percentage": progress,
            "message": f"Enhanced iterative processing: {progress:.1f}%"
        })

    def _is_female_name(self, name: str) -> bool:
        """Simple heuristic to determine if name is likely female."""
        first_name = name.split()[0].lower()
        female_indicators = ['jennifer', 'sarah', 'mary', 'lisa', 'susan', 'maria', 'anna', 'emma', 'olivia']
        male_indicators = ['john', 'michael', 'david', 'james', 'robert', 'william', 'thomas', 'richard']
        
        if first_name in female_indicators:
            return True
        elif first_name in male_indicators:
            return False
        else:
            # Default to female for ambiguous names (can be improved with better name database)
            # Check common female name endings, but default to True for unknown names
            if first_name.endswith(('a', 'e', 'ie', 'y')):
                return True
            # For completely unknown names, default to female
            return True

    def _replace_name_tags(self, content: str, person_name: str) -> str:
        """Replace name tags with actual person information."""
        if not person_name:
            return content
        
        # Extract components
        name_parts = person_name.split()
        first_name = name_parts[0] if name_parts else person_name
        full_name = person_name
        
        # Determine gender pronouns
        is_female = self._is_female_name(first_name)
        he_she = 'she' if is_female else 'he'
        his_her = 'her' if is_female else 'his'
        him_her = 'her' if is_female else 'him'
        
        # Replace all name tags
        replacements = {
            self.name_tags['USER_NAME']: full_name,
            self.name_tags['FIRST_NAME']: first_name,
            self.name_tags['HE_SHE']: he_she,
            self.name_tags['HIS_HER']: his_her,
            self.name_tags['HIM_HER']: him_her,
        }
        
        # Apply replacements
        result = content
        for tag, replacement in replacements.items():
            result = result.replace(tag, replacement)
        
        # Additional cleanup for malformed tags that AI might generate
        malformed_tag_patterns = [
            (r'\{HIM_HERSELF\}', him_her),
            (r'\{HIS_HERS\}', his_her),
            (r'\{HE_SHE_THEY\}', he_she),
            (r'\{USER_FIRST_NAME\}', first_name),
            (r'\{PERSON_NAME\}', full_name),
            (r'\{NAME\}', full_name),
            (r'\{FIRST\}', first_name),
            (r'\{HIM_HER_THEM\}', him_her),
            (r'\{HIS_HER_THEIR\}', his_her),
        ]
        
        for pattern, replacement in malformed_tag_patterns:
            result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)
        
        # Clean up any remaining unrecognized tags in format {WORD_WORD}
        # This catches tags AI might invent that we haven't seen before
        def replace_unknown_tags(match):
            tag_content = match.group(1)
            print(f"⚠️ [NAME TAG WARNING] Found unrecognized tag: {{{tag_content}}} - replacing with appropriate pronoun")
            
            # Try to guess intent based on tag content
            tag_lower = tag_content.lower()
            if any(word in tag_lower for word in ['him', 'her', 'object']):
                return him_her
            elif any(word in tag_lower for word in ['his', 'hers', 'possess']):
                return his_her
            elif any(word in tag_lower for word in ['he', 'she', 'subject']):
                return he_she
            elif any(word in tag_lower for word in ['name', 'user', 'person']):
                if 'first' in tag_lower:
                    return first_name
                else:
                    return full_name
            else:
                # Default fallback
                return first_name
        
        # Pattern to catch any remaining {WORD_WORD} style tags
        result = re.sub(r'\{([A-Z_]+)\}', replace_unknown_tags, result)
        
        return result 

    def _calculate_content_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text pieces to detect repetition."""
        if not text1 or not text2:
            return 0.0
        
        # Simple word-based similarity check
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0

    def _check_for_repetition(self, new_content: str, existing_content: str) -> bool:
        """Check if new content is too similar to existing content."""
        if not self.anti_repetition_enabled:
            return False
        
        if not existing_content or not new_content:
            return False
        
        # Split into paragraphs for granular checking
        new_paragraphs = [p.strip() for p in new_content.split('\n\n') if p.strip() and len(p.strip()) > 50]
        existing_paragraphs = [p.strip() for p in existing_content.split('\n\n') if p.strip() and len(p.strip()) > 50]
        
        if not new_paragraphs:
            return False
        
        repetitive_paragraphs = 0
        
        for new_para in new_paragraphs:
            if len(new_para) < 100:  # Skip very short paragraphs
                continue
                
            for existing_para in existing_paragraphs:
                if len(existing_para) < 100:
                    continue
                    
                similarity = self._calculate_content_similarity(new_para, existing_para)
                if similarity > self.repetition_similarity_threshold:
                    repetitive_paragraphs += 1
                    break
        
        if not new_paragraphs:
            return False
            
        repetition_ratio = repetitive_paragraphs / len(new_paragraphs)
        is_repetitive = repetition_ratio > 0.3  # More than 30% repetitive paragraphs
        
        if is_repetitive:
            print(f"⚠️ [ANTI-REPETITION] Detected {repetition_ratio:.1%} repetitive content ({repetitive_paragraphs}/{len(new_paragraphs)} paragraphs)")
        
        return is_repetitive

    def _final_repetition_check_and_cleanup(self, biography_content: str) -> str:
        """Final check to remove all repetitive content before PDF generation."""
        if not biography_content or not biography_content.strip():
            return biography_content
        
        print(f"🔍 [FINAL CLEANUP] Analyzing {len(biography_content):,} characters for repetition...")
        
        # Split content into paragraphs
        paragraphs = [p.strip() for p in biography_content.split('\n\n') if p.strip()]
        
        # Track unique paragraph hashes to detect exact duplicates
        unique_paragraphs = []
        seen_hashes = set()
        
        # Track similar paragraphs using similarity threshold
        removed_count = 0
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) < 50:  # Keep very short paragraphs (likely headers/transitions)
                unique_paragraphs.append(paragraph)
                continue
            
            # Create normalized hash for exact duplicate detection
            normalized = re.sub(r'[^\w\s]', '', paragraph.lower())
            normalized = re.sub(r'\s+', ' ', normalized).strip()
            paragraph_hash = hash(normalized)
            
            # Check for exact duplicates
            if paragraph_hash in seen_hashes:
                print(f"🗑️ [FINAL CLEANUP] Removing exact duplicate paragraph {i+1}")
                removed_count += 1
                continue
            
            # Check for high similarity to existing paragraphs
            is_similar = False
            for existing_para in unique_paragraphs[-10:]:  # Check last 10 paragraphs only
                if len(existing_para) < 50:
                    continue
                    
                similarity = self._calculate_content_similarity(paragraph, existing_para)
                if similarity > 0.8:  # Very high similarity threshold for final cleanup
                    print(f"🗑️ [FINAL CLEANUP] Removing highly similar paragraph {i+1} (similarity: {similarity:.2f})")
                    is_similar = True
                    removed_count += 1
                    break
            
            if not is_similar:
                unique_paragraphs.append(paragraph)
                seen_hashes.add(paragraph_hash)
        
        # Reconstruct cleaned content
        cleaned_content = '\n\n'.join(unique_paragraphs)
        
        # Additional cleanup: Remove chapter headers that are numbered > 6
        lines = cleaned_content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line_stripped = line.strip()
            # Skip chapter headers with numbers > 6
            if re.match(r'^Chapter\s+([7-9]|\d{2,})\s*:', line_stripped, re.IGNORECASE):
                print(f"🗑️ [FINAL CLEANUP] Removing chapter header: {line_stripped}")
                continue
            # Skip standalone chapter numbers > 6
            elif re.match(r'^([7-9]|\d{2,})\s*$', line_stripped):
                print(f"🗑️ [FINAL CLEANUP] Removing chapter number: {line_stripped}")
                continue
            else:
                cleaned_lines.append(line)
        
        final_cleaned_content = '\n'.join(cleaned_lines)
        
        print(f"📊 [FINAL CLEANUP] Cleanup complete:")
        print(f"   Original paragraphs: {len(paragraphs)}")
        print(f"   Removed paragraphs: {removed_count}")
        print(f"   Final paragraphs: {len(unique_paragraphs)}")
        print(f"   Character reduction: {len(biography_content) - len(final_cleaned_content):,} chars")
        
        return final_cleaned_content

    async def _evaluate_content_quality(self, content: str, interview_segment: str, evaluation_prompt: str) -> Dict:
        """Evaluate content quality using evaluation prompt from database."""
        print(f"🔍 [EVALUATION] Evaluating content quality ({len(content):,} chars)")
        
        system_message = """You are the Enhanced Evaluation Agent for iterative biography improvement.

Evaluate the biographical content for:
1. Writing quality and engagement
2. Factual accuracy to interview content
3. Repetition and redundancy issues
4. Content depth and detail level
5. Flow and narrative coherence

Provide specific, actionable feedback for improvement."""

        evaluation_request = f"""
{evaluation_prompt}

=========== INTERVIEW SEGMENT FOR REFERENCE ===========
{interview_segment[:5000]}

=========== GENERATED BIOGRAPHY CONTENT TO EVALUATE ===========
{content[:10000]}

EVALUATION FOCUS:
- Rate content quality (1-10)
- Identify specific repetition issues
- Suggest improvements for depth and detail
- Check accuracy against interview content
- Provide actionable improvement recommendations

Return your evaluation as specific, actionable feedback."""

        try:
            evaluation = await self.ai_service.generate_completion(
                evaluation_request, system_message, max_tokens=2000, temperature=0.7
            )
            
            if not evaluation or not evaluation.strip():
                print(f"❌ [EVALUATION] Empty evaluation response")
                return {
                    "evaluation": "Content needs improvement for better quality and engagement.",
                    "quality_score": 6.0,
                    "has_repetition": False,
                    "needs_expansion": True
                }
            
            # Parse evaluation to extract key metrics
            quality_score = 7.0  # Default score
            has_repetition = "repetition" in evaluation.lower() or "repeat" in evaluation.lower()
            needs_expansion = "expand" in evaluation.lower() or "more detail" in evaluation.lower()
            
            print(f"📊 [EVALUATION] Quality feedback received ({len(evaluation)} chars)")
            print(f"📈 [EVALUATION] Estimated quality score: {quality_score:.1f}/10")
            print(f"🔍 [EVALUATION] Repetition detected: {'YES' if has_repetition else 'NO'}")
            print(f"📝 [EVALUATION] Needs expansion: {'YES' if needs_expansion else 'NO'}")
            
            return {
                "evaluation": evaluation,
                "quality_score": quality_score,
                "has_repetition": has_repetition,
                "needs_expansion": needs_expansion
            }
            
        except Exception as e:
            print(f"❌ [EVALUATION] Error: {e}")
            return {
                "evaluation": "Evaluation failed - content needs improvement for better quality.", 
                "quality_score": 5.0, 
                "has_repetition": False, 
                "needs_expansion": True
            }

    async def _rewrite_content_iteratively(self, content: str, evaluation: Dict, interview_segment: str, 
                                         rewrite_prompt: str, iteration: int) -> str:
        """Rewrite content based on evaluation feedback using rewrite prompt from database."""
        print(f"✍️ [REWRITE] Iteration {iteration}: Improving content based on evaluation")
        
        system_message = f"""You are the Enhanced Rewrite Agent for iterative biography improvement (Iteration {iteration}).

CRITICAL OBJECTIVES:
- Address specific issues mentioned in the evaluation
- Eliminate repetitive content completely
- Expand content with NEW details from interview
- Improve narrative flow and engagement
- Maintain factual accuracy to source material

This is iteration {iteration} - make substantial improvements while avoiding repetition."""

        rewrite_request = f"""
{rewrite_prompt}

=========== EVALUATION FEEDBACK ===========
{evaluation['evaluation']}

Quality Score: {evaluation['quality_score']:.1f}/10
Has Repetition: {'YES' if evaluation['has_repetition'] else 'NO'}
Needs Expansion: {'YES' if evaluation['needs_expansion'] else 'NO'}

=========== INTERVIEW SEGMENT FOR NEW DETAILS ===========
{interview_segment}

=========== CURRENT CONTENT TO IMPROVE ===========
{content}

REWRITE INSTRUCTIONS FOR ITERATION {iteration}:
- Address ALL issues mentioned in the evaluation feedback
- If repetition detected, eliminate redundant sections completely
- If expansion needed, add substantial new details from interview
- Improve writing quality and narrative flow
- Expand content by 20-30% with new perspectives and details
- Ensure final content is engaging and comprehensive

Return the substantially improved and expanded biography content."""

        try:
            improved_content = await self.ai_service.generate_completion(
                rewrite_request, system_message, max_tokens=None, temperature=0.8
            )
            
            if not improved_content or not improved_content.strip():
                print(f"❌ [REWRITE] Iteration {iteration} returned empty content")
                return content
            
            improved_content = improved_content.strip()
            
            print(f"✅ [REWRITE] Iteration {iteration} completed:")
            print(f"    Original: {len(content):,} chars")
            print(f"    Improved: {len(improved_content):,} chars")
            print(f"    Change: {((len(improved_content) - len(content)) / len(content) * 100):+.1f}%")
            
            return improved_content
            
        except Exception as e:
            print(f"❌ [REWRITE] Iteration {iteration} failed: {e}")
            return content 

    async def _analyze_interview_content(self, interview_text: str, person_name: str) -> Dict:
        """Analyze interview content to extract key themes, life periods, and topics for automatic chapter generation."""
        
        print(f"🔍 [INTERVIEW ANALYSIS] Analyzing interview content for {person_name}")
        print(f"📊 [INTERVIEW ANALYSIS] Content length: {len(interview_text):,} characters")
        
        # Use first portion of interview for analysis (to stay within token limits)
        analysis_content = interview_text[:15000]  # ~15k chars for comprehensive analysis
        
        system_message = f"""You are an Expert Interview Content Analyzer for biographical structure creation.

Your task is to analyze the interview content and identify:
1. Key life themes and major topics discussed
2. Chronological periods and life stages mentioned
3. Important relationships, places, and experiences
4. Significant events, challenges, and achievements
5. Personal values, beliefs, and defining characteristics

Create a comprehensive analysis that will be used to automatically generate personalized biography chapters."""

        analysis_prompt = f"""
Analyze this interview content for {person_name} and extract key biographical themes and structure:

=========== INTERVIEW CONTENT TO ANALYZE ===========
{analysis_content}

ANALYSIS REQUIREMENTS:

1. LIFE PERIODS: Identify distinct chronological periods mentioned (childhood, education, career phases, etc.)
2. MAJOR THEMES: Extract recurring themes and important topics discussed
3. KEY RELATIONSHIPS: Identify important people mentioned (family, friends, mentors, colleagues)
4. SIGNIFICANT PLACES: Note locations that played important roles in their life
5. MAJOR EVENTS: Identify pivotal events, challenges, achievements, or turning points
6. PERSONAL CHARACTERISTICS: Note personality traits, values, beliefs, or defining qualities
7. CAREER/WORK: Identify professional aspects, career development, or work-related themes
8. INTERESTS/PASSIONS: Note hobbies, interests, or things they're passionate about

Return your analysis as a JSON structure with this exact format:
{{
    "person_name": "{person_name}",
    "analysis_summary": "Brief overview of the person's life story",
    "life_periods": [
        {{
            "period": "Period name (e.g., 'Early Childhood', 'College Years')",
            "timeframe": "Age range or years if mentioned", 
            "description": "What happened during this period",
            "key_themes": ["theme1", "theme2"]
        }}
    ],
    "major_themes": [
        {{
            "theme": "Theme name",
            "description": "Detailed description of this theme",
            "importance": "high/medium/low",
            "examples": ["example1", "example2"]
        }}
    ],
    "key_relationships": [
        {{
            "person": "Person name or relationship type",
            "relationship": "Nature of relationship",
            "significance": "How they influenced the person's life"
        }}
    ],
    "significant_places": [
        {{
            "place": "Location name",
            "significance": "Why this place was important",
            "timeframe": "When they were there"
        }}
    ],
    "major_events": [
        {{
            "event": "Event description",
            "impact": "How it affected their life",
            "timeframe": "When it happened"
        }}
    ],
    "suggested_chapters": [
        {{
            "chapter_title": "Personalized chapter title based on actual content",
            "chapter_focus": "What this chapter should cover",
            "themes_covered": ["theme1", "theme2"],
            "content_sources": "What parts of interview relate to this"
        }}
    ]
}}

Focus on creating PERSONALIZED, SPECIFIC chapter suggestions based on the actual content discussed, not generic life stages.

IMPORTANT: Create exactly 4-6 suggested chapters maximum. Quality over quantity - each chapter should cover substantial content and be truly meaningful."""

        try:
            print(f"🤖 [INTERVIEW ANALYSIS] Sending analysis request to AI...")
            
            analysis_response = await self.ai_service.generate_completion(
                analysis_prompt, system_message, max_tokens=4000, temperature=0.7
            )
            
            if not analysis_response or not analysis_response.strip():
                print(f"❌ [INTERVIEW ANALYSIS] Empty response from AI")
                return self._create_fallback_analysis(person_name, interview_text)
            
            # Clean response from markdown blocks if present
            cleaned_response = analysis_response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response.replace('```json', '').replace('```', '').strip()
            elif cleaned_response.startswith('```'):
                lines = cleaned_response.split('\n')
                if lines[0].startswith('```'):
                    lines = lines[1:]
                if lines and lines[-1].strip() == '```':
                    lines = lines[:-1]
                cleaned_response = '\n'.join(lines).strip()
            
            print(f"🧹 [INTERVIEW ANALYSIS] Cleaned response preview: {cleaned_response[:300]}...")
            
            analysis_data = json.loads(cleaned_response)
            
            # Validate analysis structure
            required_keys = ['person_name', 'life_periods', 'major_themes', 'suggested_chapters']
            if all(key in analysis_data for key in required_keys):
                
                # CRITICAL: Enforce maximum 6 chapters at code level
                suggested_chapters = analysis_data.get('suggested_chapters', [])
                if len(suggested_chapters) > 6:
                    print(f"⚠️ [INTERVIEW ANALYSIS] AI suggested {len(suggested_chapters)} chapters, limiting to 6")
                    analysis_data['suggested_chapters'] = suggested_chapters[:6]
                elif len(suggested_chapters) < 4:
                    print(f"⚠️ [INTERVIEW ANALYSIS] AI suggested only {len(suggested_chapters)} chapters, will expand to 4 minimum")
                
                print(f"✅ [INTERVIEW ANALYSIS] Successfully analyzed interview:")
                print(f"    📊 Life periods: {len(analysis_data.get('life_periods', []))}")
                print(f"    🎯 Major themes: {len(analysis_data.get('major_themes', []))}")
                print(f"    📚 Suggested chapters: {len(analysis_data.get('suggested_chapters', []))}")
                print(f"    👥 Key relationships: {len(analysis_data.get('key_relationships', []))}")
                print(f"    📍 Significant places: {len(analysis_data.get('significant_places', []))}")
                print(f"    🎉 Major events: {len(analysis_data.get('major_events', []))}")
                
                return analysis_data
            else:
                print(f"⚠️ [INTERVIEW ANALYSIS] Invalid analysis structure, using fallback")
                return self._create_fallback_analysis(person_name, interview_text)
                
        except json.JSONDecodeError as e:
            print(f"❌ [INTERVIEW ANALYSIS] JSON parsing failed: {e}")
            return self._create_fallback_analysis(person_name, interview_text)
        except Exception as e:
            print(f"❌ [INTERVIEW ANALYSIS] Analysis failed: {e}")
            return self._create_fallback_analysis(person_name, interview_text)
    
    def _create_fallback_analysis(self, person_name: str, interview_text: str) -> Dict:
        """Create a basic fallback analysis when AI analysis fails."""
        print(f"🔄 [INTERVIEW ANALYSIS] Creating fallback analysis for {person_name}")
        
        # Basic analysis based on content length and common patterns
        word_count = len(interview_text.split())
        
        return {
            "person_name": person_name,
            "analysis_summary": f"Comprehensive life story covering various aspects of {person_name}'s personal and professional journey.",
            "life_periods": [
                {"period": "Early Life and Formation", "timeframe": "Childhood and youth", "description": "Early experiences that shaped character", "key_themes": ["childhood", "family", "education"]},
                {"period": "Personal Development", "timeframe": "Young adulthood", "description": "Period of growth and self-discovery", "key_themes": ["growth", "relationships", "career_start"]},
                {"period": "Professional Journey", "timeframe": "Career development", "description": "Professional achievements and work experiences", "key_themes": ["career", "achievements", "challenges"]},
                {"period": "Personal Life and Relationships", "timeframe": "Throughout life", "description": "Important relationships and family life", "key_themes": ["family", "relationships", "personal_life"]},
                {"period": "Wisdom and Reflection", "timeframe": "Later life", "description": "Life lessons and personal philosophy", "key_themes": ["wisdom", "reflection", "legacy"]}
            ],
            "major_themes": [
                {"theme": "Personal Growth", "description": "Journey of self-discovery and development", "importance": "high", "examples": ["Learning experiences", "Character development"]},
                {"theme": "Relationships", "description": "Important connections with family and friends", "importance": "high", "examples": ["Family bonds", "Friendships"]},
                {"theme": "Professional Life", "description": "Career development and work experiences", "importance": "medium", "examples": ["Job experiences", "Career achievements"]},
                {"theme": "Life Philosophy", "description": "Personal values and beliefs", "importance": "medium", "examples": ["Core values", "Life principles"]}
            ],
            "suggested_chapters": [
                {"chapter_title": f"The Foundation Years: {person_name}'s Early Life", "chapter_focus": "Childhood, family background, and formative experiences", "themes_covered": ["childhood", "family"], "content_sources": "Early life discussions"},
                {"chapter_title": f"Building Character: {person_name}'s Personal Development", "chapter_focus": "Personal growth, education, and character formation", "themes_covered": ["growth", "education"], "content_sources": "Development and learning experiences"},
                {"chapter_title": f"Professional Journey: {person_name}'s Career Path", "chapter_focus": "Career development, work experiences, and professional achievements", "themes_covered": ["career", "achievements"], "content_sources": "Work and career discussions"},
                {"chapter_title": f"Personal Connections: {person_name}'s Relationships", "chapter_focus": "Important relationships, family life, and social connections", "themes_covered": ["relationships", "family"], "content_sources": "Relationship and family discussions"}
            ]
        } 