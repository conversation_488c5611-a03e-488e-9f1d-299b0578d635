from sqlalchemy.orm import Session
from typing import Optional, Dict
from app.models.agent_prompt import AgentPrompt
import logging

# Configure logging for prompt debugging
logging.basicConfig(level=logging.INFO)
prompt_logger = logging.getLogger("prompt_debug")

class PromptService:
    """Service for managing agent prompts from database."""
    
    def __init__(self, db: Session):
        self.db = db
        self._prompt_cache: Dict[str, str] = {}
    
    def get_prompt(self, agent_name: str) -> str:
        """Get prompt for specific agent from database with caching."""
        
        print(f"\n🔍 [PROMPT DEBUG] Requesting prompt for agent: '{agent_name}'")
        
        # Check cache first
        if agent_name in self._prompt_cache:
            cached_prompt = self._prompt_cache[agent_name]
            print(f"📋 [PROMPT DEBUG] Using cached prompt for '{agent_name}' (length: {len(cached_prompt)} chars)")
            print(f"📄 [PROMPT DEBUG] Cached prompt preview: {cached_prompt[:200]}...")
            return cached_prompt
        
        # Query database
        print(f"🔎 [PROMPT DEBUG] Querying database for active prompt: agent='{agent_name}'")
        prompt_record = self.db.query(AgentPrompt).filter(
            AgentPrompt.agent_name == agent_name,
            AgentPrompt.is_active == True
        ).first()
        
        if not prompt_record:
            # Return default fallback prompt
            fallback_prompt = self._get_fallback_prompt(agent_name)
            print(f"⚠️ [PROMPT DEBUG] No DB prompt found for '{agent_name}', using fallback")
            print(f"📄 [PROMPT DEBUG] Fallback prompt preview: {fallback_prompt[:200]}...")
            print(f"📏 [PROMPT DEBUG] Fallback prompt length: {len(fallback_prompt)} chars")
            return fallback_prompt
        
        # Log detailed information about the prompt
        print(f"✅ [PROMPT DEBUG] Found DB prompt for '{agent_name}':")
        print(f"   📊 ID: {prompt_record.id}")
        print(f"   📈 Version: {prompt_record.version}")
        print(f"   🟢 Active: {prompt_record.is_active}")
        print(f"   📏 Length: {len(prompt_record.prompt_content)} chars")
        print(f"   📅 Created: {getattr(prompt_record, 'created_at', 'N/A')}")
        print(f"   📄 Preview: {prompt_record.prompt_content[:300]}...")
        
        # Cache and return
        self._prompt_cache[agent_name] = prompt_record.prompt_content
        prompt_logger.info(f"Loaded prompt for agent '{agent_name}' - v{prompt_record.version}, {len(prompt_record.prompt_content)} chars")
        
        return prompt_record.prompt_content
    
    def _get_fallback_prompt(self, agent_name: str) -> str:
        """Get fallback prompt if none exists in database."""
        
        print(f"🔄 [PROMPT DEBUG] Generating fallback for agent '{agent_name}'")
        
        fallbacks = {
            "outline": """You are a biography outliner. Create a structured outline for a biography based on interview content.
            
Focus on:
- Identifying major life periods and themes
- Creating logical chapter structure
- Personalizing chapter titles based on specific details
- Organizing content in chronological order

Return a clear outline with chapter numbers and descriptive titles.""",
            
            "writer": """You are a biography writer. Write a comprehensive, engaging biography based on the interview and outline provided.
            
Style guidelines:
- Write in an engaging, narrative style similar to Walter Isaacson
- Include specific details and quotes from the interview
- Maintain chronological flow with smooth transitions
- Focus on the person's unique experiences and personality
- Avoid repetition and filler content

Create a rich, detailed biography that captures the person's life story.""",
            
            "evaluation": """You are a biography evaluator. Review the written biography and provide constructive feedback.
            
Evaluate:
- Writing style and readability
- Accuracy to source material
- Engagement and narrative flow
- Use of specific details and quotes
- Overall quality and completeness

Provide specific suggestions for improvement.""",
            
            "rewrite": """You are a biography editor. Improve the biography based on the evaluation feedback.
            
Focus on:
- Addressing specific issues mentioned in evaluation
- Improving narrative flow and readability
- Adding missing details from interview
- Enhancing writing style and engagement
- Maintaining accuracy to source material

Create an improved version of the biography."""
        }
        
        fallback = fallbacks.get(agent_name, "You are a helpful AI assistant for biography generation.")
        print(f"📝 [PROMPT DEBUG] Generated fallback prompt for '{agent_name}' ({len(fallback)} chars)")
        
        return fallback
    
    def clear_cache(self):
        """Clear the prompt cache to force fresh database queries."""
        cache_count = len(self._prompt_cache)
        self._prompt_cache.clear()
        print(f"🗑️ [PROMPT DEBUG] Cleared cache ({cache_count} prompts removed)")
        prompt_logger.info(f"Prompt cache cleared - {cache_count} cached prompts removed")
    
    def update_prompt(self, agent_name: str, new_content: str) -> bool:
        """Update prompt in database and refresh cache."""
        print(f"\n🔄 [PROMPT DEBUG] Updating prompt for agent '{agent_name}'")
        print(f"📏 [PROMPT DEBUG] New content length: {len(new_content)} chars")
        print(f"📄 [PROMPT DEBUG] New content preview: {new_content[:200]}...")
        
        try:
            # Find existing prompt
            existing = self.db.query(AgentPrompt).filter(
                AgentPrompt.agent_name == agent_name,
                AgentPrompt.is_active == True
            ).first()
            
            if existing:
                # Update existing prompt directly
                old_version = existing.version
                existing.prompt_content = new_content
                existing.version += 1
                print(f"✅ [PROMPT DEBUG] Updated existing prompt for '{agent_name}' (v{old_version} -> v{existing.version})")
            else:
                # Create new prompt
                new_prompt = AgentPrompt(
                    agent_name=agent_name,
                    prompt_content=new_content,
                    version=1,
                    is_active=True
                )
                self.db.add(new_prompt)
                print(f"✅ [PROMPT DEBUG] Created new prompt for '{agent_name}' (v1)")
            
            self.db.commit()
            
            # Update cache
            self._prompt_cache[agent_name] = new_content
            print(f"💾 [PROMPT DEBUG] Updated cache for '{agent_name}'")
            
            prompt_logger.info(f"Prompt updated for agent '{agent_name}' - {len(new_content)} chars")
            
            return True
            
        except Exception as e:
            print(f"❌ [PROMPT DEBUG] Failed to update prompt for '{agent_name}': {e}")
            prompt_logger.error(f"Failed to update prompt for agent '{agent_name}': {e}")
            self.db.rollback()
            return False
    
    def get_all_active_prompts(self) -> Dict[str, str]:
        """Get all active prompts for debugging purposes."""
        print(f"\n🔍 [PROMPT DEBUG] Fetching all active prompts from database")
        
        prompts = {}
        active_prompts = self.db.query(AgentPrompt).filter(AgentPrompt.is_active == True).all()
        
        print(f"📊 [PROMPT DEBUG] Found {len(active_prompts)} active prompts:")
        
        for prompt in active_prompts:
            prompts[prompt.agent_name] = prompt.prompt_content
            print(f"   🔸 {prompt.agent_name}: v{prompt.version}, {len(prompt.prompt_content)} chars")
        
        return prompts 