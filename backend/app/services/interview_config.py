"""
Interview Processing Configuration

This module contains configuration settings for processing interviews of different sizes
and optimizing the biography generation process.
"""

from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class InterviewProcessingConfig:
    """Configuration for interview processing based on size and complexity."""
    
    # Segmentation settings
    max_segment_size: int = 15000  # characters per segment
    segment_overlap: int = 500     # overlap between segments
    
    # Size thresholds
    large_file_page_threshold: int = 50
    large_file_char_threshold: int = 100000
    
    # Chapter recommendations
    min_chapters: int = 6
    max_chapters: int = 12
    pages_per_chapter: int = 35  # target pages per chapter
    
    # Iteration settings
    max_iterations_per_chapter: int = 10
    thesis_extraction_iterations: List[int] = None
    
    # AI model settings
    max_tokens_per_iteration: int = 4000
    delay_between_iterations: float = 0.5
    
    def __post_init__(self):
        if self.thesis_extraction_iterations is None:
            self.thesis_extraction_iterations = [3, 6, 10]

class InterviewSizeClassifier:
    """Classifies interviews by size and recommends processing strategies."""
    
    @staticmethod
    def classify_interview(word_count: int, page_count: int, char_count: int) -> Dict:
        """Classify interview and return processing recommendations."""
        
        if page_count > 100 or word_count > 30000:
            size_class = "very_large"
            recommended_chapters = min(max(word_count // 4000, 8), 12)
            processing_strategy = "segmented_iterative"
        elif page_count > 50 or word_count > 15000:
            size_class = "large"
            recommended_chapters = min(max(word_count // 3000, 6), 10)
            processing_strategy = "segmented_standard"
        elif page_count > 20 or word_count > 8000:
            size_class = "medium"
            recommended_chapters = min(max(word_count // 2000, 5), 8)
            processing_strategy = "standard"
        else:
            size_class = "small"
            recommended_chapters = min(max(word_count // 1500, 4), 6)
            processing_strategy = "standard"
        
        return {
            "size_class": size_class,
            "recommended_chapters": recommended_chapters,
            "processing_strategy": processing_strategy,
            "estimated_biography_pages": min(max(word_count // 250, 20), 500),
            "needs_segmentation": size_class in ["large", "very_large"],
            "recommended_iterations": 10 if size_class == "very_large" else 8
        }

class ChapterThemeGenerator:
    """Generates appropriate chapter themes based on interview content and size."""
    
    BIOGRAPHICAL_THEMES = [
        ("Early Life and Family Origins", ["family", "childhood", "parents", "siblings"], "early childhood"),
        ("Growing Up and Education", ["school", "education", "learning", "teachers"], "youth/education"),
        ("Coming of Age", ["teenage", "adolescence", "high school", "friends"], "teenage years"),
        ("Higher Education", ["college", "university", "studies", "graduation"], "college years"),
        ("Early Adulthood", ["first job", "independence", "early career"], "early adulthood"),
        ("Career Development", ["work", "career", "profession", "advancement"], "professional years"),
        ("Personal Relationships", ["dating", "romance", "marriage", "spouse"], "relationship years"),
        ("Family Life and Children", ["children", "parenting", "family", "home"], "family years"),
        ("Major Life Challenges", ["difficulties", "challenges", "struggles", "hardships"], "challenging periods"),
        ("Achievements and Milestones", ["success", "accomplishments", "achievements", "recognition"], "peak years"),
        ("Personal Interests and Hobbies", ["hobbies", "interests", "passions", "activities"], "personal fulfillment"),
        ("Travel and Adventures", ["travel", "journeys", "exploration", "experiences"], "adventure years"),
        ("Health and Wellness", ["health", "fitness", "wellbeing", "recovery"], "wellness journey"),
        ("Community and Service", ["volunteer", "community", "service", "giving back"], "service years"),
        ("Entrepreneurship and Business", ["business", "startup", "entrepreneur", "leadership"], "business years"),
        ("Creative Pursuits", ["art", "music", "writing", "creativity"], "creative years"),
        ("Spiritual Journey", ["faith", "spirituality", "beliefs", "meaning"], "spiritual development"),
        ("Later Life and Reflection", ["reflection", "wisdom", "later years", "maturity"], "mature years"),
        ("Legacy and Future", ["legacy", "future", "advice", "impact"], "legacy years")
    ]
    
    @staticmethod
    def generate_chapter_structure(
        recommended_chapters: int, 
        detected_themes: List[str] = None,
        interview_type: str = "general"
    ) -> List[Dict]:
        """Generate chapter structure based on detected themes and interview size."""
        
        chapters = []
        available_themes = ChapterThemeGenerator.BIOGRAPHICAL_THEMES.copy()
        
        # Prioritize themes detected in the interview
        if detected_themes:
            priority_themes = []
            for theme in available_themes:
                title, keywords, period = theme
                if any(keyword in detected_themes for keyword in keywords):
                    priority_themes.append(theme)
            
            # Add remaining themes
            for theme in available_themes:
                if theme not in priority_themes:
                    priority_themes.append(theme)
            
            available_themes = priority_themes
        
        # Select appropriate number of themes
        selected_themes = available_themes[:recommended_chapters]
        
        for i, (title, themes, period) in enumerate(selected_themes):
            chapters.append({
                "number": i + 1,
                "title": title,
                "description": f"Explores {title.lower()} and related experiences",
                "key_themes": themes,
                "estimated_sections": 3 + (i % 2),  # Vary between 3-4 sections
                "time_period": period
            })
        
        return chapters

class ProcessingOptimizer:
    """Optimizes processing parameters based on interview characteristics."""
    
    @staticmethod
    def optimize_for_interview(analysis: Dict, config: InterviewProcessingConfig) -> Dict:
        """Optimize processing parameters for specific interview."""
        
        optimization = {
            "segment_size": config.max_segment_size,
            "overlap": config.segment_overlap,
            "iterations": config.max_iterations_per_chapter,
            "max_tokens": config.max_tokens_per_iteration,
            "delay": config.delay_between_iterations
        }
        
        # Adjust for very large interviews
        if analysis.get("word_count", 0) > 40000:
            optimization["segment_size"] = 12000  # Smaller segments for better focus
            optimization["iterations"] = 12      # More iterations for refinement
            optimization["max_tokens"] = 3500    # Conservative token usage
            optimization["delay"] = 0.8          # Longer delays for stability
            
        # Adjust for medium interviews
        elif analysis.get("word_count", 0) > 20000:
            optimization["segment_size"] = 18000
            optimization["iterations"] = 10
            optimization["max_tokens"] = 4000
            optimization["delay"] = 0.5
            
        # Adjust for small interviews
        else:
            optimization["segment_size"] = analysis.get("total_length", 15000)
            optimization["iterations"] = 8
            optimization["max_tokens"] = 4500
            optimization["delay"] = 0.3
        
        return optimization

# Default configuration instance
DEFAULT_CONFIG = InterviewProcessingConfig() 