#!/usr/bin/env python3
"""
Enhanced Biography Service with intelligent processing strategy selection.
Uses progressive generation for large files, standard generation for smaller ones.
"""

import asyncio
import json
import time
from datetime import datetime
from sqlalchemy.orm import Session
from typing import Dict

from app.models.biography_job import Biography<PERSON><PERSON>, JobStatus
from app.models.agent_prompt import AgentPrompt
from app.services.ai_service import AIService
from app.services.pdf_service import PDFService
from app.services.websocket_manager import manager
from app.services.iterative_biography_service import IterativeBiographyService
from app.services.progressive_biography_service import ProgressiveBiographyService
from app.services.enhanced_progressive_service import EnhancedProgressiveBiographyService
from app.services.enhanced_iterative_service import EnhancedIterativeService
from app.services.gemini_optimized_service import GeminiOptimizedService
from app.schemas.biography_job import BiographyJobUpdate
from typing import Optional
import os

class BiographyService:
    """Enhanced biography service with intelligent processing strategy."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService(db)
        self.iterative_service = IterativeBiographyService(db)
        self.progressive_service = ProgressiveBiographyService(db)
        self.enhanced_iterative_service = EnhancedIterativeService(db)
        
        # Initialize Gemini service if available
        try:
            from app.core.config import settings
            if settings.gemini_api_key:
                self.gemini_service = GeminiOptimizedService(db)
            else:
                self.gemini_service = None
        except Exception as e:
            print(f"⚠️ Gemini service not available: {e}")
            self.gemini_service = None
        
        # Thresholds for processing strategy selection (updated for Enhanced Iterative)
        self.LARGE_FILE_THRESHOLD = 150000  # 150k characters
        self.VERY_LARGE_FILE_THRESHOLD = 300000  # 300k characters
        self.ENHANCED_ITERATIVE_THRESHOLD = 30000  # 30k characters - use Enhanced Iterative
        self.GEMINI_THRESHOLD = 500000  # 500k characters - use Gemini for very large files
        self.GEMINI_2M_THRESHOLD = 1000000  # 1M characters - use Gemini 2.5 Pro's 2M token context
        self.MAX_PAGES_STANDARD = 80  # Max pages for standard processing
    
    async def process_biography(self, job_id: str) -> bool:
        """Process biography with intelligent strategy selection."""
        print(f"🎯 Starting intelligent biography processing for job {job_id}")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        start_time = time.time()
        
        try:
            # Step 1: Analyze file to determine processing strategy
            processing_strategy = await self._determine_processing_strategy(job)
            
            # Step 2: Process based on strategy
            if processing_strategy == "gemini":
                print("🧠 Using GEMINI OPTIMIZED processing for very large file")
                success = await self.gemini_service.process_gemini_optimized_biography(job_id)
            elif processing_strategy == "progressive":
                print("📊 Using PROGRESSIVE processing for large/complex file")
                success = await self.progressive_service.process_progressive_biography(job_id)
            elif processing_strategy == "enhanced_iterative":
                print("🚀 Using ENHANCED ITERATIVE processing for medium/large file")
                success = await self.enhanced_iterative_service.process_enhanced_iterative_job(job_id)
            else:
                print("⚡ Using STANDARD processing for normal file")
                success = await self.iterative_service.process_iterative_biography_job(job_id)
            
            if success:
                processing_time = time.time() - start_time
                print(f"✅ Biography processing completed in {processing_time:.2f}s using {processing_strategy} strategy")
                
                # Update job with strategy info
                metadata = {
                    "processing_strategy": processing_strategy,
                    "total_processing_time": processing_time,
                    "strategy_selection_reason": getattr(job, '_strategy_reason', 'Standard selection')
                }
                
                if job.evaluation_content:
                    existing_metadata = json.loads(job.evaluation_content)
                    existing_metadata.update(metadata)
                    job.evaluation_content = json.dumps(existing_metadata)
                else:
                    job.evaluation_content = json.dumps(metadata)
                
                self.db.commit()
                
            return success
            
        except Exception as e:
            job.error_message = str(e)
            job.processing_time_seconds = time.time() - start_time
            await self._update_job(job, JobStatus.FAILED, job.progress_percentage)
            print(f"❌ Biography processing failed: {e}")
            return False
    
    async def _determine_processing_strategy(self, job: BiographyJob) -> str:
        """Determine optimal processing strategy based on file analysis."""
        print("🔍 Analyzing file to determine processing strategy...")
        
        try:
            # Extract and analyze interview text
            interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
            if not interview_text.strip():
                raise Exception("Could not extract text from uploaded PDF")
            
            # Analyze file characteristics
            text_length = len(interview_text)
            word_count = len(interview_text.split())
            line_count = len(interview_text.split('\n'))
            
            # Count sessions/structure markers
            session_markers = self._count_structure_markers(interview_text)
            
            # Estimate PDF pages (rough calculation)
            estimated_pages = max(text_length // 2000, word_count // 250)  # Conservative estimation
            
            print(f"📏 File analysis:")
            print(f"   • Characters: {text_length:,}")
            print(f"   • Words: {word_count:,}")
            print(f"   • Lines: {line_count:,}")
            print(f"   • Session markers: {session_markers}")
            print(f"   • Estimated pages: {estimated_pages}")
            
            # Log available Gemini capabilities if service is available
            if self.gemini_service:
                selected_model = self.gemini_service._get_selected_gemini_model()
                context_limit = self.gemini_service._get_model_context_limit()
                print(f"   • Gemini model: {selected_model} ({context_limit:,} tokens context)")
            else:
                print(f"   • Gemini service: Not available")
            
            # Strategy selection logic
            strategy_reason = ""
            
            # Ultra-large files use Gemini 2.5 Pro's 2M context if available
            if text_length > self.GEMINI_2M_THRESHOLD and self.gemini_service:
                # Check if we have Gemini 2.5 Pro for 2M context
                selected_model = self.gemini_service._get_selected_gemini_model()
                if selected_model == "gemini-2.5-pro":
                    strategy_reason = f"Ultra-large file ({text_length:,} chars > {self.GEMINI_2M_THRESHOLD:,}) - using Gemini 2.5 Pro 2M token context"
                else:
                    strategy_reason = f"Ultra-large file ({text_length:,} chars > {self.GEMINI_2M_THRESHOLD:,}) - using Gemini 1M token context"
                job._strategy_reason = strategy_reason
                return "gemini"
            
            # Very large files use Gemini if available  
            elif text_length > self.GEMINI_THRESHOLD and self.gemini_service:
                strategy_reason = f"Very large file ({text_length:,} chars > {self.GEMINI_THRESHOLD:,}) - using Gemini large context"
                job._strategy_reason = strategy_reason
                return "gemini"
            
            # Very large files use progressive
            elif text_length > self.VERY_LARGE_FILE_THRESHOLD:
                strategy_reason = f"Very large file ({text_length:,} chars > {self.VERY_LARGE_FILE_THRESHOLD:,})"
                job._strategy_reason = strategy_reason
                return "progressive"
            
            # Large files with many sessions use progressive
            elif text_length > self.LARGE_FILE_THRESHOLD and session_markers > 15:
                strategy_reason = f"Large complex file ({text_length:,} chars, {session_markers} sessions)"
                job._strategy_reason = strategy_reason
                return "progressive"
            
            # Very long documents (estimated pages)
            elif estimated_pages > self.MAX_PAGES_STANDARD:
                strategy_reason = f"Long document (~{estimated_pages} pages > {self.MAX_PAGES_STANDARD} threshold)"
                job._strategy_reason = strategy_reason
                return "progressive"
            
            # High complexity indicators
            elif session_markers > 20:
                strategy_reason = f"High complexity ({session_markers} session markers)"
                job._strategy_reason = strategy_reason
                return "progressive"
            
            # Enhanced Iterative for medium-sized files
            elif (text_length > self.ENHANCED_ITERATIVE_THRESHOLD or 
                  word_count > 5000 or 
                  estimated_pages > 20):
                strategy_reason = f"Medium file ({text_length:,} chars, {word_count:,} words, ~{estimated_pages} pages) - using Enhanced Iterative"
                job._strategy_reason = strategy_reason
                return "enhanced_iterative"
            
            # Standard processing for normal files
            else:
                strategy_reason = f"Standard file ({text_length:,} chars, ~{estimated_pages} pages, {session_markers} sessions)"
                job._strategy_reason = strategy_reason
                return "standard"
                
        except Exception as e:
            print(f"⚠️ Error analyzing file, defaulting to standard: {e}")
            job._strategy_reason = f"Analysis failed: {e}"
            return "standard"
    
    def _count_structure_markers(self, text: str) -> int:
        """Count structure markers that indicate complex multi-session interviews."""
        markers = [
            'session', 'ai:', 'user:', 'eternal ai', 'welcome back',
            '--- page', 'chapter', 'part', 'interview',
            'transcript', 'conversation', 'dialogue'
        ]
        
        text_lower = text.lower()
        total_markers = 0
        
        for marker in markers:
            total_markers += text_lower.count(marker)
        
        return total_markers
    
    async def resume_biography(self, job_id: str) -> bool:
        """Resume biography processing from where it left off."""
        print(f"🔄 Resuming biography processing for job {job_id}")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        # Check processing strategy from metadata
        strategy = "standard"  # default
        if job.evaluation_content:
            try:
                metadata = json.loads(job.evaluation_content)
                strategy = metadata.get('processing_strategy', 'standard')
            except:
                pass
        
        # Resume based on strategy
        if strategy == "gemini" and self.gemini_service:
            print("🧠 Resuming with GEMINI OPTIMIZED service")
            return await self.gemini_service.process_gemini_optimized_biography(job_id)
        elif strategy == "progressive" or job.chapters_content or (job.evaluation_content and 'progressive' in job.evaluation_content):
            print("📊 Resuming with PROGRESSIVE service")
            return await self.progressive_service.resume_progressive_biography(job_id)
        else:
            print("⚡ Resuming with STANDARD service")
            return await self.iterative_service.process_biography(job_id)
    
    async def get_job_status(self, job_id: str) -> dict:
        """Get enhanced job status with strategy information."""
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            return {"error": "Job not found"}
        
        # Parse metadata for strategy info
        strategy_info = {}
        if job.evaluation_content:
            try:
                metadata = json.loads(job.evaluation_content)
                strategy_info = {
                    "processing_strategy": metadata.get("processing_strategy", "unknown"),
                    "strategy_reason": metadata.get("strategy_selection_reason", "Not specified"),
                    "total_processing_time": metadata.get("total_processing_time", 0)
                }
            except:
                pass
        
        # Enhanced status with buffered chapter info
        status = {
            "id": job.id,
            "status": job.status.value,
            "progress_percentage": job.progress_percentage,
            "total_chapters": job.total_chapters,
            "completed_chapters": job.completed_chapters,
            "created_at": job.created_at,
            "updated_at": job.updated_at,
            "completed_at": job.completed_at,
            "error_message": job.error_message,
            "processing_time_seconds": job.processing_time_seconds,
            **strategy_info
        }
        
        # Add buffered chapters info if available
        if job.chapters_content:
            chapter_stats = {}
            for key, chapter in job.chapters_content.items():
                chapter_stats[key] = {
                    "title": chapter.get("title", "Unknown"),
                    "word_count": chapter.get("word_count", 0),
                    "generated_at": chapter.get("generated_at", "Unknown"),
                    "status": "completed"
                }
            status["buffered_chapters"] = chapter_stats
            status["buffered_chapters_count"] = len(chapter_stats)
        
        return status
    
    async def _update_job(self, job: BiographyJob, status: JobStatus, progress: float):
        """Update job status and progress."""
        job.status = status
        job.progress_percentage = progress
        job.updated_at = datetime.utcnow()
        self.db.commit()
        
        # Send WebSocket update
        try:
            await manager.broadcast_job_update(job.id)
        except Exception as e:
            print(f"⚠️ WebSocket update failed: {e}")
    
    # Legacy compatibility methods for existing API endpoints
    async def process_iterative_biography(self, job_id: str) -> bool:
        """Legacy method - now uses intelligent processing."""
        return await self.process_biography(job_id)
    
    def get_biography_statistics(self, job_id: str) -> dict:
        """Get detailed statistics about biography generation."""
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            return {"error": "Job not found"}
        
        stats = {
            "job_id": job.id,
            "status": job.status.value,
            "total_chapters": job.total_chapters,
            "completed_chapters": job.completed_chapters,
            "processing_time": job.processing_time_seconds
        }
        
        # Add chapter-level statistics if available
        if job.chapters_content:
            chapter_stats = []
            total_words = 0
            
            for key, chapter in job.chapters_content.items():
                word_count = chapter.get("word_count", 0)
                total_words += word_count
                
                chapter_stats.append({
                    "number": chapter.get("number", 0),
                    "title": chapter.get("title", "Unknown"),
                    "word_count": word_count,
                    "character_count": len(chapter.get("content", "")),
                    "generated_at": chapter.get("generated_at", "Unknown")
                })
            
            stats["chapter_statistics"] = chapter_stats
            stats["total_words"] = total_words
            stats["average_words_per_chapter"] = total_words // len(chapter_stats) if chapter_stats else 0
        
        # Add file analysis stats if available
        if job.evaluation_content:
            try:
                metadata = json.loads(job.evaluation_content)
                analysis = metadata.get("analysis", {})
                if analysis:
                    stats["file_analysis"] = {
                        "original_length": analysis.get("total_length", 0),
                        "word_count": analysis.get("word_count", 0),
                        "segments_created": len(analysis.get("segments", [])),
                        "session_markers": len(analysis.get("session_markers", [])),
                        "needs_segmentation": analysis.get("needs_segmentation", False)
                    }
            except:
                pass
        
        return stats

    def _determine_optimal_strategy(self, analysis: Dict) -> str:
        """Determine optimal processing strategy based on content analysis."""
        file_size = analysis.get('file_size', 0)
        content_length = analysis.get('content_length', 0)
        word_count = analysis.get('word_count', 0)
        page_count = analysis.get('page_count', 0)
        
        # Enhanced thresholds for strategy selection
        if (file_size > 500_000 or  # 500KB+ files
            content_length > 300_000 or  # 300K+ characters  
            word_count > 50_000 or  # 50K+ words
            page_count > 100):  # 100+ pages
            print(f"📈 ENHANCED PROGRESSIVE: Very large file detected ({file_size:,} bytes, {word_count:,} words, {page_count} pages)")
            return "enhanced_progressive"
        elif (file_size > 200_000 or  # 200KB+ files
              content_length > 150_000 or  # 150K+ characters
              word_count > 20_000 or  # 20K+ words  
              page_count > 50):  # 50+ pages
            print(f"📊 PROGRESSIVE: Medium-large file detected ({file_size:,} bytes, {word_count:,} words, {page_count} pages)")
            return "progressive"
        elif (file_size > 50_000 or  # 50KB+ files
              content_length > 30_000 or  # 30K+ characters
              word_count > 5_000 or  # 5K+ words
              page_count > 20):  # 20+ pages  
            print(f"🧠 ENHANCED ITERATIVE: Medium file detected - using context-aware iterative generation ({file_size:,} bytes, {word_count:,} words, {page_count} pages)")
            return "enhanced_iterative"
        else:
            print(f"⚡ STANDARD: Normal file size ({file_size:,} bytes, {word_count:,} words, {page_count} pages)")
            return "standard"

    async def process_biography_intelligently(self, job_id: str) -> bool:
        """Process biography using intelligent strategy selection."""
        print(f"🎯 Starting intelligent biography processing for job {job_id}")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        # Quick analysis for strategy determination
        analysis = self._analyze_uploaded_content(job.uploaded_pdf_path)
        strategy = self._determine_optimal_strategy(analysis)
        
        # Store strategy in job for tracking
        if job.evaluation_content:
            metadata = json.loads(job.evaluation_content)
        else:
            metadata = {}
        metadata['processing_strategy'] = strategy
        metadata['analysis'] = analysis
        job.evaluation_content = json.dumps(metadata)
        self.db.commit()
        
        # Route to appropriate service based on strategy
        if strategy == "enhanced_progressive":
            print("🚀 Routing to Enhanced Progressive Service for maximum content generation")
            enhanced_service = EnhancedProgressiveBiographyService(self.db)
            return await enhanced_service.process_enhanced_biography(job_id)
        elif strategy == "progressive":
            print("📊 Routing to Progressive Service for buffered processing")
            progressive_service = ProgressiveBiographyService(self.db)
            return await progressive_service.process_progressive_biography(job_id)
        elif strategy == "enhanced_iterative":
            print("🧠 Routing to Enhanced Iterative Service for context-aware book-style generation")
            return await self.enhanced_iterative_service.process_enhanced_iterative_job(job_id)
        else:
            print("⚡ Using Standard Service for fast processing")
            return await self.iterative_service.process_iterative_biography_job(job_id)

    def _get_agent_prompts(self) -> dict:
        """Get current active prompts for all agents."""
        prompts = {}
        
        # Default prompts (from your provided prompts)
        default_prompts = {
            "outline": """You are the "Biography Outliner," a specialized AI whose task is to review a complete interview transcript between the user and our AI Historian. From this transcript, your job is to create a structured outline for a written biography of the user's life.

=========== BACKGROUND CONTEXT ===========
The AI Historian typically interviews users across 12 core chapters:
1) Early Life & Childhood
2) Adolescence & Teenage Years
3) Early Adulthood (College / Career Start)
4) Mid-Life Experiences (Career, Family, Relationships)
5) Major Life Events & Reflections
6) Values, Beliefs, and Worldview
7) Personal Passions, Hobbies, and Interests
8) Reflections on Relationships & Community
9) Life Lessons & Legacy
10) Future Outlook & Aspirations
11) Wrap-Up & Validation
12) Freestyle (Open-Ended)

However, the user may or may not have covered all these chapters in depth. Your task is to adapt to whatever content they actually provided.

=========== OBJECTIVE ===========
1. Read through the entire transcript, focusing on the user's experiences and the chapters/topics they actually covered.
2. Produce a high-level outline (chapters and chapter names) for a written biography that reflects the user's unique life story.
3. Personalize the chapter titles based on specific details from the user's transcript (e.g., "Chapter 1: Growing Up in Texas").
4. Omit or combine chapters that were not discussed or had minimal content.

=========== PROMPT REQUIREMENTS ===========
• Read the user's interview transcript in full.
• Consult the generic structure above as a reference, but only include a chapter if the user has enough content to justify it.
• For each segment the user meaningfully covered, propose a **custom chapter** that reflects the essence, location, or unique experiences discussed.
• If the user spent considerable time on a topic not in the standard interview flow, feel free to create a separate chapter for it.
• Provide each chapter as follows:
  – A **chapter number** (e.g., "Chapter 1," "Chapter 2," etc.).
  – A **descriptive, user-specific title** reflecting key themes or places from the user's story.
  – (Optionally) a **one- to two-sentence summary** or bullet points indicating what that chapter covers.

=========== STYLE AND FORMAT ===========
• Present a concise, user-friendly list of chapters.
• Chapter titles should not be generic if the user offered specific details to personalize them.
• Do not provide the full biography—just the **outline** of chapters.

=========== SAMPLE GUIDANCE ===========
– If the user spoke a lot about early family life in a small seaside town, consider: "Chapter 1: Childhood by the Coast" instead of something generic.
– If they skipped the teenage years or only mentioned them briefly, either omit or combine that content as you see fit.
– If there's a topic that doesn't neatly match a standard chapter but was significant (e.g., a mid-life entrepreneurial venture), create a separate chapter for it.

=========== FINAL INSTRUCTIONS ===========
• After reviewing the transcript, output only the **proposed biography outline** with personalized chapter titles.
• Keep your outline well organized and accurate to the content actually discussed.""",
            
            "writer": """You are the "Full Biography Writer," tasked with composing a detailed, in-depth biography of a user based on the following that you will be provided with:
1) The complete interview transcript between the user and our AI Historian.
2) The personalized biography outline created by the Biography Outliner.

=========== CONTEXT ON ETERNAL AI ===========
• Eternal AI is a technology company dedicated to capturing and preserving human life stories for future generations. Our "Capsule" concept ensures that these stories, experiences, and personalities can live on in various forms—print biographies, podcasts, digital avatars, and more.

=========== GOALS ===========
1. Write a comprehensive, captivating biography of the user's life, covering all the chapters from the provided outline in a logical, narrative flow.
2. Emulate the engaging, in-depth style of a Walter Isaacson biography—meaning a blend of personal detail, historical or contextual notes, thoughtful analysis, and a lively narrative voice.
3. Infuse the story with detail: don't skim. Retell specific stories and key anecdotes the user shared during the interview. Include direct quotes from the user where relevant, so it reads authentically and with personality.
4. Ensure the final biography remains interesting and never bland. Keep the user's unique experiences front-and-center. If the user has humorous or poignant moments, reflect those in the writing.
5. Don't make the biography too fluffy or lacking substance. Your writing should be filled with meaningful content, not filler.
6. Avoid repetition. Do not restate the same themes or anecdotes unnecessarily. Write it as a flowing narrative without looping back on previously covered material.

=========== PROMPT REQUIREMENTS ===========
• Use the biography outline as your structural guide. Each chapter or section in your final text should correspond to the outline's chapters, though you may rearrange slightly if it serves the narrative flow.
• Integrate details from the interview transcript, weaving in quotes and anecdotes that the user provided.
• Maintain a comprehensive but not repetitive style. You don't need to restate entire blocks of text from the transcript; aim for a narrative retelling that includes the essential facts, emotions, and revelations.
• The tone should be reminiscent of a Walter Isaacson biography: respectful, curious, rich with context, and told in an engaging storytelling voice.
• Do not simply bullet out the user's life; write a cohesive story, with paragraphs and smooth transitions between chapters.
• Feel free to add short transitional or contextual notes if needed, but stay true to the user's experiences.
• Ensure that the written content is TRUE and GROUNDED to the transcript. Do not make things up.

=========== FORMAT SUGGESTION ===========
• Title Page (optional)
• Introduction (optional prologue if you think it improves the flow)
• Chapters in sequential order, following the outline's structure:
   – Chapter # / Title
   – Narrative text with quotes, details, etc.
• Conclusion or final reflection (optional), summarizing the user's overall journey or key takeaways
• Refer to the user's full name in the introduction, but after that refer to them simply by their first name only or last name only
• Don't be repetitive with unique words, for example, "palette" or "tapestry". You can use rich words like that occasionally, but don't repeat them throughout the biography.


=========== ADDITIONAL TIPS ===========
• It's acceptable to incorporate brief background info if the user described historical events or cultural references—but focus on the user's perspective.
• When quoting, attribute it clearly to the user, like:  
  "I always found solace in painting," the user recalled.
• Keep the user's voice and experiences at the heart of the biography. The user is the protagonist, so the narrative revolves around their journey, influences, and transformations.

=========== FINAL INSTRUCTIONS ===========
• Produce the final biography text as a cohesive narrative. 
• Reference the user's direct words by inserting short quotes (and small paraphrases) where beneficial. 
• Write for clarity, depth, and reader engagement—this biography should be a treasured keepsake that readers can enjoy and learn from.""",
            
            "evaluation": """You are the "Evaluation Agent," tasked with critically reviewing the biography written by the Biography Writer to ensure it meets high editorial standards before being passed to the Rewrite Agent for final polishing.

=========== BACKGROUND CONTEXT ===========
The Biography Writer just completed a written biography based on:
1) The original interview transcript between the user and our AI Historian.
2) A personalized outline created by the Biography Outliner.

Your job is to evaluate this biography for quality, completeness, and adherence to the user's authentic story.

=========== EVALUATION CRITERIA ===========
You should assess the biography on the following dimensions:

**1. COMPREHENSIVENESS**
• Does the biography cover all major life themes and stories mentioned in the interview transcript?
• Are there important experiences from the transcript that were omitted or under-developed?
• Does it feel complete, or are there obvious gaps?

**2. AUTHENTICITY**
• Does the biography stay true to the user's voice and personality as reflected in the transcript?
• Are quotes and anecdotes accurately represented?
• Does it feel like the user's authentic story, or does it sound generic/fabricated?

**3. NARRATIVE FLOW**
• Does the biography read as a cohesive, engaging story?
• Are there smooth transitions between chapters and topics?
• Is the chronological/thematic organization logical and easy to follow?

**4. DEPTH AND DETAIL**
• Does the biography provide sufficient detail and context to be interesting and meaningful?
• Are the stories and experiences fleshed out appropriately?
• Does it avoid being too surface-level or overly brief?

**5. REPETITION AND REDUNDANCY**
• Are there sections where the same information is repeated unnecessarily?
• Does the writing avoid circular or redundant storytelling?

**6. ENGAGEMENT AND READABILITY**
• Is the biography engaging to read?
• Does it maintain reader interest throughout?
• Is the writing style appropriate for a published biography?

=========== EVALUATION OUTPUT ===========
Provide a structured evaluation that includes:

**STRENGTHS:**
• List 3-5 specific things the biography does well

**AREAS FOR IMPROVEMENT:**
• Identify 3-5 specific issues that need to be addressed
• Be specific about what's missing, what's repetitive, or what could be enhanced

**COMPLETENESS CHECK:**
• Compare against the original transcript to identify any major missing elements

**RECOMMENDATIONS FOR REWRITE:**
• Provide specific, actionable suggestions for the Rewrite Agent
• Priority order your recommendations (most important first)

**OVERALL ASSESSMENT:**
• Rate the biography on a scale of 1-10 for overall quality
• Provide a brief summary of whether it's ready for publication or needs significant revision

=========== EVALUATION GUIDELINES ===========
• Be thorough but constructive in your critique
• Focus on substantive issues rather than minor stylistic preferences
• Remember that the goal is to create a meaningful, authentic life story
• Ensure your feedback will help the Rewrite Agent improve the final product""",
            
            "rewrite": """You are the "Rewrite Agent," the final step in our biography creation process. Your task is to take the biography written by the Biography Writer, along with the detailed evaluation and recommendations from the Evaluation Agent, and produce a polished, publication-ready final version.

=========== YOUR ROLE ===========
You are essentially the "editor" who takes a good draft and makes it excellent. You have access to:
1) The original interview transcript
2) The biography outline
3) The current biography draft
4) Detailed evaluation feedback with specific recommendations

Your job is to address the evaluation feedback while maintaining the authentic voice and story of the user.

=========== REWRITE PRIORITIES ===========
Based on the evaluation feedback, focus on these areas in order of importance:

**1. ADDRESS MAJOR GAPS**
• Add any important life experiences or stories that were omitted
• Ensure completeness based on what was discussed in the interview

**2. ELIMINATE REPETITION**
• Remove redundant sections or repetitive storytelling
• Streamline narrative flow without losing important details

**3. ENHANCE AUTHENTICITY**
• Ensure quotes and anecdotes accurately reflect the user's voice
• Maintain the user's personality and unique perspective throughout

**4. IMPROVE NARRATIVE FLOW**
• Smooth transitions between chapters and topics
• Ensure logical progression and readability

**5. ADD DEPTH WHERE NEEDED**
• Flesh out important stories that may have been under-developed
• Provide appropriate context and detail

**6. POLISH FOR PUBLICATION**
• Ensure professional writing quality
• Make it engaging and compelling to read

=========== REWRITE GUIDELINES ===========
• **Stay True to the Source:** Everything in your rewrite should be grounded in the original interview transcript
• **Maintain Authenticity:** Preserve the user's voice, personality, and unique perspective
• **Address All Feedback:** Systematically work through the evaluation recommendations
• **Enhance, Don't Recreate:** Build upon the existing biography rather than starting over
• **Focus on Quality:** This is the final version, so aim for publication-quality writing

=========== REWRITE PROCESS ===========
1. Carefully review the evaluation feedback and prioritize the recommendations
2. Identify specific sections that need revision, addition, or removal
3. Make targeted improvements while maintaining the overall structure and flow
4. Ensure the final biography feels complete, authentic, and engaging
5. Perform a final quality check to ensure publication readiness

=========== OUTPUT REQUIREMENTS ===========
• Provide the complete rewritten biography
• Ensure it addresses the major points raised in the evaluation
• Maintain the chapter structure from the original outline
• Deliver a polished, engaging, and authentic final life story

Remember: This is the user's life story that may be shared with family, friends, and future generations. Make it something they would be proud to have represent their legacy."""
        }
        
        agent_names = ["outline", "writer", "evaluation", "rewrite"]
        for agent_name in agent_names:
            prompt_record = (
                self.db.query(AgentPrompt)
                .filter(AgentPrompt.agent_name == agent_name, AgentPrompt.is_active == True)
                .first()
            )
            
            if prompt_record:
                prompts[agent_name] = prompt_record.prompt_content
            else:
                prompts[agent_name] = default_prompts[agent_name]
        
        return prompts 

    async def create_biography_job(self, job_data, uploaded_pdf_path: str = None) -> BiographyJob:
        """Create a new biography job from job data."""
        new_job = BiographyJob(
            user_name=job_data.user_name,
            email=getattr(job_data, 'email', None),
            uploaded_pdf_path=uploaded_pdf_path or "",  # Ensure it's not None
            status=JobStatus.PENDING,
            progress_percentage=0.0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(new_job)
        self.db.commit()
        self.db.refresh(new_job)
        
        return new_job

    def _analyze_uploaded_content(self, pdf_path: str) -> Dict:
        """Analyze uploaded PDF content for strategy determination."""
        try:
            # Extract basic file information
            file_size = os.path.getsize(pdf_path) if os.path.exists(pdf_path) else 0
            
            # Extract text content
            content = PDFService.extract_text_from_pdf(pdf_path)
            content_length = len(content)
            word_count = len(content.split()) if content else 0
            
            # Estimate page count (rough estimation)
            page_count = max(content_length // 2000, word_count // 250) if content else 0
            
            return {
                'file_size': file_size,
                'content_length': content_length,
                'word_count': word_count,
                'page_count': page_count,
                'has_content': bool(content.strip()) if content else False
            }
            
        except Exception as e:
            print(f"⚠️ Error analyzing content: {e}")
            return {
                'file_size': 0,
                'content_length': 0,
                'word_count': 0,
                'page_count': 0,
                'has_content': False,
                'error': str(e)
            } 