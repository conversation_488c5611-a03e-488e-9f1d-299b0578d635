import asyncio
import json
import logging
from typing import Dict, Set
from datetime import datetime
from fastapi import WebSocket
from sqlalchemy.orm import Session
from app.models.biography_job import <PERSON><PERSON>ob
from app.models.websocket_notification import WebSocketNotification
from app.schemas.biography_job import BiographyJobResponse
from app.models.base import SessionLocal

# Configure logger with more detailed formatting
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def datetime_serializer(obj):
    """JSON serializer for datetime objects."""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

class ConnectionManager:
    def __init__(self):
        # Dictionary to store job_id -> set of WebSocket connections
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self._lock = asyncio.Lock()
        logger.info("🏗️ WebSocket ConnectionManager initialized")

    async def connect(self, websocket: WebSocket, job_id: str):
        """Accept a new WebSocket connection for a specific job."""
        logger.info(f"🔌 Attempting to connect WebSocket for job {job_id[:8]}...")
        
        await websocket.accept()
        logger.info(f"✅ WebSocket accepted for job {job_id[:8]}")
        
        async with self._lock:
            if job_id not in self.active_connections:
                self.active_connections[job_id] = set()
                logger.info(f"📝 Created new connection set for job {job_id[:8]}")
            
            self.active_connections[job_id].add(websocket)
            connection_count = len(self.active_connections[job_id])
            total_connections = self.get_total_connections()
        
        logger.info(f"🟢 WebSocket connected for job {job_id[:8]}. Job connections: {connection_count}, Total: {total_connections}")
        logger.info(f"🌐 WebSocket connection details: client_state={websocket.client_state.name}, app_state={websocket.application_state.name}")

    async def disconnect(self, websocket: WebSocket, job_id: str):
        """Remove a WebSocket connection."""
        logger.info(f"🔌 Disconnecting WebSocket for job {job_id[:8]}...")
        
        async with self._lock:
            if job_id in self.active_connections:
                self.active_connections[job_id].discard(websocket)
                remaining_connections = len(self.active_connections[job_id])
                
                if not self.active_connections[job_id]:
                    del self.active_connections[job_id]
                    logger.info(f"🗑️ Removed empty connection set for job {job_id[:8]}")
                else:
                    logger.info(f"📉 Job {job_id[:8]} still has {remaining_connections} connections")
        
        total_connections = self.get_total_connections()
        logger.info(f"🔴 WebSocket disconnected for job {job_id[:8]}. Total connections: {total_connections}")

    async def send_job_update(self, job_id: str, job_data: dict):
        """Send job update to all connected clients for this job."""
        short_job_id = job_id[:8]
        logger.info(f"📡 Starting send_job_update for job {short_job_id}")
        
        if job_id not in self.active_connections:
            logger.info(f"📭 No connections for job {short_job_id}, skipping send")
            return

        connections_to_remove = set()
        connection_count = len(self.active_connections[job_id])
        
        logger.info(f"📤 Sending update to {connection_count} connections for job {short_job_id}")
        logger.info(f"📊 Update data: status={job_data.get('status')}, progress={job_data.get('progress_percentage')}%")
        
        # Prepare message once
        try:
            message = json.dumps(job_data, default=datetime_serializer)
            message_size = len(message)
            logger.info(f"📝 Prepared message for job {short_job_id}: {message_size} characters")
        except Exception as e:
            logger.error(f"❌ Failed to serialize job data for {short_job_id}: {e}")
            return
        
        for i, websocket in enumerate(self.active_connections[job_id].copy()):
            connection_num = i + 1
            logger.info(f"🔍 Checking connection {connection_num}/{connection_count} for job {short_job_id}")
            
            try:
                # Detailed connection state check
                client_state = websocket.client_state.name
                app_state = websocket.application_state.name
                logger.info(f"🔗 Connection {connection_num}: client_state={client_state}, app_state={app_state}")
                
                if client_state != 'CONNECTED' or app_state != 'CONNECTED':
                    logger.warning(f"🚫 Connection {connection_num} for job {short_job_id} is not active, marking for removal")
                    connections_to_remove.add(websocket)
                    continue

                logger.info(f"📨 Sending to connection {connection_num}/{connection_count} for job {short_job_id}")
                await websocket.send_text(message)
                logger.info(f"✅ Successfully sent to connection {connection_num} for job {short_job_id}")
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"❌ Failed to send to connection {connection_num} for job {short_job_id}: {error_msg}")
                
                # Categorize error types
                expected_errors = [
                    "1005", "no status received", "no close frame", 
                    "websocket is not connected", "connection closed",
                    "broken pipe", "connection reset"
                ]
                
                if any(phrase in error_msg.lower() for phrase in expected_errors):
                    logger.info(f"🔌 Expected disconnection error for connection {connection_num}: {error_msg}")
                else:
                    logger.warning(f"⚠️ Unexpected error for connection {connection_num}: {error_msg}")
                
                connections_to_remove.add(websocket)

        # Remove failed connections
        if connections_to_remove:
            logger.info(f"🧹 Removing {len(connections_to_remove)} failed connections for job {short_job_id}")
            async with self._lock:
                for websocket in connections_to_remove:
                    self.active_connections[job_id].discard(websocket)
                    try:
                        # Only try to close if still in a closeable state
                        if (websocket.client_state.name == 'CONNECTED' and 
                            websocket.application_state.name == 'CONNECTED'):
                            await websocket.close()
                            logger.info(f"🔒 Closed failed WebSocket connection")
                    except Exception as e:
                        logger.debug(f"🔒 Error closing websocket: {e}")
                        
                remaining_connections = len(self.active_connections[job_id])
                if remaining_connections == 0:
                    del self.active_connections[job_id]
                    logger.info(f"🗑️ Removed empty connection set for job {short_job_id}")
                else:
                    logger.info(f"📊 Job {short_job_id} now has {remaining_connections} active connections")

        successful_sends = connection_count - len(connections_to_remove)
        logger.info(f"📈 Send summary for job {short_job_id}: {successful_sends}/{connection_count} successful")

    async def broadcast_job_update(self, job_id: str):
        """Fetch job from database and broadcast to all connected clients."""
        short_job_id = job_id[:8]
        
        try:
            logger.info(f"📡 Starting broadcast_job_update for job {short_job_id}")
            
            if job_id not in self.active_connections:
                logger.info(f"📭 No active connections for job {short_job_id}")
                # Still create notification for other processes
                await self._create_notification(job_id)
                return
                
            connection_count = len(self.active_connections[job_id])
            logger.info(f"📡 Broadcasting to {connection_count} connections for job {short_job_id}")
            
            logger.info(f"🗄️ Fetching job {short_job_id} from database...")
            db = SessionLocal()
            try:
                job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
                if job:
                    logger.info(f"✅ Found job {short_job_id}: status={job.status}, progress={job.progress_percentage}%")
                    
                    # Convert to response format
                    job_response = BiographyJobResponse.model_validate(job)
                    job_data = job_response.model_dump()
                    
                    logger.info(f"📦 Prepared job data for {short_job_id}, calling send_job_update...")
                    await self.send_job_update(job_id, job_data)
                    
                    logger.info(f"✅ Successfully broadcasted update for job {short_job_id}: {job.status} ({job.progress_percentage}%)")
                else:
                    logger.warning(f"❌ Job {short_job_id} not found in database for broadcast")
            finally:
                db.close()
                logger.info(f"🔒 Database session closed for job {short_job_id}")
                
        except Exception as e:
            logger.error(f"💥 Error broadcasting job update for {short_job_id}: {e}", exc_info=True)
    
    async def _create_notification(self, job_id: str):
        """Create a notification in database for inter-process communication."""
        try:
            db = SessionLocal()
            try:
                notification = WebSocketNotification(
                    job_id=job_id,
                    notification_type="job_update"
                )
                db.add(notification)
                db.commit()
                logger.info(f"💾 Created WebSocket notification for job {job_id[:8]}")
            finally:
                db.close()
        except Exception as e:
            logger.error(f"❌ Failed to create notification for job {job_id[:8]}: {e}")
    
    async def process_pending_notifications(self):
        """Process pending notifications from database (for main FastAPI process)."""
        try:
            db = SessionLocal()
            try:
                # Get unprocessed notifications
                notifications = db.query(WebSocketNotification).filter(
                    WebSocketNotification.processed == False
                ).all()
                
                for notification in notifications:
                    job_id = notification.job_id
                    if job_id in self.active_connections:
                        logger.info(f"📨 Processing notification for job {job_id[:8]}")
                        await self.broadcast_job_update(job_id)
                    
                    # Mark as processed
                    notification.processed = True
                    notification.processed_at = datetime.utcnow()
                
                if notifications:
                    db.commit()
                    logger.info(f"✅ Processed {len(notifications)} notifications")
                    
            finally:
                db.close()
        except Exception as e:
            logger.error(f"❌ Error processing notifications: {e}")

    def get_connection_count(self, job_id: str) -> int:
        """Get number of active connections for a job."""
        count = len(self.active_connections.get(job_id, set()))
        logger.debug(f"📊 Job {job_id[:8]} has {count} active connections")
        return count

    def get_total_connections(self) -> int:
        """Get total number of active connections across all jobs."""
        total = sum(len(connections) for connections in self.active_connections.values())
        active_jobs = len(self.active_connections)
        logger.debug(f"📊 Total connections: {total} across {active_jobs} jobs")
        return total

# Global instance
manager = ConnectionManager()
logger.info("🚀 Global WebSocket manager instance created")