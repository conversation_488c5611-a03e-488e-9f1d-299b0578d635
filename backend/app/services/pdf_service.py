import PyPDF2
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import black, darkblue
from typing import Optional, List, Dict, Tuple
import io
import re
import os

class PDFService:
    
    @staticmethod
    def extract_text_from_pdf(pdf_path: str) -> str:
        """Extract text content from a PDF file with optimization for large files."""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                total_pages = len(pdf_reader.pages)
                
                # For very large PDFs, show progress and handle memory efficiently
                if total_pages > 50:
                    print(f"Processing large PDF with {total_pages} pages...")
                
                for page_num in range(total_pages):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    text += page_text + "\n"
                    
                    # Progress indication for large files
                    if total_pages > 50 and page_num % 20 == 0:
                        print(f"Processed {page_num + 1}/{total_pages} pages...")
                
                # Clean up the extracted text
                text = PDFService._clean_extracted_text(text)
                return text
                
        except Exception as e:
            raise Exception(f"Error extracting text from PDF: {str(e)}")

    @staticmethod
    def extract_text_from_pdf_in_chunks(pdf_path: str, chunk_size: int = 20) -> List[Dict]:
        """Extract text from PDF in chunks for large file processing."""
        try:
            chunks = []
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                for start_page in range(0, total_pages, chunk_size):
                    end_page = min(start_page + chunk_size, total_pages)
                    chunk_text = ""
                    
                    for page_num in range(start_page, end_page):
                        page = pdf_reader.pages[page_num]
                        chunk_text += page.extract_text() + "\n"
                    
                    # Clean up the chunk text
                    clean_chunk = PDFService._clean_extracted_text(chunk_text)
                    
                    chunks.append({
                        'start_page': start_page + 1,
                        'end_page': end_page,
                        'page_count': end_page - start_page,
                        'content': clean_chunk,
                        'character_count': len(clean_chunk),
                        'word_count': len(clean_chunk.split())
                    })
                
                return chunks
                
        except Exception as e:
            raise Exception(f"Error extracting text from PDF in chunks: {str(e)}")

    @staticmethod
    def analyze_pdf_structure(pdf_path: str) -> Dict:
        """Analyze PDF structure to determine optimal processing strategy."""
        try:
            file_size = os.path.getsize(pdf_path)
            
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                # Sample first few pages to estimate text density
                sample_text = ""
                sample_pages = min(5, total_pages)
                
                for page_num in range(sample_pages):
                    page = pdf_reader.pages[page_num]
                    sample_text += page.extract_text()
                
                # Calculate averages
                avg_chars_per_page = len(sample_text) / sample_pages if sample_pages > 0 else 0
                estimated_total_chars = avg_chars_per_page * total_pages
                estimated_words = estimated_total_chars / 5  # Average 5 chars per word
                
                # Determine processing strategy
                is_large_file = total_pages > 50 or estimated_total_chars > 100000
                recommended_chunk_size = 20 if is_large_file else total_pages
                
                # Detect document type by content patterns
                document_type = PDFService._detect_document_type(sample_text)
                
                return {
                    'file_size_bytes': file_size,
                    'total_pages': total_pages,
                    'avg_chars_per_page': avg_chars_per_page,
                    'estimated_total_chars': estimated_total_chars,
                    'estimated_words': estimated_words,
                    'is_large_file': is_large_file,
                    'recommended_chunk_size': recommended_chunk_size,
                    'document_type': document_type,
                    'processing_recommendation': 'chunked' if is_large_file else 'standard'
                }
                
        except Exception as e:
            raise Exception(f"Error analyzing PDF structure: {str(e)}")

    @staticmethod
    def _detect_document_type(sample_text: str) -> str:
        """Detect the type of document based on content patterns."""
        text_lower = sample_text.lower()
        
        # Interview patterns
        if any(pattern in text_lower for pattern in [
            'ai:', 'user:', 'interview', 'session', 'q:', 'a:', 
            'interviewer:', 'interviewee:', 'eternal ai'
        ]):
            return 'interview'
        
        # Transcript patterns
        elif any(pattern in text_lower for pattern in [
            'transcript', 'speaker', 'recording', 'conversation'
        ]):
            return 'transcript'
        
        # Memoir/autobiography patterns
        elif any(pattern in text_lower for pattern in [
            'chapter', 'my life', 'my story', 'growing up', 'childhood'
        ]):
            return 'memoir'
        
        # Biography patterns
        elif any(pattern in text_lower for pattern in [
            'born in', 'life of', 'biography', 'early years'
        ]):
            return 'biography'
        
        else:
            return 'document'

    @staticmethod
    def extract_structured_interview_data(pdf_path: str) -> Dict:
        """Extract structured data from interview PDFs with session detection."""
        try:
            chunks = PDFService.extract_text_from_pdf_in_chunks(pdf_path, chunk_size=25)
            
            sessions = []
            session_counter = 0
            
            for chunk in chunks:
                content = chunk['content']
                
                # Detect session markers
                session_markers = PDFService._find_session_markers(content)
                
                if session_markers:
                    for marker in session_markers:
                        session_counter += 1
                        sessions.append({
                            'session_number': session_counter,
                            'marker': marker,
                            'pages': f"{chunk['start_page']}-{chunk['end_page']}",
                            'content_preview': content[marker['position']:marker['position']+200]
                        })
                
                # If no session markers in this chunk, treat as continuation
                if not session_markers and sessions:
                    sessions[-1]['content_preview'] += f"\n[Continues in pages {chunk['start_page']}-{chunk['end_page']}]"
            
            # Extract key themes and topics
            all_content = "\n".join([chunk['content'] for chunk in chunks])
            themes = PDFService._extract_key_themes(all_content)
            
            return {
                'chunks': chunks,
                'sessions': sessions,
                'total_sessions': session_counter,
                'key_themes': themes,
                'structure_type': 'multi_session_interview' if session_counter > 1 else 'single_interview',
                'total_content_length': len(all_content),
                'processing_notes': f"Detected {session_counter} interview sessions across {len(chunks)} content chunks"
            }
            
        except Exception as e:
            raise Exception(f"Error extracting structured interview data: {str(e)}")

    @staticmethod
    def _find_session_markers(content: str) -> List[Dict]:
        """Find session markers in interview content."""
        markers = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_clean = line.strip().lower()
            
            # Common session patterns
            session_patterns = [
                r'session\s+\d+',
                r'eternal\s+ai.*session',
                r'welcome\s+back.*eternal',
                r'ai:\s*hello.*welcome',
                r'interview\s+session\s+\d+',
                r'part\s+\d+.*interview'
            ]
            
            for pattern in session_patterns:
                if re.search(pattern, line_clean):
                    markers.append({
                        'line_number': i,
                        'content': line.strip(),
                        'pattern_matched': pattern,
                        'position': content.find(line)
                    })
                    break
        
        return markers

    @staticmethod
    def _extract_key_themes(content: str) -> List[str]:
        """Extract key themes and topics from interview content."""
        # Common biographical themes
        theme_patterns = {
            'childhood': r'\b(childhood|growing up|when i was young|as a child|early years)\b',
            'family': r'\b(family|mother|father|parents|siblings|spouse|children)\b',
            'education': r'\b(school|college|university|education|learning|degree)\b',
            'career': r'\b(work|job|career|profession|business|company)\b',
            'relationships': r'\b(marriage|wedding|relationship|partner|friend)\b',
            'challenges': r'\b(difficult|challenge|struggle|hardship|problem)\b',
            'achievements': r'\b(achievement|success|accomplish|proud|award)\b',
            'values': r'\b(value|believe|principle|important|matter)\b',
            'hobbies': r'\b(hobby|enjoy|love|passion|interest)\b',
            'travel': r'\b(travel|trip|visit|journey|vacation)\b'
        }
        
        content_lower = content.lower()
        detected_themes = []
        
        for theme, pattern in theme_patterns.items():
            matches = re.findall(pattern, content_lower)
            if len(matches) > 5:  # Theme appears frequently
                detected_themes.append(theme)
        
        return detected_themes
    
    @staticmethod
    def _clean_extracted_text(text: str) -> str:
        """Clean and format extracted text with improved handling for large documents."""
        # Remove excessive whitespace but preserve paragraph structure
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)  # Multiple newlines to double
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs to single space
        
        # Remove page numbers and headers/footers if they appear to be repetitive
        lines = text.split('\n')
        cleaned_lines = []
        
        # Patterns that might indicate headers/footers
        header_footer_patterns = [
            r'^\d+$',  # Just a number
            r'^page\s+\d+',  # Page numbers
            r'^\d+\s*of\s*\d+',  # Page x of y
            r'^(transcript|interview|session)\s*$'  # Common headers
        ]
        
        for line in lines:
            line = line.strip()
            
            # Skip very short lines that might be artifacts
            if len(line) < 3:
                continue
                
            # Check if line matches header/footer patterns
            is_header_footer = False
            for pattern in header_footer_patterns:
                if re.match(pattern, line.lower()):
                    is_header_footer = True
                    break
            
            if not is_header_footer:
                cleaned_lines.append(line)
        
        # Rejoin and clean up remaining issues
        cleaned_text = '\n'.join(cleaned_lines)
        
        # Remove excessive spacing around punctuation
        cleaned_text = re.sub(r'\s+([.!?;,])', r'\1', cleaned_text)
        cleaned_text = re.sub(r'([.!?])\s*\n\s*([a-z])', r'\1 \2', cleaned_text)
        
        return cleaned_text

    @staticmethod
    def generate_biography_pdf(
        biography_content: str, 
        user_name: str, 
        output_path: str
    ) -> bool:
        """Generate a formatted PDF book from biography content."""
        try:
            # Create PDF document
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Get style sheets
            styles = getSampleStyleSheet()
            
            # Create custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=1,  # Center alignment
                textColor='darkblue'
            )
            
            subtitle_style = ParagraphStyle(
                'CustomSubtitle',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=20,
                alignment=1
            )
            
            chapter_style = ParagraphStyle(
                'ChapterTitle',
                parent=styles['Heading2'],
                fontSize=18,
                spaceAfter=12,
                spaceBefore=20,
                textColor='darkblue'
            )
            
            body_style = ParagraphStyle(
                'CustomBody',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                leading=16,
                alignment=0  # Justify
            )
            
            # Build the document content
            story = []
            
            # Title page
            story.append(Paragraph(f"The Life Story of", subtitle_style))
            story.append(Paragraph(user_name, title_style))
            story.append(Spacer(1, 50))
            story.append(Paragraph("A Personal Biography", subtitle_style))
            story.append(PageBreak())
            
            # Process biography content
            content_lines = biography_content.split('\n')
            current_chapter = None
            
            for line in content_lines:
                line = line.strip()
                if not line:
                    continue
                
                # Check if line is a chapter heading
                if (line.startswith('Chapter') or 
                    line.startswith('CHAPTER') or
                    (len(line) < 100 and line.isupper() and len(line) > 10)):
                    
                    if current_chapter is not None:
                        story.append(PageBreak())
                    
                    story.append(Paragraph(line, chapter_style))
                    current_chapter = line
                    
                # Check if line looks like a subtitle
                elif (len(line) < 100 and 
                      (line.count(' ') < 10) and 
                      not line.endswith('.') and
                      not line.startswith('"')):
                    
                    story.append(Paragraph(line, styles['Heading3']))
                    
                # Regular paragraph
                else:
                    # Handle quotes properly
                    if line.startswith('"') and line.endswith('"'):
                        quote_style = ParagraphStyle(
                            'Quote',
                            parent=body_style,
                            leftIndent=36,
                            rightIndent=36,
                            fontStyle='italic'
                        )
                        story.append(Paragraph(line, quote_style))
                    else:
                        story.append(Paragraph(line, body_style))
                
                story.append(Spacer(1, 6))
            
            # Build PDF
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"Error generating PDF: {str(e)}")

    @staticmethod
    def generate_enhanced_biography_pdf(
        biography_content: str,
        user_name: str,
        chapters: List[Dict],
        output_path: str
    ) -> bool:
        """Generate an enhanced book-quality PDF with table of contents and better formatting."""
        try:
            # Create PDF document with better margins for book format
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=54,  # 0.75 inch
                leftMargin=72,   # 1 inch (binding side)
                topMargin=72,    # 1 inch
                bottomMargin=54  # 0.75 inch
            )
            
            # Get style sheets and create enhanced styles
            styles = getSampleStyleSheet()
            
            # Enhanced styles for book-quality formatting
            title_style = ParagraphStyle(
                'BookTitle',
                parent=styles['Title'],
                fontSize=28,
                spaceAfter=50,
                spaceBefore=50,
                alignment=1,  # Center
                textColor=darkblue,
                fontName='Helvetica-Bold'
            )
            
            subtitle_style = ParagraphStyle(
                'BookSubtitle',
                parent=styles['Heading2'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,
                textColor=black,
                fontName='Helvetica'
            )
            
            chapter_title_style = ParagraphStyle(
                'ChapterTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=20,
                spaceBefore=40,
                textColor=darkblue,
                fontName='Helvetica-Bold',
                keepWithNext=True
            )
            
            section_style = ParagraphStyle(
                'SectionHeading',
                parent=styles['Heading3'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=18,
                textColor=black,
                fontName='Helvetica-Bold'
            )
            
            body_style = ParagraphStyle(
                'BookBody',
                parent=styles['Normal'],
                fontSize=11,
                spaceAfter=8,
                leading=14,
                alignment=4,  # Justify
                fontName='Times-Roman',
                firstLineIndent=18
            )
            
            quote_style = ParagraphStyle(
                'Quote',
                parent=body_style,
                leftIndent=36,
                rightIndent=36,
                fontStyle=1,  # Italic
                spaceAfter=12,
                spaceBefore=12
            )
            
            toc_title_style = ParagraphStyle(
                'TOCTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,
                textColor=darkblue
            )
            
            toc_entry_style = ParagraphStyle(
                'TOCEntry',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=8,
                leftIndent=0
            )
            
            # Build the document content
            story = []
            
            # Title page
            story.append(Spacer(1, 2*inch))
            story.append(Paragraph("The Life Story of", subtitle_style))
            story.append(Spacer(1, 0.5*inch))
            story.append(Paragraph(user_name, title_style))
            story.append(Spacer(1, 1*inch))
            story.append(Paragraph("A Personal Biography", subtitle_style))
            story.append(PageBreak())
            
            # Table of Contents
            story.append(Paragraph("Table of Contents", toc_title_style))
            story.append(Spacer(1, 0.3*inch))
            
            for i, chapter in enumerate(chapters):
                page_num = i + 3  # Rough page estimation
                toc_line = f"Chapter {chapter['number']}: {chapter['title']} {'.' * 20} {page_num}"
                story.append(Paragraph(toc_line, toc_entry_style))
            
            story.append(PageBreak())
            
            # Process enhanced biography content
            content_sections = biography_content.split('Chapter ')
            
            # Track unique paragraphs to avoid duplicates
            unique_paragraphs = set()
            
            # Helper function to normalize text for comparison
            def normalize_text(text):
                # Remove extra spaces, convert to lowercase, and remove punctuation for comparison
                normalized = re.sub(r'[^\w\s]', '', text.lower())
                normalized = re.sub(r'\s+', ' ', normalized).strip()
                return normalized
            
            # Helper function to check if paragraph is unique
            def is_unique_paragraph(text):
                normalized = normalize_text(text)
                if len(normalized) < 20:  # Skip very short texts
                    return True
                if normalized in unique_paragraphs:
                    return False
                unique_paragraphs.add(normalized)
                return True
            
            for i, section in enumerate(content_sections[1:], 1):  # Skip first empty section
                lines = section.split('\n')
                if not lines:
                    continue
                
                # Extract chapter title from first line
                chapter_title_line = lines[0].strip()
                chapter_number = None
                chapter_title = None
                
                if ':' in chapter_title_line:
                    chapter_num_part, chapter_title = chapter_title_line.split(':', 1)
                    # Extract numeric part from chapter number
                    chapter_num_match = re.search(r'(\d+)', chapter_num_part)
                    if chapter_num_match:
                        chapter_number = int(chapter_num_match.group(1))
                else:
                    # Try to extract number from beginning of line
                    chapter_num_match = re.search(r'^(\d+)', chapter_title_line)
                    if chapter_num_match:
                        chapter_number = int(chapter_num_match.group(1))
                        chapter_title = chapter_title_line[len(chapter_num_match.group(0)):].strip()
                
                # Only add chapter title if chapter number is 6 or less
                should_add_chapter_title = chapter_number is None or chapter_number <= 6
                
                if should_add_chapter_title:
                    if chapter_number and chapter_title:
                        full_chapter_title = f"Chapter {chapter_number}: {chapter_title.strip()}"
                    else:
                        full_chapter_title = f"Chapter {chapter_title_line}"
                    
                    # Add chapter title
                    story.append(Paragraph(full_chapter_title, chapter_title_style))
                    story.append(Spacer(1, 0.2*inch))
                
                # Process chapter content (always process content, even if we skip chapter title)
                current_paragraph = ""
                in_quote = False
                
                for line in lines[1:]:
                    line = line.strip()
                    if not line:
                        if current_paragraph and is_unique_paragraph(current_paragraph):
                            # End current paragraph
                            style = quote_style if in_quote else body_style
                            story.append(Paragraph(current_paragraph, style))
                            story.append(Spacer(1, 6))
                        current_paragraph = ""
                        in_quote = False
                        continue
                    
                    # Skip separator lines
                    if line.startswith('===') or line.startswith('---'):
                        continue
                    
                    # Skip sub-chapter headings (like "Chapter 7:", "Chapter 8:" etc)
                    if re.match(r'^Chapter\s+\d+\s*:', line, re.IGNORECASE):
                        continue
                    
                    # Check for quotes
                    if line.startswith('"') and line.endswith('"'):
                        if current_paragraph and is_unique_paragraph(current_paragraph):
                            # Finish current paragraph first
                            story.append(Paragraph(current_paragraph, body_style))
                            current_paragraph = ""
                        
                        # Add quote if unique
                        if is_unique_paragraph(line):
                            story.append(Paragraph(line, quote_style))
                            story.append(Spacer(1, 6))
                        continue
                    
                    # Skip section headings that look like sub-chapters or repetitive headers
                    if (len(line) < 80 and 
                        not line.endswith('.') and 
                        not line.endswith(',') and
                        line.count(' ') < 8):
                        
                        # Skip if it looks like a chapter heading
                        if re.search(r'Chapter\s+\d+', line, re.IGNORECASE):
                            continue
                            
                        # Skip if it's a repetitive section heading
                        if not is_unique_paragraph(line):
                            continue
                        
                        if current_paragraph and is_unique_paragraph(current_paragraph):
                            # Finish current paragraph first
                            story.append(Paragraph(current_paragraph, body_style))
                            current_paragraph = ""
                        
                        # Add section heading
                        story.append(Paragraph(line, section_style))
                        continue
                    
                    # Regular content - accumulate into paragraph
                    if current_paragraph:
                        current_paragraph += " " + line
                    else:
                        current_paragraph = line
                    
                    # If paragraph is getting long, end it
                    if len(current_paragraph) > 500:
                        if is_unique_paragraph(current_paragraph):
                            story.append(Paragraph(current_paragraph, body_style))
                            story.append(Spacer(1, 6))
                        current_paragraph = ""
                
                # Add any remaining paragraph
                if current_paragraph and is_unique_paragraph(current_paragraph):
                    style = quote_style if in_quote else body_style
                    story.append(Paragraph(current_paragraph, style))
                    story.append(Spacer(1, 6))
                
                # Page break after each chapter except the last (only for valid chapters)
                if should_add_chapter_title and i < len(content_sections) - 1:
                    story.append(PageBreak())
            
            # Build PDF
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"Error generating enhanced PDF: {str(e)}")
    
    @staticmethod
    def validate_pdf_file(file_path: str) -> bool:
        """Validate if the file is a readable PDF."""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                # Try to read the first page to ensure it's valid
                if len(pdf_reader.pages) > 0:
                    pdf_reader.pages[0].extract_text()
                    return True
                return False
        except Exception:
            return False

    @staticmethod
    def extract_person_name_from_interview(pdf_path: str) -> str:
        """Extract the interviewed person's real name from PDF content."""
        try:
            # Extract text from PDF
            text = PDFService.extract_text_from_pdf(pdf_path)
            
            # Try multiple extraction strategies
            
            # Strategy 1: Look for explicit introductions
            intro_patterns = [
                r'my name is ([A-Z][a-z]+ [A-Z][a-z]+)',
                r"i'm ([A-Z][a-z]+ [A-Z][a-z]+)",
                r'i am ([A-Z][a-z]+ [A-Z][a-z]+)',
                r'this is ([A-Z][a-z]+ [A-Z][a-z]+)'
            ]
            
            for pattern in intro_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    return matches[0]
            
            # Strategy 2: Look for consistent speaker names (not AI/User)
            speaker_patterns = [
                r'^([A-Z][a-z]+):\s',
                r'([A-Z][a-z]+ [A-Z][a-z]+):\s'
            ]
            
            speaker_names = {}
            lines = text.split('\n')
            
            for line in lines:
                for pattern in speaker_patterns:
                    matches = re.findall(pattern, line)
                    for match in matches:
                        # Skip common non-names
                        if match.lower() not in ['ai', 'user', 'interviewer', 'interviewee', 'eternal', 'assistant']:
                            speaker_names[match] = speaker_names.get(match, 0) + 1
            
            # Return most frequent non-AI speaker name
            if speaker_names:
                most_common = max(speaker_names.items(), key=lambda x: x[1])
                if most_common[1] > 2:  # Appears more than twice
                    return most_common[0]
            
            # Strategy 3: Extract from filename if available
            filename = os.path.basename(pdf_path)
            name_from_filename = re.search(r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)', filename)
            if name_from_filename:
                potential_name = name_from_filename.group(1)
                if potential_name.lower() not in ['interview', 'transcript', 'session', 'eternal', 'document']:
                    return potential_name
            
            # Strategy 4: Look for possessive patterns
            possessive_patterns = [
                r"([A-Z][a-z]+(?:'s|\s+[A-Z][a-z]+(?:'s)?)) (?:story|life|biography|childhood|family)",
                r"about ([A-Z][a-z]+ [A-Z][a-z]+)(?:'s life|'s story)"
            ]
            
            for pattern in possessive_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    return matches[0].replace("'s", "").strip()
            
            # Fallback: Return "Unknown Person" instead of generic name
            return "Unknown Person"
            
        except Exception as e:
            print(f"Error extracting person name: {e}")
            return "Unknown Person" 