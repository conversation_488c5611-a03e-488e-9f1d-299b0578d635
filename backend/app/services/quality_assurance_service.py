"""
Quality Assurance Service for Biography Generation

This service provides post-processing quality assurance for generated biographies,
addressing issues like:
- Overused words and phrases
- Repetitive content
- Style inconsistencies
- Overall quality improvements
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from collections import Counter
from sqlalchemy.orm import Session
from app.services.ai_service import AIService

logger = logging.getLogger(__name__)

class QualityAssuranceService:
    """Service for post-processing quality assurance of biographies."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService(db)
        
        # Words that are commonly overused in biographies
        self.overused_words = {
            'tapestry': ['fabric', 'story', 'narrative', 'composition', 'blend', 'weaving', 'pattern'],
            'mosaic': ['collection', 'array', 'combination', 'mixture', 'assembly', 'compilation'],
            'journey': ['experience', 'progression', 'development', 'evolution', 'path', 'adventure'],
            'palette': ['range', 'spectrum', 'variety', 'selection', 'array', 'collection'],
            'woven': ['integrated', 'combined', 'blended', 'merged', 'connected', 'intertwined'],
            'painted': ['created', 'formed', 'shaped', 'crafted', 'built', 'developed'],
            'canvas': ['backdrop', 'setting', 'foundation', 'background', 'stage'],
            'thread': ['element', 'aspect', 'component', 'strand', 'part'],
            'fabric': ['structure', 'foundation', 'essence', 'core', 'framework'],
            'brushstroke': ['touch', 'detail', 'element', 'aspect', 'feature']
        }
        
        # Threshold for considering a word overused (occurrences per 1000 words)
        self.overuse_threshold = 3.0
        
    async def process_biography_quality_assurance(self, biography_content: str, person_name: str) -> Dict:
        """
        Process a complete biography through quality assurance.
        
        Args:
            biography_content: The complete biography text
            person_name: Name of the person the biography is about
            
        Returns:
            Dict containing the improved biography and quality metrics
        """
        logger.info(f"🔍 Starting quality assurance for biography ({len(biography_content):,} chars)")
        
        # Step 1: Analyze current quality issues
        quality_analysis = self._analyze_quality_issues(biography_content)
        
        # Step 2: Fix overused words
        improved_content = self._fix_overused_words(biography_content, quality_analysis['overused_words'])
        
        # Step 3: Remove repetitive content
        improved_content = self._remove_repetitive_content(improved_content)
        
        # Step 4: AI-powered quality improvement
        if quality_analysis['needs_ai_improvement']:
            improved_content = await self._ai_quality_improvement(
                improved_content, 
                quality_analysis, 
                person_name
            )
        
        # Step 5: Final analysis
        final_analysis = self._analyze_quality_issues(improved_content)
        
        logger.info(f"✅ Quality assurance complete. Improved {len(biography_content):,} → {len(improved_content):,} chars")
        
        return {
            'improved_biography': improved_content,
            'original_analysis': quality_analysis,
            'final_analysis': final_analysis,
            'improvements_made': self._calculate_improvements(quality_analysis, final_analysis)
        }
    
    def _analyze_quality_issues(self, content: str) -> Dict:
        """Analyze the biography for quality issues."""
        
        words = re.findall(r'\b\w+\b', content.lower())
        word_count = len(words)
        word_freq = Counter(words)
        
        # Find overused words
        overused_words = {}
        for word, alternatives in self.overused_words.items():
            count = word_freq.get(word, 0)
            frequency_per_1000 = (count / word_count) * 1000 if word_count > 0 else 0
            
            if frequency_per_1000 > self.overuse_threshold:
                overused_words[word] = {
                    'count': count,
                    'frequency_per_1000': frequency_per_1000,
                    'alternatives': alternatives
                }
        
        # Check for repetitive phrases
        repetitive_phrases = self._find_repetitive_phrases(content)
        
        # Calculate overall quality score
        quality_score = self._calculate_quality_score(content, overused_words, repetitive_phrases)
        
        return {
            'word_count': word_count,
            'overused_words': overused_words,
            'repetitive_phrases': repetitive_phrases,
            'quality_score': quality_score,
            'needs_ai_improvement': quality_score < 7.0 or len(overused_words) > 2
        }
    
    def _find_repetitive_phrases(self, content: str) -> List[Dict]:
        """Find repetitive phrases in the content."""
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 20]
        
        repetitive_phrases = []
        
        # Look for similar sentence patterns
        for i, sentence1 in enumerate(sentences):
            for j, sentence2 in enumerate(sentences[i+1:], i+1):
                similarity = self._calculate_sentence_similarity(sentence1, sentence2)
                if similarity > 0.7:  # High similarity threshold
                    repetitive_phrases.append({
                        'sentence1': sentence1[:100] + '...' if len(sentence1) > 100 else sentence1,
                        'sentence2': sentence2[:100] + '...' if len(sentence2) > 100 else sentence2,
                        'similarity': similarity,
                        'positions': [i, j]
                    })
        
        return repetitive_phrases
    
    def _calculate_sentence_similarity(self, sent1: str, sent2: str) -> float:
        """Calculate similarity between two sentences."""
        
        words1 = set(re.findall(r'\b\w+\b', sent1.lower()))
        words2 = set(re.findall(r'\b\w+\b', sent2.lower()))
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _calculate_quality_score(self, content: str, overused_words: Dict, repetitive_phrases: List) -> float:
        """Calculate an overall quality score for the biography."""
        
        base_score = 8.0
        
        # Deduct for overused words
        overuse_penalty = min(len(overused_words) * 0.5, 2.0)
        
        # Deduct for repetitive phrases
        repetition_penalty = min(len(repetitive_phrases) * 0.3, 1.5)
        
        # Deduct for very short content
        word_count = len(re.findall(r'\b\w+\b', content))
        if word_count < 1000:
            length_penalty = 1.0
        elif word_count < 2000:
            length_penalty = 0.5
        else:
            length_penalty = 0.0
        
        final_score = base_score - overuse_penalty - repetition_penalty - length_penalty
        return max(final_score, 1.0)  # Minimum score of 1.0
    
    def _fix_overused_words(self, content: str, overused_words: Dict) -> str:
        """Replace overused words with alternatives."""
        
        if not overused_words:
            return content
        
        logger.info(f"🔧 Fixing {len(overused_words)} overused words")
        
        improved_content = content
        
        for word, info in overused_words.items():
            alternatives = info['alternatives']
            count = info['count']
            
            # Find all occurrences of the word
            pattern = r'\b' + re.escape(word) + r'\b'
            matches = list(re.finditer(pattern, improved_content, re.IGNORECASE))
            
            # Replace some occurrences (keep 1-2, replace the rest)
            keep_count = min(2, max(1, count // 3))
            replace_count = len(matches) - keep_count
            
            if replace_count > 0:
                # Replace from the end to avoid position shifts
                for i, match in enumerate(reversed(matches)):
                    if i < replace_count:
                        # Choose a random alternative
                        alternative = alternatives[i % len(alternatives)]
                        
                        # Preserve capitalization
                        original = match.group()
                        if original[0].isupper():
                            alternative = alternative.capitalize()
                        
                        start, end = match.span()
                        improved_content = improved_content[:start] + alternative + improved_content[end:]
                
                logger.info(f"   Replaced {replace_count}/{count} occurrences of '{word}'")
        
        return improved_content
    
    def _remove_repetitive_content(self, content: str) -> str:
        """Remove or modify repetitive content."""
        
        # Split into paragraphs
        paragraphs = content.split('\n\n')
        unique_paragraphs = []
        seen_content = set()
        
        for paragraph in paragraphs:
            if not paragraph.strip():
                continue
                
            # Create a simplified version for comparison
            simplified = re.sub(r'[^\w\s]', '', paragraph.lower())
            simplified = ' '.join(simplified.split())
            
            # Check if we've seen very similar content
            is_duplicate = False
            for seen in seen_content:
                similarity = self._calculate_sentence_similarity(simplified, seen)
                if similarity > 0.8:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_paragraphs.append(paragraph)
                seen_content.add(simplified)
            else:
                logger.info(f"   Removed repetitive paragraph: {paragraph[:50]}...")
        
        return '\n\n'.join(unique_paragraphs)
    
    async def _ai_quality_improvement(self, content: str, quality_analysis: Dict, person_name: str) -> str:
        """Use AI to improve the biography quality."""
        
        logger.info("🤖 Applying AI-powered quality improvements")
        
        # Create improvement prompt based on identified issues
        issues = []
        if quality_analysis['overused_words']:
            word_list = ', '.join(quality_analysis['overused_words'].keys())
            issues.append(f"Overused words that still need variation: {word_list}")
        
        if quality_analysis['repetitive_phrases']:
            issues.append(f"Found {len(quality_analysis['repetitive_phrases'])} repetitive phrases that need rewording")
        
        if quality_analysis['quality_score'] < 6.0:
            issues.append("Overall writing quality needs improvement for better flow and engagement")
        
        improvement_prompt = f"""Please review and improve this biography for {person_name}. 

SPECIFIC ISSUES TO ADDRESS:
{chr(10).join(f"- {issue}" for issue in issues)}

IMPROVEMENT GUIDELINES:
- Vary vocabulary and avoid repetitive word choices
- Improve sentence structure and flow
- Enhance readability while maintaining authenticity
- Remove any redundant or repetitive content
- Ensure the writing is engaging and professional
- Maintain all factual content and personal details
- Keep the same overall structure and length

BIOGRAPHY TO IMPROVE:
{content}

Please return the improved biography with better word variety, flow, and overall quality."""
        
        system_message = """You are a professional biography editor specializing in improving writing quality. 
Your task is to enhance the given biography by addressing specific quality issues while maintaining 
authenticity and factual accuracy. Focus on improving word variety, sentence flow, and overall readability."""
        
        try:
            improved_content = await self.ai_service.generate_completion(
                improvement_prompt,
                system_message,
                max_tokens=None,  # Use maximum available tokens
                temperature=0.7
            )
            
            if improved_content and improved_content.strip():
                logger.info(f"✅ AI improvement complete: {len(content):,} → {len(improved_content):,} chars")
                return improved_content.strip()
            else:
                logger.warning("AI improvement returned empty content, using original")
                return content
                
        except Exception as e:
            logger.error(f"AI improvement failed: {e}")
            return content
    
    def _calculate_improvements(self, original_analysis: Dict, final_analysis: Dict) -> Dict:
        """Calculate what improvements were made."""
        
        return {
            'quality_score_improvement': final_analysis['quality_score'] - original_analysis['quality_score'],
            'overused_words_fixed': len(original_analysis['overused_words']) - len(final_analysis['overused_words']),
            'repetitive_phrases_removed': len(original_analysis['repetitive_phrases']) - len(final_analysis['repetitive_phrases']),
            'word_count_change': final_analysis['word_count'] - original_analysis['word_count']
        }
