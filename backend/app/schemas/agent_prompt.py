from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class AgentPromptCreate(BaseModel):
    agent_name: str
    prompt_content: str

class AgentPromptUpdate(BaseModel):
    prompt_content: Optional[str] = None
    is_active: Optional[bool] = None

class AgentPromptResponse(BaseModel):
    id: str
    agent_name: str
    prompt_content: str
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    version: int

    class Config:
        from_attributes = True 