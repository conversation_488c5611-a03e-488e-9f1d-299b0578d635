from pydantic import BaseModel, EmailStr, field_validator
from typing import Optional, Union
from datetime import datetime
from enum import Enum
import json

class JobStatusEnum(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    OUTLINE_COMPLETE = "outline_complete"
    WRITING_COMPLETE = "writing_complete"
    EVALUATION_COMPLETE = "evaluation_complete"
    REWRITE_COMPLETE = "rewrite_complete"
    GENERATING_PDF = "generating_pdf"
    ITERATIVE_WRITING = "iterative_writing"
    COMPLETED = "completed"
    FAILED = "failed"

class BiographyJobCreate(BaseModel):
    user_name: str
    email: Optional[EmailStr] = None

class BiographyJobUpdate(BaseModel):
    status: Optional[JobStatusEnum] = None
    progress_percentage: Optional[float] = None
    error_message: Optional[str] = None
    outline_content: Optional[str] = None
    biography_content: Optional[str] = None
    evaluation_content: Optional[str] = None
    final_biography_content: Optional[str] = None
    output_pdf_path: Optional[str] = None
    processing_time_seconds: Optional[float] = None
    
    # New fields for iterative generation
    chapters_content: Optional[dict] = None
    chapter_theses: Optional[dict] = None
    generation_iterations: Optional[dict] = None
    total_chapters: Optional[int] = None
    completed_chapters: Optional[int] = None

class BiographyJobResponse(BaseModel):
    id: str
    user_name: str
    email: Optional[str] = None
    uploaded_pdf_path: str
    output_pdf_path: Optional[str] = None
    status: JobStatusEnum
    progress_percentage: float
    error_message: Optional[str] = None
    outline_content: Optional[str] = None
    biography_content: Optional[str] = None
    evaluation_content: Optional[str] = None
    final_biography_content: Optional[str] = None
    
    # New fields for iterative generation
    chapters_content: Optional[dict] = None
    chapter_theses: Optional[dict] = None
    generation_iterations: Optional[dict] = None
    total_chapters: Optional[int] = None
    completed_chapters: Optional[int] = None
    
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processing_time_seconds: Optional[float] = None

    @field_validator('chapters_content', 'chapter_theses', 'generation_iterations', mode='before')
    @classmethod
    def parse_json_fields(cls, v: Union[str, dict, None]) -> Optional[dict]:
        """Convert JSON strings to dictionaries for WebSocket compatibility"""
        if v is None:
            return None
        if isinstance(v, dict):
            return v
        if isinstance(v, str):
            try:
                return json.loads(v)
            except (json.JSONDecodeError, TypeError):
                # If it's not valid JSON, return None instead of failing
                return None
        return None

    class Config:
        from_attributes = True 