#!/usr/bin/env python3
"""
Test script to verify the quality improvements work correctly.
"""

import asyncio
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.models.base import get_db
from app.services.quality_assurance_service import QualityAssuranceService
from app.services.word_tracking_service import WordTrackingService, GlobalWordTracker

def test_word_tracking():
    """Test the word tracking service."""
    print("🧪 Testing Word Tracking Service...")
    
    # Create a test tracker
    tracker = WordTrackingService("test-job-123")
    
    # Test content with overused words
    test_content = """
    This is a rich tapestry of life experiences. The colorful mosaic of memories creates a beautiful tapestry.
    Her life's journey was like a vibrant palette of experiences. The woven fabric of her story shows how 
    each thread contributes to the overall tapestry. This journey through life painted a masterful portrait
    on the canvas of time. The tapestry of her experiences created a mosaic of beautiful memories.
    """
    
    # Update word counts
    tracker.update_word_counts(test_content)
    
    # Check for overused words
    overused = tracker.get_overused_words()
    print(f"   Found {len(overused)} overused words:")
    for word, info in overused.items():
        print(f"   - '{word}': {info['count']} times ({info['frequency_per_1000']:.1f}/1000 words)")
    
    # Test replacement suggestions
    improved_content = tracker.suggest_replacement_text(test_content, max_replacements=3)
    print(f"   Original length: {len(test_content)} chars")
    print(f"   Improved length: {len(improved_content)} chars")
    print(f"   Content changed: {'Yes' if improved_content != test_content else 'No'}")
    
    # Show usage report
    report = tracker.get_usage_report()
    print(f"   Total words tracked: {report['total_words']}")
    print(f"   Tracked word occurrences: {len(report['tracked_word_counts'])}")
    
    print("✅ Word tracking test completed\n")

async def test_quality_assurance():
    """Test the quality assurance service."""
    print("🧪 Testing Quality Assurance Service...")
    
    # Get database session
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        qa_service = QualityAssuranceService(db)
        
        # Test content with quality issues
        test_biography = """
        John's life was a rich tapestry of experiences. This colorful tapestry began in childhood.
        The tapestry of his early years was woven with threads of adventure. His journey through life
        created a beautiful mosaic of memories. This journey was filled with challenges and triumphs.
        The mosaic of his experiences painted a vivid picture. Each brushstroke of life added to the
        overall tapestry. The journey continued as he grew older, adding more threads to his life's tapestry.
        
        John's career was another thread in the tapestry. This professional journey added richness to
        the overall mosaic. The tapestry of his work life was complex and rewarding. His journey in
        business created new patterns in the tapestry of his existence.
        """
        
        # Process through quality assurance
        result = await qa_service.process_biography_quality_assurance(test_biography, "John Smith")
        
        print(f"   Original quality score: {result['original_analysis']['quality_score']:.1f}")
        print(f"   Final quality score: {result['final_analysis']['quality_score']:.1f}")
        print(f"   Quality improvement: {result['improvements_made']['quality_score_improvement']:+.1f}")
        print(f"   Overused words fixed: {result['improvements_made']['overused_words_fixed']}")
        print(f"   Repetitive phrases removed: {result['improvements_made']['repetitive_phrases_removed']}")
        print(f"   Word count change: {result['improvements_made']['word_count_change']:+d}")
        
        # Show overused words found
        overused_words = result['original_analysis']['overused_words']
        if overused_words:
            print(f"   Overused words detected:")
            for word, info in overused_words.items():
                print(f"   - '{word}': {info['count']} times ({info['frequency_per_1000']:.1f}/1000)")
        
        print("✅ Quality assurance test completed\n")
        
    finally:
        db.close()

def test_global_word_tracker():
    """Test the global word tracker."""
    print("🧪 Testing Global Word Tracker...")
    
    # Test multiple job tracking
    job1_id = "test-job-001"
    job2_id = "test-job-002"
    
    # Get trackers for different jobs
    tracker1 = GlobalWordTracker.get_tracker(job1_id)
    tracker2 = GlobalWordTracker.get_tracker(job2_id)
    
    # Add content to each tracker
    tracker1.update_word_counts("This is a beautiful tapestry of life experiences.")
    tracker2.update_word_counts("Her journey was like a colorful mosaic of memories.")
    
    # Check active trackers
    active_trackers = GlobalWordTracker.get_active_trackers()
    print(f"   Active trackers: {len(active_trackers)}")
    print(f"   Tracker IDs: {active_trackers}")
    
    # Test tracker removal
    GlobalWordTracker.remove_tracker(job1_id)
    active_trackers_after = GlobalWordTracker.get_active_trackers()
    print(f"   Active trackers after removal: {len(active_trackers_after)}")
    
    # Clean up
    GlobalWordTracker.remove_tracker(job2_id)
    
    print("✅ Global word tracker test completed\n")

async def main():
    """Run all tests."""
    print("🚀 Starting Quality Improvement Tests\n")
    
    # Test word tracking
    test_word_tracking()
    
    # Test global word tracker
    test_global_word_tracker()
    
    # Test quality assurance (requires database)
    try:
        await test_quality_assurance()
    except Exception as e:
        print(f"❌ Quality assurance test failed: {e}")
        print("   (This might be expected if database is not available)")
    
    print("🎉 All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
