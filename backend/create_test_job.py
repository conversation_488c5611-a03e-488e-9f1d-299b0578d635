#!/usr/bin/env python3
"""
Create a test biography job for Enhanced Iterative Service testing.
"""

import os
from datetime import datetime
from app.models.base import SessionLocal
from app.models.biography_job import BiographyJob, JobStatus

def create_test_job():
    """Create a test biography job with sample data."""
    
    print("📝 Creating test biography job...")
    
    # Use existing PDF from the repository
    test_pdf_path = "biography_249e2a1f-8805-4ce9-be8c-bea33f7473ed.pdf"
    if not os.path.exists(test_pdf_path):
        # Try in parent directory
        test_pdf_path = "../biography_249e2a1f-8805-4ce9-be8c-bea33f7473ed.pdf"
        if not os.path.exists(test_pdf_path):
            print("❌ Test PDF not found!")
            print("Please ensure biography_249e2a1f-8805-4ce9-be8c-bea33f7473ed.pdf exists")
            return None
    
    with SessionLocal() as db:
        # Check if we already have pending jobs
        existing_pending = db.query(BiographyJob).filter(BiographyJob.status == JobStatus.PENDING).first()
        
        if existing_pending:
            print(f"✅ Using existing pending job: {existing_pending.id}")
            print(f"   User: {existing_pending.user_name}")
            return existing_pending.id
        
        # Create new test job
        test_job = BiographyJob(
            user_name="Test User for Enhanced Iterative",
            email="<EMAIL>",
            uploaded_pdf_path=test_pdf_path,
            status=JobStatus.PENDING,
            progress_percentage=0.0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        db.add(test_job)
        db.commit()
        db.refresh(test_job)
        
        print(f"✅ Created test job: {test_job.id}")
        print(f"   User: {test_job.user_name}")
        print(f"   PDF: {test_job.uploaded_pdf_path}")
        
        return test_job.id

if __name__ == "__main__":
    job_id = create_test_job()
    if job_id:
        print(f"\n🎯 Test job ready: {job_id}")
        print(f"You can now run: python test_enhanced_iterative_sqs.py")
    else:
        print("\n❌ Failed to create test job") 