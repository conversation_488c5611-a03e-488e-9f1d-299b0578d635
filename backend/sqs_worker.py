#!/usr/bin/env python3
"""
SQS Worker for processing biography generation jobs.
This replaces the Celery worker functionality.
"""

import asyncio
import signal
import sys
import logging
import json
from datetime import datetime
from typing import Dict, List

from app.core.config import settings
from app.services.sqs_service import sqs_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('sqs_worker.log')
    ]
)
logger = logging.getLogger(__name__)

class SQSWorker:
    """SQS Worker for processing biography generation jobs."""
    
    def __init__(self):
        self.running = False
        self.tasks = set()
        self.shutdown_event = asyncio.Event()
        
    async def start(self):
        """Start the SQS worker."""
        self.running = True
        logger.info("Starting SQS Worker...")
        
        # Setup signal handlers for graceful shutdown using asyncio
        if sys.platform != 'win32':
            loop = asyncio.get_event_loop()
            for sig in (signal.SIGTERM, signal.SIGINT):
                loop.add_signal_handler(sig, self._handle_shutdown)
        
        try:
            await self._worker_loop()
        except Exception as e:
            logger.error(f"Worker crashed: {e}")
            raise
        finally:
            await self._cleanup()
    
    def _handle_shutdown(self):
        """Handle shutdown signals asynchronously."""
        logger.info("Received shutdown signal, initiating graceful shutdown...")
        self.running = False
        self.shutdown_event.set()
    
    async def _worker_loop(self):
        """Main worker loop to process SQS messages."""
        logger.info("Worker started, waiting for messages...")
        
        while self.running:
            try:
                # Use shorter polling time for better responsiveness to shutdown signals
                wait_time = 5  # Reduced from 20 to 5 seconds
                
                # Create a task for receiving messages
                receive_task = asyncio.create_task(
                    sqs_service.receive_messages(
                        max_messages=10,
                        wait_time=wait_time
                    )
                )
                
                # Wait for either messages or shutdown signal
                done, pending = await asyncio.wait(
                    [receive_task, asyncio.create_task(self.shutdown_event.wait())],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # If shutdown was signaled, cancel pending operations
                if self.shutdown_event.is_set():
                    if receive_task in pending:
                        receive_task.cancel()
                        try:
                            await receive_task
                        except asyncio.CancelledError:
                            pass
                    break
                
                # Get messages if the receive task completed
                if receive_task in done:
                    messages = await receive_task
                else:
                    # Cancel the receive task if shutdown was signaled
                    receive_task.cancel()
                    try:
                        await receive_task
                    except asyncio.CancelledError:
                        pass
                    continue
                
                if not messages:
                    continue  # No messages, continue polling
                
                # Process messages concurrently
                tasks = []
                for message in messages:
                    if not self.running:  # Check if we should stop
                        break
                    task = asyncio.create_task(self._process_message(message))
                    tasks.append(task)
                    self.tasks.add(task)
                
                # Wait for all messages to be processed or shutdown signal
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
                    
            except asyncio.CancelledError:
                logger.info("Worker loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in worker loop: {e}")
                if self.running:  # Only sleep if we're still supposed to be running
                    await asyncio.sleep(1)  # Reduced sleep time
    
    async def _process_message(self, message: Dict):
        """Process a single SQS message."""
        receipt_handle = message.get('ReceiptHandle')
        message_id = message.get('MessageId')
        
        try:
            # Check if we should stop processing
            if not self.running:
                logger.info(f"Skipping message {message_id} due to shutdown")
                return
                
            logger.info(f"Processing message {message_id}")
            
            # Parse message body
            body = json.loads(message['Body'])
            job_id = body.get('job_id')
            retry_count = body.get('retry_count', 0)
            
            # Process the message
            success = await sqs_service.process_message(message)
            
            if success:
                # Delete message from queue on successful processing
                await sqs_service.delete_message(receipt_handle)
                logger.info(f"Successfully processed and deleted message {message_id} for job {job_id}")
            else:
                # Handle failed processing
                await self._handle_failed_message(message, body, retry_count)
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse message {message_id}: {e}")
            # Delete malformed messages
            await sqs_service.delete_message(receipt_handle)
            
        except Exception as e:
            logger.error(f"Unexpected error processing message {message_id}: {e}")
            # Handle unexpected errors
            try:
                body = json.loads(message['Body'])
                await self._handle_failed_message(message, body, body.get('retry_count', 0))
            except:
                # If we can't even parse the message, delete it
                await sqs_service.delete_message(receipt_handle)
        
        finally:
            # Remove task from tracking set
            current_task = asyncio.current_task()
            self.tasks.discard(current_task)
    
    async def _handle_failed_message(self, message: Dict, body: Dict, retry_count: int):
        """Handle failed message processing with retry logic."""
        receipt_handle = message.get('ReceiptHandle')
        message_id = message.get('MessageId')
        job_id = body.get('job_id', 'unknown')
        
        if retry_count < settings.max_retries:
            # Increment retry count and put back in queue
            body['retry_count'] = retry_count + 1
            body['last_retry_at'] = datetime.utcnow().isoformat()
            
            logger.info(f"Retrying message {message_id} for job {job_id} (attempt {retry_count + 1}/{settings.max_retries})")
            
            # Send updated message back to queue
            success = await sqs_service.send_biography_job(
                job_id=job_id,
                use_iterative=body.get('use_iterative', True),
                force_enhanced_iterative=body.get('force_enhanced_iterative', False)
            )
            
            if success:
                # Delete original message after successful requeue
                await sqs_service.delete_message(receipt_handle)
            else:
                logger.error(f"Failed to requeue message {message_id}")
        else:
            # Max retries exceeded, send to DLQ
            logger.error(f"Max retries exceeded for message {message_id}, job {job_id}. Sending to DLQ.")
            
            await sqs_service.send_to_dlq(
                original_message=body,
                error_reason=f"Max retries ({settings.max_retries}) exceeded"
            )
            
            # Delete original message
            await sqs_service.delete_message(receipt_handle)
    
    async def _cleanup(self):
        """Cleanup before shutdown."""
        logger.info("Cleaning up worker...")
        
        if self.tasks:
            logger.info(f"Waiting for {len(self.tasks)} running tasks to complete...")
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        logger.info("Worker cleanup complete")

async def main():
    """Main entry point for the SQS worker."""
    if not settings.sqs_queue_url:
        logger.error("SQS_QUEUE_URL not configured. Please set it in your environment.")
        sys.exit(1)
    
    logger.info(f"Starting SQS Worker with queue: {settings.sqs_queue_url}")
    logger.info(f"AWS Region: {settings.aws_region}")
    logger.info(f"Max retries: {settings.max_retries}")
    
    worker = SQSWorker()
    
    # For Windows compatibility, handle KeyboardInterrupt
    if sys.platform == 'win32':
        try:
            await worker.start()
        except KeyboardInterrupt:
            logger.info("Worker interrupted by user (Ctrl+C)")
            worker._handle_shutdown()
            await worker._cleanup()
    else:
        try:
            await worker.start()
        except KeyboardInterrupt:
            logger.info("Worker interrupted by user (Ctrl+C)")
        except Exception as e:
            logger.error(f"Worker failed: {e}")
            sys.exit(1)
    
    logger.info("Worker shutdown complete")

def run_worker():
    """Entry point that handles asyncio event loop setup with proper signal handling."""
    if sys.platform == 'win32':
        # On Windows, use ProactorEventLoop for better signal handling
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Worker interrupted during startup")
        pass  # Graceful exit

if __name__ == "__main__":
    run_worker() 