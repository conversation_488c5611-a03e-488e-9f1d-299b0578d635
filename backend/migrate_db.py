#!/usr/bin/env python3
"""
Database migration script to add new fields for iterative biography generation.
Run this script to update your existing database with the new schema.
"""

import sqlite3
import json
import os

def migrate_database():
    """Migrate the database to support iterative generation."""
    
    # Database path
    db_path = "biography_generator.db"
    
    if not os.path.exists(db_path):
        print(f"Database {db_path} not found. Please ensure the database exists.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting database migration...")
        
        # Check if new columns already exist
        cursor.execute("PRAGMA table_info(biography_jobs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        new_columns = [
            ('chapters_content', 'TEXT'),
            ('chapter_theses', 'TEXT'),
            ('generation_iterations', 'TEXT'),
            ('total_chapters', 'INTEGER DEFAULT 0'),
            ('completed_chapters', 'INTEGER DEFAULT 0')
        ]
        
        # Add new columns if they don't exist
        for column_name, column_type in new_columns:
            if column_name not in columns:
                print(f"Adding column: {column_name}")
                cursor.execute(f"ALTER TABLE biography_jobs ADD COLUMN {column_name} {column_type}")
            else:
                print(f"Column {column_name} already exists, skipping...")
        
        # Check if new status exists in JobStatus enum
        cursor.execute("SELECT DISTINCT status FROM biography_jobs")
        existing_statuses = [row[0] for row in cursor.fetchall()]
        
        if 'iterative_writing' not in existing_statuses:
            print("New status 'iterative_writing' will be available for new jobs.")
        
        conn.commit()
        print("Database migration completed successfully!")
        
        # Verify migration
        cursor.execute("PRAGMA table_info(biography_jobs)")
        updated_columns = [column[1] for column in cursor.fetchall()]
        print(f"Updated table now has {len(updated_columns)} columns:")
        for col in updated_columns:
            print(f"  - {col}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Migration failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = migrate_database()
    if success:
        print("\n✅ Migration completed successfully!")
        print("You can now use the iterative biography generation feature.")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above.") 