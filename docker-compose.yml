version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://postgres:<EMAIL>/biodb
      - SQS_QUEUE_URL=${SQS_QUEUE_URL}
      - SQS_DLQ_QUEUE_URL=${SQS_DLQ_QUEUE_URL}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-eu-north-1}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - AI_PROVIDER=${AI_PROVIDER:-openai}
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/output:/app/output
    restart: unless-stopped
  
  # SQS Worker service for processing background jobs
  sqs-worker:
    build: ./backend
    command: python sqs_worker.py
    environment:
      - DATABASE_URL=postgresql://postgres:<EMAIL>/biodb
      - SQS_QUEUE_URL=${SQS_QUEUE_URL}
      - SQS_DLQ_QUEUE_URL=${SQS_DLQ_QUEUE_URL}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-eu-north-1}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - AI_PROVIDER=${AI_PROVIDER:-openai}
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/output:/app/output
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped

# Redis service removed - now using Amazon SQS

  # Optional: PostgreSQL for production
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=biography_generator
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data: 