# API Documentation

## Base URL
- Development: `http://localhost:8000`
- Production: `https://your-domain.com`

## Authentication
Currently, the API does not require authentication. For production use, implement JWT or OAuth2.

## Endpoints

### Health Check

#### GET /health
Check if the API is running.

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0"
}
```

### Biography Jobs

#### POST /api/biography/upload
Upload an interview PDF and create a new biography generation job.

**Parameters:**
- `user_name` (form-data, required): Name of the person
- `email` (form-data, optional): Email address
- `file` (form-data, required): PDF file (max 50MB)

**Response:**
```json
{
  "id": 1,
  "user_name": "<PERSON>",
  "email": "<EMAIL>",
  "uploaded_pdf_path": "./uploads/1234567890_interview.pdf",
  "output_pdf_path": null,
  "status": "pending",
  "progress_percentage": 0.0,
  "error_message": null,
  "created_at": "2024-01-01T12:00:00Z"
}
```

#### POST /api/biography/process/{job_id}
Start processing a biography generation job.

**Parameters:**
- `job_id` (path, required): Job ID

**Response:**
```json
{
  "message": "Biography generated successfully",
  "job_id": 1
}
```

#### GET /api/biography/jobs
Get all biography generation jobs.

**Response:**
```json
[
  {
    "id": 1,
    "user_name": "John Doe",
    "status": "completed",
    "progress_percentage": 100.0,
    "created_at": "2024-01-01T12:00:00Z",
    "completed_at": "2024-01-01T12:15:00Z"
  }
]
```

#### GET /api/biography/jobs/{job_id}
Get a specific biography generation job.

**Parameters:**
- `job_id` (path, required): Job ID

**Response:**
```json
{
  "id": 1,
  "user_name": "John Doe",
  "email": "<EMAIL>",
  "uploaded_pdf_path": "./uploads/1234567890_interview.pdf",
  "output_pdf_path": "./output/biography_1_1234567890.pdf",
  "status": "completed",
  "progress_percentage": 100.0,
  "outline_content": "Chapter 1: Early Life...",
  "biography_content": "John Doe was born...",
  "evaluation_content": "The biography shows excellent...",
  "final_biography_content": "Revised version of the biography...",
  "created_at": "2024-01-01T12:00:00Z",
  "completed_at": "2024-01-01T12:15:00Z",
  "processing_time_seconds": 900.5
}
```

#### GET /api/biography/jobs/{job_id}/download
Download the generated biography PDF.

**Parameters:**
- `job_id` (path, required): Job ID

**Response:** PDF file download

#### DELETE /api/biography/jobs/{job_id}
Delete a biography generation job and its files.

**Parameters:**
- `job_id` (path, required): Job ID

**Response:**
```json
{
  "message": "Job deleted successfully"
}
```

### AI Prompts

#### GET /api/prompts/
Get all active agent prompts.

**Response:**
```json
[
  {
    "id": 1,
    "agent_name": "outline",
    "prompt_content": "You are the Biography Outliner...",
    "is_active": true,
    "created_at": "2024-01-01T12:00:00Z",
    "version": 1
  }
]
```

#### GET /api/prompts/{agent_name}
Get prompt for a specific agent.

**Parameters:**
- `agent_name` (path, required): Agent name (outline, writer, evaluation, rewrite)

**Response:**
```json
{
  "id": 1,
  "agent_name": "outline",
  "prompt_content": "You are the Biography Outliner...",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z",
  "version": 1
}
```

#### POST /api/prompts/
Create a new agent prompt.

**Request Body:**
```json
{
  "agent_name": "outline",
  "prompt_content": "You are the Biography Outliner..."
}
```

**Response:**
```json
{
  "id": 1,
  "agent_name": "outline",
  "prompt_content": "You are the Biography Outliner...",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z",
  "version": 1
}
```

#### PUT /api/prompts/{prompt_id}
Update an existing agent prompt.

**Parameters:**
- `prompt_id` (path, required): Prompt ID

**Request Body:**
```json
{
  "prompt_content": "Updated prompt content...",
  "is_active": true
}
```

**Response:**
```json
{
  "id": 1,
  "agent_name": "outline",
  "prompt_content": "Updated prompt content...",
  "is_active": true,
  "updated_at": "2024-01-01T12:30:00Z",
  "version": 2
}
```

#### DELETE /api/prompts/{prompt_id}
Delete an agent prompt.

**Parameters:**
- `prompt_id` (path, required): Prompt ID

**Response:**
```json
{
  "message": "Prompt deleted successfully"
}
```

#### POST /api/prompts/initialize-defaults
Initialize default prompts for all agents.

**Response:**
```json
{
  "message": "Initialized default prompts for: outline, writer, evaluation, rewrite"
}
```

## Job Status Flow

1. **pending** - Job created, waiting to start
2. **processing** - Job started, AI agents working
3. **outline_complete** - Outline agent finished
4. **writing_complete** - Writer agent finished
5. **evaluation_complete** - Evaluation agent finished
6. **rewrite_complete** - Rewrite agent finished
7. **generating_pdf** - Creating final PDF
8. **completed** - Job finished successfully
9. **failed** - Job failed with error

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200` - Success
- `400` - Bad Request (validation errors)
- `404` - Not Found
- `422` - Unprocessable Entity (invalid data)
- `500` - Internal Server Error

Error responses include a `detail` field:
```json
{
  "detail": "Error message description"
}
```

## Rate Limiting

No rate limiting is currently implemented. For production:
- Implement rate limiting per IP
- Add user-based quotas
- Monitor API usage patterns

## File Limits

- Maximum PDF file size: 50MB
- Supported file types: PDF only
- File retention: Configurable (default: keep all files)

## AI Processing

The system uses 4 AI agents in sequence:

1. **Outline Agent** - Creates biography structure
2. **Writer Agent** - Writes full biography
3. **Evaluation Agent** - Reviews and grades output
4. **Rewrite Agent** - Improves based on feedback

Processing time varies based on:
- PDF length and complexity
- AI provider response times
- Selected AI model capabilities

## Real-time Status Updates

### Server-Sent Events (SSE) - Recommended
**Endpoint**: `GET /api/biography/jobs/{job_id}/status-stream`

Real-time job status updates via Server-Sent Events. Автоматично закривається коли job завершується.

**Usage**:
```javascript
const eventSource = new EventSource(`http://localhost:8000/api/biography/jobs/${jobId}/status-stream`);

eventSource.onmessage = (event) => {
    const status = JSON.parse(event.data);
    console.log('Job status update:', status);
    
    // Update UI with new status
    updateJobStatus(status);
    
    // Connection automatically closes when job is completed/failed
};

eventSource.onerror = (error) => {
    console.error('SSE connection error:', error);
    eventSource.close();
};

// Optional: Manual cleanup
function stopListening() {
    eventSource.close();
}
```

**Response Format**:
```json
{
    "job_id": 1,
    "status": "processing",
    "progress_percentage": 45.0,
    "error_message": null,
    "updated_at": "2025-05-29T12:30:45",
    "completed_at": null,
    "has_output": false
}
```

**Benefits**:
- 🚀 **Zero polling**: No constant HTTP requests needed
- 🔄 **Auto-reconnect**: Browser automatically handles connection issues
- 📡 **Real-time**: Updates sent only when status actually changes
- 🔋 **Efficient**: Minimal server and client resources
- 📈 **Scalable**: Supports thousands of concurrent streams
- 🛠 **Simple**: Easy to implement and maintain
- ✅ **Stable**: Most reliable option for status updates

**Connection Lifecycle**:
1. Client opens SSE connection
2. Server sends updates only when job status changes
3. Connection automatically closes when job completes/fails
4. No manual connection management needed